# Language Translation Feature Setup

This document explains how to set up and use the language translation feature in the enterprise chat application.

## Overview

The translation feature allows users to translate their English text input into various languages in real-time. It supports both instant translation (when pressing space after a word) and auto-translation (after a 1.5-second delay).

## Features

- **Real-time Translation**: Translate text as you type
- **Instant Translation**: Press space after each word for immediate translation
- **Auto Translation**: Automatic translation after 1.5 seconds of inactivity
- **Multiple Languages**: Support for 80+ languages including Indian languages
- **Bootstrap UI**: Clean, white-themed interface using Bootstrap components
- **Non-intrusive**: Doesn't interfere with existing chat functionality

## Setup Instructions

### 1. Translation Server Setup

The translation feature requires a separate Node.js server running the Google Translate API. You can use the server from the `LanguageConvertFinal/language-converter/server` directory.

#### Prerequisites:
- Node.js installed
- Google Cloud Translation API key

#### Steps:
1. Navigate to the server directory:
   ```bash
   cd LanguageConvertFinal/language-converter/server
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file with your Google Translate API key:
   ```
   GOOGLE_TRANSLATE_API_KEY=your_api_key_here
   PORT=5000
   ```

4. Start the server:
   ```bash
   npm start
   ```

The server will run on `http://localhost:5000`

### 2. Configuration

The translation feature can be configured in `src/config/translation.js`:

```javascript
export const TRANSLATION_CONFIG = {
  BASE_URL: 'http://localhost:5000',  // Translation server URL
  ENDPOINTS: {
    LANGUAGES: '/api/languages',
    TRANSLATE: '/api/translate',
    HEALTH: '/api/health'
  },
  SETTINGS: {
    DEBOUNCE_DELAY: 1500,           // Auto-translation delay (ms)
    AUTO_TRANSLATE: true,           // Enable auto-translation
    INSTANT_TRANSLATE_ON_SPACE: true // Enable space-key translation
  }
};
```

## How to Use

### 1. Enable Translation

1. In the chat input area, look for the language dropdown button (with language icon) next to the formatting buttons
2. Click the dropdown to see available languages
3. Select a language to enable translation
4. The button will turn blue when translation is active

### 2. Translation Methods

**Instant Translation (Recommended):**
- Type a word in English
- Press the space bar
- The word will be instantly translated to the selected language

**Auto Translation:**
- Type a word in English
- Wait 1.5 seconds without typing
- The word will be automatically translated

### 3. Disable Translation

- Click the language dropdown
- Select "Disable Translation"
- The feature will be turned off

## Supported Languages

The feature supports 80+ languages including:

**Indian Languages:**
- Hindi, Bengali, Telugu, Marathi, Tamil, Urdu, Gujarati, Kannada, Malayalam, Punjabi, Odia, Assamese, and more

**International Languages:**
- Spanish, French, German, Italian, Portuguese, Russian, Japanese, Korean, Chinese, Arabic, and more

## Technical Implementation

### Components Modified:

1. **InputBar.jsx**: Main component with translation logic
2. **Input.jsx**: Text input component with translation event handlers
3. **translation.js**: Configuration file

### Key Features:

- **Debounced Translation**: Prevents excessive API calls
- **Cursor Position Management**: Maintains cursor position after translation
- **Error Handling**: Graceful error handling with user feedback
- **State Management**: Proper React state management for translation status
- **Bootstrap Integration**: Uses Bootstrap components for consistent styling

## Troubleshooting

### Common Issues:

1. **Translation not working:**
   - Check if the translation server is running on port 5000
   - Verify the Google Translate API key is valid
   - Check browser console for error messages

2. **Languages not loading:**
   - Ensure the server is accessible at the configured URL
   - Check network connectivity

3. **Cursor jumping:**
   - This is handled automatically by the cursor position management system

### Error Messages:

- "Failed to load languages": Server connection issue
- "Translation failed": API error or network issue

## Notes

- The translation feature is completely optional and doesn't affect existing functionality
- All translations are processed server-side using Google Translate API
- The feature maintains the original chat application's performance and responsiveness
- Bootstrap classes are used for consistent styling with the existing application theme
