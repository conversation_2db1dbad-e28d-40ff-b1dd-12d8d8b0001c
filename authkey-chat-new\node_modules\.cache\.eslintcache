[{"D:\\DataGen\\authkey-chat-new\\src\\index.js": "1", "D:\\DataGen\\authkey-chat-new\\src\\App.js": "2", "D:\\DataGen\\authkey-chat-new\\src\\context\\ChatContext.js": "3", "D:\\DataGen\\authkey-chat-new\\src\\context\\AuthContext.js": "4", "D:\\DataGen\\authkey-chat-new\\src\\context\\AllProviders.js": "5", "D:\\DataGen\\authkey-chat-new\\src\\utils\\Utils.js": "6", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Login.jsx": "7", "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentManagementPage.jsx": "8", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Home.jsx": "9", "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentReportPage.jsx": "10", "D:\\DataGen\\authkey-chat-new\\src\\pages\\CreateAgentPage.jsx": "11", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Setting.jsx": "12", "D:\\DataGen\\authkey-chat-new\\src\\components\\VerifyAccount.jsx": "13", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\CustomerView.jsx": "14", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\Customers.jsx": "15", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Report\\Report.jsx": "16", "D:\\DataGen\\authkey-chat-new\\src\\components\\BroadcastReport\\BroadcastReport.jsx": "17", "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentReport\\AgentReport.jsx": "18", "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentSettings\\AgentSettings.jsx": "19", "D:\\DataGen\\authkey-chat-new\\src\\api\\api.js": "20", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\CreateNewPipeline.jsx": "21", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LeadsDashboard.jsx": "22", "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentDetails\\AgentDetailPage.jsx": "23", "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentBreakModal\\AgentBreakStatusCard.jsx": "24", "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerReport\\CustomerReport.jsx": "25", "D:\\DataGen\\authkey-chat-new\\src\\components\\LeftMenu.jsx": "26", "D:\\DataGen\\authkey-chat-new\\src\\components\\Navbar.jsx": "27", "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentManagement.jsx": "28", "D:\\DataGen\\authkey-chat-new\\src\\components\\Offline.jsx": "29", "D:\\DataGen\\authkey-chat-new\\src\\components\\Chat.jsx": "30", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useDebounce.js": "31", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useMaskNo.js": "32", "D:\\DataGen\\authkey-chat-new\\src\\components\\Sidebar.jsx": "33", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\ActivityLog.jsx": "34", "D:\\DataGen\\authkey-chat-new\\src\\components\\ui-conponents\\NewChatCall.jsx": "35", "D:\\DataGen\\authkey-chat-new\\src\\components\\Contactdetailcard\\ContactDetailCard.jsx": "36", "D:\\DataGen\\authkey-chat-new\\src\\components\\QuickReply\\QuickReply.jsx": "37", "D:\\DataGen\\authkey-chat-new\\src\\components\\faq\\FaqPanel.jsx": "38", "D:\\DataGen\\authkey-chat-new\\src\\components\\Notescard\\NotesCard.jsx": "39", "D:\\DataGen\\authkey-chat-new\\src\\components\\faq\\Faqsettings.jsx": "40", "D:\\DataGen\\authkey-chat-new\\src\\components\\Labels\\LabelCard.jsx": "41", "D:\\DataGen\\authkey-chat-new\\src\\components\\NewDashboard\\NewDashboard.jsx": "42", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LabelSelector.jsx": "43", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\StageCard.jsx": "44", "D:\\DataGen\\authkey-chat-new\\src\\components\\AutoReply\\AutoReplyRules.jsx": "45", "D:\\DataGen\\authkey-chat-new\\src\\components\\DeleteModal\\DeleteModal.jsx": "46", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LabelColumn.jsx": "47", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\ContactCard.jsx": "48", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\FilterSortBar.jsx": "49", "D:\\DataGen\\authkey-chat-new\\src\\components\\Broadcast\\Broadcast.jsx": "50", "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerReport\\AssignModal.jsx": "51", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useBlockUser.js": "52", "D:\\DataGen\\authkey-chat-new\\src\\components\\Messages.jsx": "53", "D:\\DataGen\\authkey-chat-new\\src\\components\\Search.jsx": "54", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useReminders.js": "55", "D:\\DataGen\\authkey-chat-new\\src\\components\\Welcome.jsx": "56", "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentBreakModal\\AgentBreakModal.jsx": "57", "D:\\DataGen\\authkey-chat-new\\src\\components\\Chats.jsx": "58", "D:\\DataGen\\authkey-chat-new\\src\\components\\ChangePassword.jsx": "59", "D:\\DataGen\\authkey-chat-new\\src\\components\\bookmark\\Bookmark.js": "60", "D:\\DataGen\\authkey-chat-new\\src\\components\\Labels\\Labels.jsx": "61", "D:\\DataGen\\authkey-chat-new\\src\\components\\Reminder\\Reminder.jsx": "62", "D:\\DataGen\\authkey-chat-new\\src\\components\\Reminder\\RemiderToast.jsx": "63", "D:\\DataGen\\authkey-chat-new\\src\\components\\profile\\UserProfile.jsx": "64", "D:\\DataGen\\authkey-chat-new\\src\\components\\InputBar\\Inputbar.jsx": "65", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\ContactActivityPopup.jsx": "66", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\ChartSection.jsx": "67", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\Table.jsx": "68", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\TodayStats.jsx": "69", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\OverviewCard.jsx": "70", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\ChatOverviewCard.jsx": "71", "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerHistory\\CustomerHistoryModal.jsx": "72", "D:\\DataGen\\authkey-chat-new\\src\\components\\TemplatePrev.jsx": "73", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useSentences.js": "74", "D:\\DataGen\\authkey-chat-new\\src\\components\\Message.jsx": "75", "D:\\DataGen\\authkey-chat-new\\src\\components\\BlockCard.jsx": "76", "D:\\DataGen\\authkey-chat-new\\src\\components\\Input.jsx": "77", "D:\\DataGen\\authkey-chat-new\\src\\components\\WaitingCard\\WaitingCard.jsx": "78", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReplyPreview\\ReplyPreview.jsx": "79", "D:\\DataGen\\authkey-chat-new\\src\\components\\SendTemplate.jsx": "80", "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\MessageCard.jsx": "81", "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\ReplyDropdown.jsx": "82", "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\OrderModal.jsx": "83", "D:\\DataGen\\authkey-chat-new\\src\\components\\MenuList\\MenuList.jsx": "84", "D:\\DataGen\\authkey-chat-new\\src\\config\\translation.js": "85"}, {"size": 772, "mtime": 1753168233939, "results": "86", "hashOfConfig": "87"}, {"size": 8668, "mtime": 1753168233729, "results": "88", "hashOfConfig": "87"}, {"size": 807, "mtime": 1753168233936, "results": "89", "hashOfConfig": "87"}, {"size": 719, "mtime": 1753168233936, "results": "90", "hashOfConfig": "87"}, {"size": 11215, "mtime": 1753168233936, "results": "91", "hashOfConfig": "87"}, {"size": 4276, "mtime": 1753168233951, "results": "92", "hashOfConfig": "87"}, {"size": 7124, "mtime": 1753168233948, "results": "93", "hashOfConfig": "87"}, {"size": 496, "mtime": 1753168233940, "results": "94", "hashOfConfig": "87"}, {"size": 8589, "mtime": 1753168233944, "results": "95", "hashOfConfig": "87"}, {"size": 11705, "mtime": 1753168233941, "results": "96", "hashOfConfig": "87"}, {"size": 13433, "mtime": 1753168233942, "results": "97", "hashOfConfig": "87"}, {"size": 36897, "mtime": 1753168233950, "results": "98", "hashOfConfig": "87"}, {"size": 18151, "mtime": 1753168233929, "results": "99", "hashOfConfig": "87"}, {"size": 11949, "mtime": 1753168233943, "results": "100", "hashOfConfig": "87"}, {"size": 34651, "mtime": 1753168233943, "results": "101", "hashOfConfig": "87"}, {"size": 7225, "mtime": 1753168233949, "results": "102", "hashOfConfig": "87"}, {"size": 8527, "mtime": 1753168233911, "results": "103", "hashOfConfig": "87"}, {"size": 5737, "mtime": 1753168233908, "results": "104", "hashOfConfig": "87"}, {"size": 7865, "mtime": 1753168233941, "results": "105", "hashOfConfig": "87"}, {"size": 670, "mtime": 1753168233732, "results": "106", "hashOfConfig": "87"}, {"size": 11153, "mtime": 1753168233946, "results": "107", "hashOfConfig": "87"}, {"size": 21545, "mtime": 1753168233947, "results": "108", "hashOfConfig": "87"}, {"size": 29861, "mtime": 1753168233940, "results": "109", "hashOfConfig": "87"}, {"size": 3983, "mtime": 1753168233907, "results": "110", "hashOfConfig": "87"}, {"size": 35976, "mtime": 1753168233915, "results": "111", "hashOfConfig": "87"}, {"size": 9016, "mtime": 1753168233918, "results": "112", "hashOfConfig": "87"}, {"size": 2426, "mtime": 1753168233921, "results": "113", "hashOfConfig": "87"}, {"size": 51998, "mtime": 1753168233908, "results": "114", "hashOfConfig": "87"}, {"size": 720, "mtime": 1753168233922, "results": "115", "hashOfConfig": "87"}, {"size": 19809, "mtime": 1753168233912, "results": "116", "hashOfConfig": "87"}, {"size": 422, "mtime": 1753168233937, "results": "117", "hashOfConfig": "87"}, {"size": 415, "mtime": 1753168233937, "results": "118", "hashOfConfig": "87"}, {"size": 47488, "mtime": 1753168233928, "results": "119", "hashOfConfig": "87"}, {"size": 3273, "mtime": 1753168233943, "results": "120", "hashOfConfig": "87"}, {"size": 5900, "mtime": 1753168233935, "results": "121", "hashOfConfig": "87"}, {"size": 15841, "mtime": 1753168233914, "results": "122", "hashOfConfig": "87"}, {"size": 23234, "mtime": 1753168233923, "results": "123", "hashOfConfig": "87"}, {"size": 4387, "mtime": 1753168233932, "results": "124", "hashOfConfig": "87"}, {"size": 12941, "mtime": 1753168233922, "results": "125", "hashOfConfig": "87"}, {"size": 14032, "mtime": 1753168233932, "results": "126", "hashOfConfig": "87"}, {"size": 1807, "mtime": 1753168233917, "results": "127", "hashOfConfig": "87"}, {"size": 10754, "mtime": 1753168233921, "results": "128", "hashOfConfig": "87"}, {"size": 1911, "mtime": 1753168233947, "results": "129", "hashOfConfig": "87"}, {"size": 1641, "mtime": 1753168233948, "results": "130", "hashOfConfig": "87"}, {"size": 8940, "mtime": 1753168233909, "results": "131", "hashOfConfig": "87"}, {"size": 894, "mtime": 1753168233916, "results": "132", "hashOfConfig": "87"}, {"size": 1864, "mtime": 1753168233946, "results": "133", "hashOfConfig": "87"}, {"size": 4796, "mtime": 1753168233945, "results": "134", "hashOfConfig": "87"}, {"size": 5430, "mtime": 1753168233946, "results": "135", "hashOfConfig": "87"}, {"size": 34730, "mtime": 1753168233910, "results": "136", "hashOfConfig": "87"}, {"size": 1411, "mtime": 1753168233915, "results": "137", "hashOfConfig": "87"}, {"size": 1863, "mtime": 1753168233937, "results": "138", "hashOfConfig": "87"}, {"size": 3571, "mtime": 1753168233921, "results": "139", "hashOfConfig": "87"}, {"size": 3661, "mtime": 1753168233927, "results": "140", "hashOfConfig": "87"}, {"size": 3345, "mtime": 1753168233938, "results": "141", "hashOfConfig": "87"}, {"size": 1092, "mtime": 1753168233930, "results": "142", "hashOfConfig": "87"}, {"size": 6060, "mtime": 1753168233907, "results": "143", "hashOfConfig": "87"}, {"size": 65894, "mtime": 1753168233913, "results": "144", "hashOfConfig": "87"}, {"size": 5114, "mtime": 1753168233911, "results": "145", "hashOfConfig": "87"}, {"size": 6474, "mtime": 1753168233932, "results": "146", "hashOfConfig": "87"}, {"size": 2511, "mtime": 1753168233917, "results": "147", "hashOfConfig": "87"}, {"size": 23187, "mtime": 1753168233924, "results": "148", "hashOfConfig": "87"}, {"size": 1727, "mtime": 1753168233923, "results": "149", "hashOfConfig": "87"}, {"size": 6231, "mtime": 1753168233933, "results": "150", "hashOfConfig": "87"}, {"size": 22712, "mtime": 1753177252246, "results": "151", "hashOfConfig": "87"}, {"size": 3229, "mtime": 1753168233945, "results": "152", "hashOfConfig": "87"}, {"size": 1903, "mtime": 1753168233926, "results": "153", "hashOfConfig": "87"}, {"size": 7548, "mtime": 1753168233927, "results": "154", "hashOfConfig": "87"}, {"size": 4672, "mtime": 1753168233927, "results": "155", "hashOfConfig": "87"}, {"size": 1749, "mtime": 1753168233926, "results": "156", "hashOfConfig": "87"}, {"size": 6574, "mtime": 1753168233926, "results": "157", "hashOfConfig": "87"}, {"size": 4590, "mtime": 1753168233914, "results": "158", "hashOfConfig": "87"}, {"size": 5836, "mtime": 1753168233929, "results": "159", "hashOfConfig": "87"}, {"size": 3091, "mtime": 1753168233938, "results": "160", "hashOfConfig": "87"}, {"size": 23211, "mtime": 1753168233919, "results": "161", "hashOfConfig": "87"}, {"size": 886, "mtime": 1753168233910, "results": "162", "hashOfConfig": "87"}, {"size": 41532, "mtime": 1753170946360, "results": "163", "hashOfConfig": "87"}, {"size": 5348, "mtime": 1753168233930, "results": "164", "hashOfConfig": "87"}, {"size": 6572, "mtime": 1753168233925, "results": "165", "hashOfConfig": "87"}, {"size": 14476, "mtime": 1753168233928, "results": "166", "hashOfConfig": "87"}, {"size": 15277, "mtime": 1753168233920, "results": "167", "hashOfConfig": "87"}, {"size": 519, "mtime": 1753168233920, "results": "168", "hashOfConfig": "87"}, {"size": 1780, "mtime": 1753168233920, "results": "169", "hashOfConfig": "87"}, {"size": 6056, "mtime": 1753168233919, "results": "170", "hashOfConfig": "87"}, {"size": 528, "mtime": 1753175305151, "results": "171", "hashOfConfig": "87"}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18psi8c", {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\DataGen\\authkey-chat-new\\src\\index.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\App.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\context\\ChatContext.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\context\\AuthContext.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\context\\AllProviders.js", ["427"], [], "D:\\DataGen\\authkey-chat-new\\src\\utils\\Utils.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Login.jsx", ["428"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentManagementPage.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Home.jsx", ["429", "430", "431"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentReportPage.jsx", ["432", "433", "434", "435", "436"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\CreateAgentPage.jsx", ["437", "438"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Setting.jsx", ["439", "440", "441", "442", "443", "444", "445", "446", "447", "448"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\VerifyAccount.jsx", ["449"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\CustomerView.jsx", ["450", "451", "452"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\Customers.jsx", ["453", "454", "455", "456", "457", "458", "459", "460"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Report\\Report.jsx", ["461", "462"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\BroadcastReport\\BroadcastReport.jsx", ["463", "464"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentReport\\AgentReport.jsx", ["465"], ["466"], "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentSettings\\AgentSettings.jsx", ["467", "468"], [], "D:\\DataGen\\authkey-chat-new\\src\\api\\api.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\CreateNewPipeline.jsx", ["469"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LeadsDashboard.jsx", ["470", "471", "472", "473", "474"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentDetails\\AgentDetailPage.jsx", ["475", "476", "477", "478", "479", "480", "481", "482", "483"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentBreakModal\\AgentBreakStatusCard.jsx", ["484"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerReport\\CustomerReport.jsx", ["485", "486", "487", "488", "489", "490", "491", "492", "493"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\LeftMenu.jsx", ["494"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Navbar.jsx", ["495"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentManagement.jsx", ["496", "497", "498", "499", "500", "501"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Offline.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Chat.jsx", ["502", "503", "504", "505", "506"], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useDebounce.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useMaskNo.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Sidebar.jsx", ["507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\ActivityLog.jsx", ["522"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ui-conponents\\NewChatCall.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Contactdetailcard\\ContactDetailCard.jsx", ["523"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\QuickReply\\QuickReply.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\faq\\FaqPanel.jsx", ["524"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Notescard\\NotesCard.jsx", ["525"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\faq\\Faqsettings.jsx", ["526", "527"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Labels\\LabelCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\NewDashboard\\NewDashboard.jsx", ["528"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LabelSelector.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\StageCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AutoReply\\AutoReplyRules.jsx", ["529"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\DeleteModal\\DeleteModal.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LabelColumn.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\ContactCard.jsx", ["530", "531", "532", "533"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\FilterSortBar.jsx", ["534", "535"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Broadcast\\Broadcast.jsx", ["536", "537", "538", "539", "540"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerReport\\AssignModal.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useBlockUser.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Messages.jsx", ["541"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Search.jsx", ["542"], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useReminders.js", ["543"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Welcome.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentBreakModal\\AgentBreakModal.jsx", ["544"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Chats.jsx", ["545", "546", "547", "548", "549", "550", "551", "552", "553"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ChangePassword.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\bookmark\\Bookmark.js", ["554"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Labels\\Labels.jsx", ["555", "556", "557", "558", "559", "560"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Reminder\\Reminder.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Reminder\\RemiderToast.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\profile\\UserProfile.jsx", ["561", "562", "563", "564", "565"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\InputBar\\Inputbar.jsx", ["566", "567", "568", "569", "570", "571", "572", "573"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\ContactActivityPopup.jsx", ["574"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\ChartSection.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\Table.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\TodayStats.jsx", ["575", "576", "577", "578", "579"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\OverviewCard.jsx", ["580"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\ChatOverviewCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerHistory\\CustomerHistoryModal.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\TemplatePrev.jsx", ["581"], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useSentences.js", ["582", "583", "584"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Message.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\BlockCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Input.jsx", ["585", "586", "587", "588", "589", "590", "591"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\WaitingCard\\WaitingCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReplyPreview\\ReplyPreview.jsx", ["592"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\SendTemplate.jsx", ["593"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\MessageCard.jsx", ["594", "595"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\ReplyDropdown.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\OrderModal.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\MenuList\\MenuList.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\config\\translation.js", [], [], {"ruleId": "596", "severity": 1, "message": "597", "line": 13, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 13, "endColumn": 17}, {"ruleId": "596", "severity": 1, "message": "600", "line": 1, "column": 17, "nodeType": "598", "messageId": "599", "endLine": 1, "endColumn": 26}, {"ruleId": "601", "severity": 1, "message": "602", "line": 60, "column": 6, "nodeType": "603", "endLine": 60, "endColumn": 19, "suggestions": "604"}, {"ruleId": "601", "severity": 1, "message": "605", "line": 101, "column": 6, "nodeType": "603", "endLine": 101, "endColumn": 27, "suggestions": "606"}, {"ruleId": "601", "severity": 1, "message": "607", "line": 166, "column": 5, "nodeType": "603", "endLine": 166, "endColumn": 73, "suggestions": "608"}, {"ruleId": "596", "severity": 1, "message": "609", "line": 9, "column": 8, "nodeType": "598", "messageId": "599", "endLine": 9, "endColumn": 14}, {"ruleId": "596", "severity": 1, "message": "610", "line": 33, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 33, "endColumn": 22}, {"ruleId": "601", "severity": 1, "message": "611", "line": 187, "column": 6, "nodeType": "603", "endLine": 187, "endColumn": 19, "suggestions": "612"}, {"ruleId": "601", "severity": 1, "message": "613", "line": 191, "column": 6, "nodeType": "603", "endLine": 191, "endColumn": 19, "suggestions": "614"}, {"ruleId": "601", "severity": 1, "message": "615", "line": 238, "column": 6, "nodeType": "603", "endLine": 238, "endColumn": 8, "suggestions": "616"}, {"ruleId": "596", "severity": 1, "message": "617", "line": 30, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 30, "endColumn": 21}, {"ruleId": "601", "severity": 1, "message": "618", "line": 35, "column": 6, "nodeType": "603", "endLine": 35, "endColumn": 8, "suggestions": "619"}, {"ruleId": "596", "severity": 1, "message": "620", "line": 17, "column": 26, "nodeType": "598", "messageId": "599", "endLine": 17, "endColumn": 43}, {"ruleId": "596", "severity": 1, "message": "621", "line": 43, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 43, "endColumn": 22}, {"ruleId": "596", "severity": 1, "message": "622", "line": 43, "column": 24, "nodeType": "598", "messageId": "599", "endLine": 43, "endColumn": 39}, {"ruleId": "596", "severity": 1, "message": "623", "line": 107, "column": 9, "nodeType": "598", "messageId": "599", "endLine": 107, "endColumn": 37}, {"ruleId": "596", "severity": 1, "message": "624", "line": 113, "column": 9, "nodeType": "598", "messageId": "599", "endLine": 113, "endColumn": 28}, {"ruleId": "596", "severity": 1, "message": "625", "line": 117, "column": 9, "nodeType": "598", "messageId": "599", "endLine": 117, "endColumn": 31}, {"ruleId": "601", "severity": 1, "message": "626", "line": 207, "column": 6, "nodeType": "603", "endLine": 207, "endColumn": 35, "suggestions": "627"}, {"ruleId": "601", "severity": 1, "message": "628", "line": 317, "column": 6, "nodeType": "603", "endLine": 317, "endColumn": 19, "suggestions": "629"}, {"ruleId": "596", "severity": 1, "message": "630", "line": 337, "column": 9, "nodeType": "598", "messageId": "599", "endLine": 337, "endColumn": 15}, {"ruleId": "596", "severity": 1, "message": "631", "line": 354, "column": 9, "nodeType": "598", "messageId": "599", "endLine": 354, "endColumn": 18}, {"ruleId": "601", "severity": 1, "message": "632", "line": 30, "column": 6, "nodeType": "603", "endLine": 30, "endColumn": 8, "suggestions": "633"}, {"ruleId": "596", "severity": 1, "message": "634", "line": 16, "column": 26, "nodeType": "598", "messageId": "599", "endLine": 16, "endColumn": 41}, {"ruleId": "596", "severity": 1, "message": "597", "line": 36, "column": 12, "nodeType": "598", "messageId": "599", "endLine": 36, "endColumn": 19}, {"ruleId": "601", "severity": 1, "message": "635", "line": 44, "column": 8, "nodeType": "603", "endLine": 44, "endColumn": 29, "suggestions": "636"}, {"ruleId": "596", "severity": 1, "message": "637", "line": 2, "column": 51, "nodeType": "598", "messageId": "599", "endLine": 2, "endColumn": 62}, {"ruleId": "596", "severity": 1, "message": "638", "line": 32, "column": 12, "nodeType": "598", "messageId": "599", "endLine": 32, "endColumn": 26}, {"ruleId": "601", "severity": 1, "message": "613", "line": 71, "column": 8, "nodeType": "603", "endLine": 71, "endColumn": 39, "suggestions": "639"}, {"ruleId": "601", "severity": 1, "message": "640", "line": 132, "column": 8, "nodeType": "603", "endLine": 132, "endColumn": 30, "suggestions": "641"}, {"ruleId": "596", "severity": 1, "message": "642", "line": 330, "column": 11, "nodeType": "598", "messageId": "599", "endLine": 330, "endColumn": 26}, {"ruleId": "596", "severity": 1, "message": "643", "line": 348, "column": 11, "nodeType": "598", "messageId": "599", "endLine": 348, "endColumn": 22}, {"ruleId": "596", "severity": 1, "message": "644", "line": 363, "column": 11, "nodeType": "598", "messageId": "599", "endLine": 363, "endColumn": 25}, {"ruleId": "596", "severity": 1, "message": "645", "line": 434, "column": 19, "nodeType": "598", "messageId": "599", "endLine": 434, "endColumn": 27}, {"ruleId": "596", "severity": 1, "message": "646", "line": 5, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 5, "endColumn": 20}, {"ruleId": "596", "severity": 1, "message": "647", "line": 14, "column": 11, "nodeType": "598", "messageId": "599", "endLine": 14, "endColumn": 22}, {"ruleId": "601", "severity": 1, "message": "648", "line": 29, "column": 8, "nodeType": "603", "endLine": 29, "endColumn": 21, "suggestions": "649"}, {"ruleId": "601", "severity": 1, "message": "650", "line": 32, "column": 8, "nodeType": "603", "endLine": 32, "endColumn": 42, "suggestions": "651"}, {"ruleId": "601", "severity": 1, "message": "652", "line": 28, "column": 6, "nodeType": "603", "endLine": 28, "endColumn": 19, "suggestions": "653"}, {"ruleId": "601", "severity": 1, "message": "654", "line": 33, "column": 6, "nodeType": "603", "endLine": 33, "endColumn": 40, "suggestions": "655", "suppressions": "656"}, {"ruleId": "596", "severity": 1, "message": "657", "line": 24, "column": 19, "nodeType": "598", "messageId": "599", "endLine": 24, "endColumn": 29}, {"ruleId": "601", "severity": 1, "message": "658", "line": 39, "column": 6, "nodeType": "603", "endLine": 39, "endColumn": 28, "suggestions": "659"}, {"ruleId": "596", "severity": 1, "message": "660", "line": 23, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 23, "endColumn": 26}, {"ruleId": "596", "severity": 1, "message": "661", "line": 14, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 14, "endColumn": 19}, {"ruleId": "662", "severity": 1, "message": "663", "line": 65, "column": 76, "nodeType": "664", "messageId": "665", "endLine": 65, "endColumn": 78}, {"ruleId": "666", "severity": 1, "message": "667", "line": 68, "column": 9, "nodeType": "668", "messageId": "669", "endLine": 74, "endColumn": 10}, {"ruleId": "601", "severity": 1, "message": "670", "line": 205, "column": 8, "nodeType": "603", "endLine": 205, "endColumn": 39, "suggestions": "671"}, {"ruleId": "601", "severity": 1, "message": "672", "line": 210, "column": 8, "nodeType": "603", "endLine": 210, "endColumn": 21, "suggestions": "673"}, {"ruleId": "596", "severity": 1, "message": "674", "line": 3, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 3, "endColumn": 14}, {"ruleId": "596", "severity": 1, "message": "675", "line": 5, "column": 5, "nodeType": "598", "messageId": "599", "endLine": 5, "endColumn": 15}, {"ruleId": "596", "severity": 1, "message": "676", "line": 7, "column": 5, "nodeType": "598", "messageId": "599", "endLine": 7, "endColumn": 12}, {"ruleId": "596", "severity": 1, "message": "677", "line": 22, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 22, "endColumn": 22}, {"ruleId": "596", "severity": 1, "message": "678", "line": 23, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 23, "endColumn": 14}, {"ruleId": "596", "severity": 1, "message": "660", "line": 24, "column": 18, "nodeType": "598", "messageId": "599", "endLine": 24, "endColumn": 34}, {"ruleId": "596", "severity": 1, "message": "679", "line": 53, "column": 11, "nodeType": "598", "messageId": "599", "endLine": 53, "endColumn": 16}, {"ruleId": "601", "severity": 1, "message": "680", "line": 104, "column": 8, "nodeType": "603", "endLine": 104, "endColumn": 48, "suggestions": "681"}, {"ruleId": "596", "severity": 1, "message": "682", "line": 123, "column": 11, "nodeType": "598", "messageId": "599", "endLine": 123, "endColumn": 21}, {"ruleId": "601", "severity": 1, "message": "683", "line": 50, "column": 6, "nodeType": "603", "endLine": 50, "endColumn": 17, "suggestions": "684"}, {"ruleId": "596", "severity": 1, "message": "685", "line": 3, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 3, "endColumn": 18}, {"ruleId": "596", "severity": 1, "message": "638", "line": 33, "column": 12, "nodeType": "598", "messageId": "599", "endLine": 33, "endColumn": 26}, {"ruleId": "596", "severity": 1, "message": "686", "line": 33, "column": 28, "nodeType": "598", "messageId": "599", "endLine": 33, "endColumn": 45}, {"ruleId": "596", "severity": 1, "message": "687", "line": 34, "column": 12, "nodeType": "598", "messageId": "599", "endLine": 34, "endColumn": 25}, {"ruleId": "596", "severity": 1, "message": "688", "line": 34, "column": 27, "nodeType": "598", "messageId": "599", "endLine": 34, "endColumn": 43}, {"ruleId": "596", "severity": 1, "message": "689", "line": 48, "column": 11, "nodeType": "598", "messageId": "599", "endLine": 48, "endColumn": 31}, {"ruleId": "601", "severity": 1, "message": "690", "line": 81, "column": 8, "nodeType": "603", "endLine": 81, "endColumn": 21, "suggestions": "691"}, {"ruleId": "601", "severity": 1, "message": "692", "line": 87, "column": 8, "nodeType": "603", "endLine": 87, "endColumn": 39, "suggestions": "693"}, {"ruleId": "596", "severity": 1, "message": "645", "line": 414, "column": 19, "nodeType": "598", "messageId": "599", "endLine": 414, "endColumn": 27}, {"ruleId": "596", "severity": 1, "message": "694", "line": 8, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 8, "endColumn": 24}, {"ruleId": "596", "severity": 1, "message": "695", "line": 6, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 6, "endColumn": 20}, {"ruleId": "596", "severity": 1, "message": "696", "line": 42, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 42, "endColumn": 23}, {"ruleId": "596", "severity": 1, "message": "617", "line": 44, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 44, "endColumn": 21}, {"ruleId": "601", "severity": 1, "message": "618", "line": 120, "column": 6, "nodeType": "603", "endLine": 120, "endColumn": 27, "suggestions": "697"}, {"ruleId": "601", "severity": 1, "message": "698", "line": 182, "column": 6, "nodeType": "603", "endLine": 182, "endColumn": 19, "suggestions": "699"}, {"ruleId": "596", "severity": 1, "message": "700", "line": 491, "column": 9, "nodeType": "598", "messageId": "599", "endLine": 491, "endColumn": 25}, {"ruleId": "596", "severity": 1, "message": "701", "line": 529, "column": 9, "nodeType": "598", "messageId": "599", "endLine": 529, "endColumn": 23}, {"ruleId": "596", "severity": 1, "message": "702", "line": 19, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 19, "endColumn": 15}, {"ruleId": "596", "severity": 1, "message": "703", "line": 24, "column": 24, "nodeType": "598", "messageId": "599", "endLine": 24, "endColumn": 34}, {"ruleId": "601", "severity": 1, "message": "704", "line": 60, "column": 6, "nodeType": "603", "endLine": 60, "endColumn": 21, "suggestions": "705"}, {"ruleId": "601", "severity": 1, "message": "706", "line": 149, "column": 6, "nodeType": "603", "endLine": 149, "endColumn": 8, "suggestions": "707"}, {"ruleId": "601", "severity": 1, "message": "708", "line": 176, "column": 6, "nodeType": "603", "endLine": 176, "endColumn": 41, "suggestions": "709"}, {"ruleId": "596", "severity": 1, "message": "694", "line": 22, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 22, "endColumn": 24}, {"ruleId": "596", "severity": 1, "message": "710", "line": 28, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 28, "endColumn": 18}, {"ruleId": "596", "severity": 1, "message": "711", "line": 29, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 29, "endColumn": 24}, {"ruleId": "596", "severity": 1, "message": "712", "line": 55, "column": 5, "nodeType": "598", "messageId": "599", "endLine": 55, "endColumn": 15}, {"ruleId": "596", "severity": 1, "message": "713", "line": 56, "column": 5, "nodeType": "598", "messageId": "599", "endLine": 56, "endColumn": 12}, {"ruleId": "596", "severity": 1, "message": "714", "line": 58, "column": 5, "nodeType": "598", "messageId": "599", "endLine": 58, "endColumn": 12}, {"ruleId": "596", "severity": 1, "message": "715", "line": 59, "column": 5, "nodeType": "598", "messageId": "599", "endLine": 59, "endColumn": 15}, {"ruleId": "596", "severity": 1, "message": "716", "line": 65, "column": 5, "nodeType": "598", "messageId": "599", "endLine": 65, "endColumn": 22}, {"ruleId": "596", "severity": 1, "message": "717", "line": 68, "column": 5, "nodeType": "598", "messageId": "599", "endLine": 68, "endColumn": 14}, {"ruleId": "596", "severity": 1, "message": "718", "line": 77, "column": 5, "nodeType": "598", "messageId": "599", "endLine": 77, "endColumn": 20}, {"ruleId": "596", "severity": 1, "message": "719", "line": 79, "column": 5, "nodeType": "598", "messageId": "599", "endLine": 79, "endColumn": 18}, {"ruleId": "601", "severity": 1, "message": "672", "line": 99, "column": 6, "nodeType": "603", "endLine": 99, "endColumn": 19, "suggestions": "720"}, {"ruleId": "601", "severity": 1, "message": "721", "line": 285, "column": 6, "nodeType": "603", "endLine": 285, "endColumn": 81, "suggestions": "722"}, {"ruleId": "596", "severity": 1, "message": "723", "line": 287, "column": 9, "nodeType": "598", "messageId": "599", "endLine": 287, "endColumn": 31}, {"ruleId": "596", "severity": 1, "message": "724", "line": 426, "column": 9, "nodeType": "598", "messageId": "599", "endLine": 426, "endColumn": 19}, {"ruleId": "725", "severity": 1, "message": "726", "line": 61, "column": 54, "nodeType": "727", "endLine": 61, "endColumn": 83}, {"ruleId": "725", "severity": 1, "message": "726", "line": 216, "column": 17, "nodeType": "727", "endLine": 216, "endColumn": 57}, {"ruleId": "596", "severity": 1, "message": "728", "line": 11, "column": 30, "nodeType": "598", "messageId": "599", "endLine": 11, "endColumn": 49}, {"ruleId": "596", "severity": 1, "message": "600", "line": 5, "column": 20, "nodeType": "598", "messageId": "599", "endLine": 5, "endColumn": 29}, {"ruleId": "596", "severity": 1, "message": "729", "line": 19, "column": 12, "nodeType": "598", "messageId": "599", "endLine": 19, "endColumn": 23}, {"ruleId": "596", "severity": 1, "message": "730", "line": 19, "column": 25, "nodeType": "598", "messageId": "599", "endLine": 19, "endColumn": 39}, {"ruleId": "596", "severity": 1, "message": "731", "line": 2, "column": 8, "nodeType": "598", "messageId": "599", "endLine": 2, "endColumn": 20}, {"ruleId": "596", "severity": 1, "message": "732", "line": 3, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 3, "endColumn": 13}, {"ruleId": "596", "severity": 1, "message": "600", "line": 1, "column": 35, "nodeType": "598", "messageId": "599", "endLine": 1, "endColumn": 44}, {"ruleId": "596", "severity": 1, "message": "733", "line": 8, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 8, "endColumn": 35}, {"ruleId": "596", "severity": 1, "message": "734", "line": 8, "column": 37, "nodeType": "598", "messageId": "599", "endLine": 8, "endColumn": 45}, {"ruleId": "596", "severity": 1, "message": "735", "line": 40, "column": 9, "nodeType": "598", "messageId": "599", "endLine": 40, "endColumn": 25}, {"ruleId": "596", "severity": 1, "message": "736", "line": 3, "column": 8, "nodeType": "598", "messageId": "599", "endLine": 3, "endColumn": 14}, {"ruleId": "596", "severity": 1, "message": "737", "line": 21, "column": 11, "nodeType": "598", "messageId": "599", "endLine": 21, "endColumn": 23}, {"ruleId": "596", "severity": 1, "message": "738", "line": 18, "column": 12, "nodeType": "598", "messageId": "599", "endLine": 18, "endColumn": 24}, {"ruleId": "596", "severity": 1, "message": "739", "line": 18, "column": 26, "nodeType": "598", "messageId": "599", "endLine": 18, "endColumn": 41}, {"ruleId": "596", "severity": 1, "message": "740", "line": 41, "column": 12, "nodeType": "598", "messageId": "599", "endLine": 41, "endColumn": 23}, {"ruleId": "596", "severity": 1, "message": "741", "line": 41, "column": 25, "nodeType": "598", "messageId": "599", "endLine": 41, "endColumn": 39}, {"ruleId": "601", "severity": 1, "message": "742", "line": 52, "column": 8, "nodeType": "603", "endLine": 52, "endColumn": 21, "suggestions": "743"}, {"ruleId": "596", "severity": 1, "message": "685", "line": 7, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 7, "endColumn": 18}, {"ruleId": "601", "severity": 1, "message": "640", "line": 28, "column": 6, "nodeType": "603", "endLine": 28, "endColumn": 18, "suggestions": "744"}, {"ruleId": "601", "severity": 1, "message": "745", "line": 54, "column": 8, "nodeType": "603", "endLine": 54, "endColumn": 54, "suggestions": "746"}, {"ruleId": "596", "severity": 1, "message": "600", "line": 1, "column": 27, "nodeType": "598", "messageId": "599", "endLine": 1, "endColumn": 36}, {"ruleId": "596", "severity": 1, "message": "747", "line": 12, "column": 8, "nodeType": "598", "messageId": "599", "endLine": 12, "endColumn": 19}, {"ruleId": "748", "severity": 1, "message": "749", "line": 16, "column": 16, "nodeType": "750", "messageId": "665", "endLine": 16, "endColumn": 19}, {"ruleId": "596", "severity": 1, "message": "751", "line": 27, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 27, "endColumn": 22}, {"ruleId": "596", "severity": 1, "message": "752", "line": 32, "column": 5, "nodeType": "598", "messageId": "599", "endLine": 32, "endColumn": 18}, {"ruleId": "596", "severity": 1, "message": "753", "line": 54, "column": 5, "nodeType": "598", "messageId": "599", "endLine": 54, "endColumn": 18}, {"ruleId": "596", "severity": 1, "message": "754", "line": 57, "column": 5, "nodeType": "598", "messageId": "599", "endLine": 57, "endColumn": 15}, {"ruleId": "601", "severity": 1, "message": "755", "line": 660, "column": 6, "nodeType": "603", "endLine": 660, "endColumn": 117, "suggestions": "756"}, {"ruleId": "601", "severity": 1, "message": "757", "line": 792, "column": 6, "nodeType": "603", "endLine": 792, "endColumn": 44, "suggestions": "758"}, {"ruleId": "601", "severity": 1, "message": "759", "line": 827, "column": 6, "nodeType": "603", "endLine": 827, "endColumn": 19, "suggestions": "760"}, {"ruleId": "596", "severity": 1, "message": "761", "line": 6, "column": 12, "nodeType": "598", "messageId": "599", "endLine": 6, "endColumn": 23}, {"ruleId": "596", "severity": 1, "message": "600", "line": 1, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 1, "endColumn": 19}, {"ruleId": "596", "severity": 1, "message": "762", "line": 1, "column": 31, "nodeType": "598", "messageId": "599", "endLine": 1, "endColumn": 41}, {"ruleId": "596", "severity": 1, "message": "763", "line": 3, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 3, "endColumn": 19}, {"ruleId": "596", "severity": 1, "message": "764", "line": 4, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 4, "endColumn": 21}, {"ruleId": "596", "severity": 1, "message": "765", "line": 7, "column": 20, "nodeType": "598", "messageId": "599", "endLine": 7, "endColumn": 26}, {"ruleId": "596", "severity": 1, "message": "766", "line": 10, "column": 12, "nodeType": "598", "messageId": "599", "endLine": 10, "endColumn": 25}, {"ruleId": "596", "severity": 1, "message": "767", "line": 6, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 6, "endColumn": 26}, {"ruleId": "596", "severity": 1, "message": "768", "line": 15, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 15, "endColumn": 22}, {"ruleId": "601", "severity": 1, "message": "769", "line": 32, "column": 6, "nodeType": "603", "endLine": 32, "endColumn": 19, "suggestions": "770"}, {"ruleId": "596", "severity": 1, "message": "771", "line": 33, "column": 9, "nodeType": "598", "messageId": "599", "endLine": 33, "endColumn": 18}, {"ruleId": "596", "severity": 1, "message": "772", "line": 98, "column": 9, "nodeType": "598", "messageId": "599", "endLine": 98, "endColumn": 27}, {"ruleId": "596", "severity": 1, "message": "773", "line": 124, "column": 15, "nodeType": "598", "messageId": "599", "endLine": 124, "endColumn": 19}, {"ruleId": "601", "severity": 1, "message": "774", "line": 143, "column": 6, "nodeType": "603", "endLine": 143, "endColumn": 25, "suggestions": "775"}, {"ruleId": "596", "severity": 1, "message": "776", "line": 264, "column": 13, "nodeType": "598", "messageId": "599", "endLine": 264, "endColumn": 27}, {"ruleId": "596", "severity": 1, "message": "777", "line": 265, "column": 13, "nodeType": "598", "messageId": "599", "endLine": 265, "endColumn": 25}, {"ruleId": "601", "severity": 1, "message": "778", "line": 332, "column": 6, "nodeType": "603", "endLine": 332, "endColumn": 59, "suggestions": "779"}, {"ruleId": "601", "severity": 1, "message": "780", "line": 335, "column": 9, "nodeType": "781", "endLine": 354, "endColumn": 4}, {"ruleId": "601", "severity": 1, "message": "782", "line": 357, "column": 9, "nodeType": "781", "endLine": 375, "endColumn": 4, "suggestions": "783"}, {"ruleId": "601", "severity": 1, "message": "784", "line": 456, "column": 6, "nodeType": "603", "endLine": 456, "endColumn": 68, "suggestions": "785"}, {"ruleId": "601", "severity": 1, "message": "786", "line": 51, "column": 5, "nodeType": "603", "endLine": 51, "endColumn": 24, "suggestions": "787"}, {"ruleId": "596", "severity": 1, "message": "788", "line": 1, "column": 56, "nodeType": "598", "messageId": "599", "endLine": 1, "endColumn": 64}, {"ruleId": "596", "severity": 1, "message": "789", "line": 1, "column": 66, "nodeType": "598", "messageId": "599", "endLine": 1, "endColumn": 69}, {"ruleId": "596", "severity": 1, "message": "790", "line": 1, "column": 71, "nodeType": "598", "messageId": "599", "endLine": 1, "endColumn": 75}, {"ruleId": "596", "severity": 1, "message": "791", "line": 42, "column": 11, "nodeType": "598", "messageId": "599", "endLine": 42, "endColumn": 23}, {"ruleId": "596", "severity": 1, "message": "792", "line": 45, "column": 11, "nodeType": "598", "messageId": "599", "endLine": 45, "endColumn": 17}, {"ruleId": "596", "severity": 1, "message": "793", "line": 6, "column": 8, "nodeType": "598", "messageId": "599", "endLine": 6, "endColumn": 11}, {"ruleId": "596", "severity": 1, "message": "773", "line": 55, "column": 13, "nodeType": "598", "messageId": "599", "endLine": 55, "endColumn": 17}, {"ruleId": "596", "severity": 1, "message": "794", "line": 1, "column": 8, "nodeType": "598", "messageId": "599", "endLine": 1, "endColumn": 13}, {"ruleId": "596", "severity": 1, "message": "795", "line": 2, "column": 8, "nodeType": "598", "messageId": "599", "endLine": 2, "endColumn": 13}, {"ruleId": "601", "severity": 1, "message": "796", "line": 48, "column": 8, "nodeType": "603", "endLine": 48, "endColumn": 28, "suggestions": "797"}, {"ruleId": "596", "severity": 1, "message": "798", "line": 8, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 8, "endColumn": 21}, {"ruleId": "596", "severity": 1, "message": "799", "line": 16, "column": 11, "nodeType": "598", "messageId": "599", "endLine": 16, "endColumn": 24}, {"ruleId": "596", "severity": 1, "message": "800", "line": 16, "column": 45, "nodeType": "598", "messageId": "599", "endLine": 16, "endColumn": 72}, {"ruleId": "596", "severity": 1, "message": "801", "line": 17, "column": 54, "nodeType": "598", "messageId": "599", "endLine": 17, "endColumn": 68}, {"ruleId": "596", "severity": 1, "message": "802", "line": 105, "column": 15, "nodeType": "598", "messageId": "599", "endLine": 105, "endColumn": 18}, {"ruleId": "601", "severity": 1, "message": "803", "line": 441, "column": 6, "nodeType": "603", "endLine": 441, "endColumn": 8, "suggestions": "804"}, {"ruleId": "601", "severity": 1, "message": "805", "line": 483, "column": 6, "nodeType": "603", "endLine": 483, "endColumn": 20, "suggestions": "806"}, {"ruleId": "807", "severity": 1, "message": "808", "line": 73, "column": 25, "nodeType": "727", "endLine": 73, "endColumn": 97}, {"ruleId": "601", "severity": 1, "message": "742", "line": 34, "column": 6, "nodeType": "603", "endLine": 34, "endColumn": 19, "suggestions": "809"}, {"ruleId": "596", "severity": 1, "message": "810", "line": 8, "column": 10, "nodeType": "598", "messageId": "599", "endLine": 8, "endColumn": 29}, {"ruleId": "596", "severity": 1, "message": "811", "line": 37, "column": 21, "nodeType": "598", "messageId": "599", "endLine": 37, "endColumn": 26}, "no-unused-vars", "'loading' is assigned a value but never used.", "Identifier", "unusedVar", "'useEffect' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'requestPermission'. Either include it or remove the dependency array.", "ArrayExpression", ["812"], "React Hook useEffect has a missing dependency: 'setCallData'. Either include it or remove the dependency array.", ["813"], "React Hook useCallback has unnecessary dependencies: 'showAllReminder' and 'showReminder'. Either exclude them or remove the dependency array.", ["814"], "'Navbar' is defined but never used.", "'chatsLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'graphReport'. Either include it or remove the dependency array.", ["815"], "React Hook useEffect has a missing dependency: 'fetchNewContactList'. Either include it or remove the dependency array.", ["816"], "React Hook useEffect has a missing dependency: 'getWhatsaAppNumberList'. Either include it or remove the dependency array.", ["817"], "'userBalance' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'Balance'. Either include it or remove the dependency array.", ["818"], "'setSelectedOption' is assigned a value but never used.", "'contactLists' is assigned a value but never used.", "'setContactLists' is assigned a value but never used.", "'handleManualAssignmentChange' is assigned a value but never used.", "'addManualAssignment' is assigned a value but never used.", "'removeManualAssignment' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAgent'. Either include it or remove the dependency array.", ["819"], "React Hook useEffect has missing dependencies: 'fetchAgents' and 'fetchTemplate'. Either include them or remove the dependency array.", ["820"], "'Option' is assigned a value but never used.", "'agentList' is defined but never used.", "React Hook useEffect has a missing dependency: 'intial_data'. Either include it or remove the dependency array.", ["821"], "'setCustomerData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCustomerData'. Either include it or remove the dependency array.", ["822"], "'useCallback' is defined but never used.", "'showDatePicker' is assigned a value but never used.", ["823"], "React Hook useEffect has a missing dependency: 'fetchSearch'. Either include it or remove the dependency array.", ["824"], "'handleDateClick' is assigned a value but never used.", "'clearFilter' is assigned a value but never used.", "'selectedOption' is assigned a value but never used.", "'nextPage' is assigned a value but never used.", "'SlCalender' is defined but never used.", "'currentUser' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchReportData', 'fromDate', and 'toDate'. Either include them or remove the dependency array.", ["825"], "React Hook useEffect has a missing dependency: 'fetchReportData'. Either include it or remove the dependency array.", ["826"], "React Hook useEffect has missing dependencies: 'fetchAgentData', 'fromDate', and 'toDate'. Either include them or remove the dependency array.", ["827"], "React Hook useEffect has a missing dependency: 'fetchAgentData'. Either include it or remove the dependency array.", ["828"], ["829"], "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchkeywords'. Either include it or remove the dependency array.", ["830"], "'CircularProgress' is defined but never used.", "'arrayMove' is defined but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "React Hook useEffect has a missing dependency: 'fetchContacts'. Either include it or remove the dependency array.", ["831"], "React Hook useEffect has a missing dependency: 'fetchAgents'. Either include it or remove the dependency array.", ["832"], "'Card' is defined but never used.", "'FaEnvelope' is defined but never used.", "'FaClock' is defined but never used.", "'IoMdSettings' is defined but never used.", "'Link' is defined but never used.", "'agent' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchActivityLog' and 'fetchAgentData'. Either include them or remove the dependency array.", ["833"], "'formatDate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'endBreak'. Either include it or remove the dependency array.", ["834"], "'BASE_URL' is defined but never used.", "'setShowDatePicker' is assigned a value but never used.", "'templatePopUp' is assigned a value but never used.", "'setTemplatePopUp' is assigned a value but never used.", "'debouncedSearchInput' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAgents', 'fetchNewContactList', 'fromDate', and 'toDate'. Either include them or remove the dependency array.", ["835"], "React Hook useEffect has missing dependencies: 'fetchNewContactList' and 'setFetchDataFn'. Either include them or remove the dependency array.", ["836"], "'MdSupportAgent' is defined but never used.", "'permission' is assigned a value but never used.", "'isExpiryClose' is assigned a value but never used.", ["837"], "React Hook useEffect has a missing dependency: 'showAgent'. Either include it or remove the dependency array.", ["838"], "'handleShowMobile' is assigned a value but never used.", "'handleNavigate' is assigned a value but never used.", "'toast' is defined but never used.", "'updateUser' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setIsOldMsg' and 'setRemainingTime'. Either include them or remove the dependency array.", ["839"], "React Hook useEffect has a missing dependency: 'setKeyBoardOpen'. Either include it or remove the dependency array. If 'setKeyBoardOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["840"], "React Hook useEffect has missing dependencies: 'selectedName' and 'setUserLabels'. Either include them or remove the dependency array. If 'setUserLabels' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["841"], "'BsFilter' is defined but never used.", "'AiOutlineClose' is defined but never used.", "'unReadChat' is assigned a value but never used.", "'setPage' is assigned a value but never used.", "'channel' is assigned a value but never used.", "'setChannel' is assigned a value but never used.", "'setRoleFilterType' is assigned a value but never used.", "'starChats' is assigned a value but never used.", "'setWaitingChats' is assigned a value but never used.", "'filteredChats' is assigned a value but never used.", ["842"], "React Hook useEffect has missing dependencies: 'fetchChats' and 'fetchStarredChats'. Either include them or remove the dependency array.", ["843"], "'getWhatsaAppNumberList' is assigned a value but never used.", "'waitingBtn' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'setSelectedCategory' is assigned a value but never used.", "'deleteIndex' is assigned a value but never used.", "'setDeleteIndex' is assigned a value but never used.", "'OverviewCard' is defined but never used.", "'use' is defined but never used.", "'IoIosArrowDroprightCircle' is defined but never used.", "'IoMdMove' is defined but never used.", "'toggleActivities' is assigned a value but never used.", "'Select' is defined but never used.", "'labelOptions' is assigned a value but never used.", "'templateList' is assigned a value but never used.", "'setTemplateList' is assigned a value but never used.", "'countryCode' is assigned a value but never used.", "'setCountryCode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTemplate'. Either include it or remove the dependency array.", ["844"], ["845"], "React Hook useEffect has missing dependencies: 'currentUser.parent_id', 'currentUser.parent_token', and 'selectedMobileNumber'. Either include them or remove the dependency array.", ["846"], "'WaitingCard' is defined but never used.", "no-empty-pattern", "Unexpected empty object pattern.", "ObjectPattern", "'waitingChats' is assigned a value but never used.", "'setUnReadChat' is assigned a value but never used.", "'mobileVisible' is assigned a value but never used.", "'nextConvId' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'disable', 'selectedName', 'setAllChats', and 'setUnreadCount'. Either include them or remove the dependency array.", ["847"], "React Hook useEffect has missing dependencies: 'setAllChats', 'setChats', 'setConvPage', 'setSelectedMobileNumber', and 'setSelectedUserDetails'. Either include them or remove the dependency array.", ["848"], "React Hook useEffect has a missing dependency: 'fetchNumberVisibility'. Either include it or remove the dependency array.", ["849"], "'searchInput' is assigned a value but never used.", "'useContext' is defined but never used.", "'ChatState' is defined but never used.", "'AuthContext' is defined but never used.", "'Button' is defined but never used.", "'selected<PERSON><PERSON><PERSON>' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", "'activeStatus' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setWpProfile'. Either include it or remove the dependency array.", ["850"], "'IOSSwitch' is assigned a value but never used.", "'handleActiveStatus' is assigned a value but never used.", "'data' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'showTypingStatus'. Either include it or remove the dependency array.", ["851"], "'selectionStart' is assigned a value but never used.", "'selectionEnd' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'setText'. Either include it or remove the dependency array.", ["852"], "The 'findCompletedWord' function makes the dependencies of useCallback Hook (at line 423) change on every render. Move it inside the useCallback callback. Alternatively, wrap the definition of 'findCompletedWord' in its own useCallback() Hook.", "VariableDeclarator", "The 'findCurrentWord' function makes the dependencies of useEffect Hook (at line 480) change on every render. To fix this, wrap the definition of 'findCurrentWord' in its own useCallback() Hook.", ["853"], "React Hook useCallback has a missing dependency: 'DEBOUNCE_DELAY'. Either include it or remove the dependency array.", ["854"], "React Hook useEffect has a missing dependency: 'setActivities'. Either include it or remove the dependency array. If 'setActivities' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["855"], "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'pieChartData' is assigned a value but never used.", "'COLORS' is assigned a value but never used.", "'Box' is defined but never used.", "'React' is defined but never used.", "'axios' is defined but never used.", "React Hook useEffect has a missing dependency: 'localSentences'. Either include it or remove the dependency array.", ["856"], "'useDebounce' is defined but never used.", "'saveSentences' is assigned a value but never used.", "'splitParagraphIntoSentences' is assigned a value but never used.", "'setShowPreview' is assigned a value but never used.", "'msg' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'textareaRef'. Either include it or remove the dependency array.", ["857"], "React Hook useEffect has a missing dependency: 'currentUser'. Either include it or remove the dependency array.", ["858"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", ["859"], "'RiArrowDropDownLine' is defined but never used.", "'reply' is assigned a value but never used.", {"desc": "860", "fix": "861"}, {"desc": "862", "fix": "863"}, {"desc": "864", "fix": "865"}, {"desc": "866", "fix": "867"}, {"desc": "868", "fix": "869"}, {"desc": "870", "fix": "871"}, {"desc": "872", "fix": "873"}, {"desc": "874", "fix": "875"}, {"desc": "876", "fix": "877"}, {"desc": "878", "fix": "879"}, {"desc": "880", "fix": "881"}, {"desc": "882", "fix": "883"}, {"desc": "884", "fix": "885"}, {"desc": "886", "fix": "887"}, {"desc": "888", "fix": "889"}, {"desc": "890", "fix": "891"}, {"desc": "892", "fix": "893"}, {"kind": "894", "justification": "895"}, {"desc": "896", "fix": "897"}, {"desc": "898", "fix": "899"}, {"desc": "900", "fix": "901"}, {"desc": "902", "fix": "903"}, {"desc": "904", "fix": "905"}, {"desc": "906", "fix": "907"}, {"desc": "908", "fix": "909"}, {"desc": "910", "fix": "911"}, {"desc": "912", "fix": "913"}, {"desc": "914", "fix": "915"}, {"desc": "916", "fix": "917"}, {"desc": "918", "fix": "919"}, {"desc": "900", "fix": "920"}, {"desc": "921", "fix": "922"}, {"desc": "923", "fix": "924"}, {"desc": "925", "fix": "926"}, {"desc": "927", "fix": "928"}, {"desc": "929", "fix": "930"}, {"desc": "931", "fix": "932"}, {"desc": "933", "fix": "934"}, {"desc": "935", "fix": "936"}, {"desc": "937", "fix": "938"}, {"desc": "939", "fix": "940"}, {"desc": "941", "fix": "942"}, {"desc": "943", "fix": "944"}, {"desc": "945", "fix": "946"}, {"desc": "947", "fix": "948"}, {"desc": "949", "fix": "950"}, {"desc": "951", "fix": "952"}, {"desc": "923", "fix": "953"}, "Update the dependencies array to be: [currentUser, requestPermission]", {"range": "954", "text": "955"}, "Update the dependencies array to be: [currentUser, setCallData, socket]", {"range": "956", "text": "957"}, "Update the dependencies array to be: [setShowReminder, setShowAllReminder]", {"range": "958", "text": "959"}, "Update the dependencies array to be: [currentUser, graphReport]", {"range": "960", "text": "961"}, "Update the dependencies array to be: [currentUser, fetchNewContactList]", {"range": "962", "text": "963"}, "Update the dependencies array to be: [getWhatsaAppNumberList]", {"range": "964", "text": "965"}, "Update the dependencies array to be: [Balance]", {"range": "966", "text": "967"}, "Update the dependencies array to be: [currentUser, fetchAgent, selectedOption]", {"range": "968", "text": "969"}, "Update the dependencies array to be: [currentUser, fetchAgents, fetchTemplate]", {"range": "970", "text": "971"}, "Update the dependencies array to be: [intial_data]", {"range": "972", "text": "973"}, "Update the dependencies array to be: [currentUser, fetchCustomerData, mobile]", {"range": "974", "text": "975"}, "Update the dependencies array to be: [currentUser, fetchNewContactList, fromDate, toDate]", {"range": "976", "text": "977"}, "Update the dependencies array to be: [debouncedSearchInput, fetchSearch]", {"range": "978", "text": "979"}, "Update the dependencies array to be: [currentUser, fetchReportData, fromDate, toDate]", {"range": "980", "text": "981"}, "Update the dependencies array to be: [setFetchDataFn, fromDate, toDate, fetchReportData]", {"range": "982", "text": "983"}, "Update the dependencies array to be: [currentUser, fetchAgentData, fromDate, toDate]", {"range": "984", "text": "985"}, "Update the dependencies array to be: [setFetchDataFn, fromDate, toDate, fetchAgentData]", {"range": "986", "text": "987"}, "directive", "", "Update the dependencies array to be: [currentUser, agentId, fetchkeywords]", {"range": "988", "text": "989"}, "Update the dependencies array to be: [currentUser, fetchContacts, fromDate, toDate]", {"range": "990", "text": "991"}, "Update the dependencies array to be: [currentUser, fetchAgents]", {"range": "992", "text": "993"}, "Update the dependencies array to be: [currentUser, agentId, fromDate, toDate, fetchAgentData, fetchActivityLog]", {"range": "994", "text": "995"}, "Update the dependencies array to be: [breakInfo, endBreak]", {"range": "996", "text": "997"}, "Update the dependencies array to be: [currentUser, fetchAgents, fetchNewContactList, fromDate, toDate]", {"range": "998", "text": "999"}, "Update the dependencies array to be: [currentUser, fetchNewContactList, fromDate, setFetchDataFn, toDate]", {"range": "1000", "text": "1001"}, "Update the dependencies array to be: [Balance, currentUser, socket]", {"range": "1002", "text": "1003"}, "Update the dependencies array to be: [currentUser, showAgent]", {"range": "1004", "text": "1005"}, "Update the dependencies array to be: [remainingTime, setIsOldMsg, setRemainingTime]", {"range": "1006", "text": "1007"}, "Update the dependencies array to be: [setKeyBoardOpen]", {"range": "1008", "text": "1009"}, "Update the dependencies array to be: [currentUser, selectedMobileNumber, selectedName, setUserLabels]", {"range": "1010", "text": "1011"}, {"range": "1012", "text": "993"}, "Update the dependencies array to be: [currentUser, setChatsLoading, disable, chatCategory, chatFilterType.value, fetchStarredChats, fetchChats]", {"range": "1013", "text": "1014"}, "Update the dependencies array to be: [currentUser, fetchTemplate]", {"range": "1015", "text": "1016"}, "Update the dependencies array to be: [fetchSearch, inputValue]", {"range": "1017", "text": "1018"}, "Update the dependencies array to be: [fetchType, fetchAllParams, fetchMobileParams, currentUser.parent_id, currentUser.parent_token, selectedMobileNumber]", {"range": "1019", "text": "1020"}, "Update the dependencies array to be: [currentUser, selectedMobileNumber, allChats, dispatch, unReadChat, data, socket, chatFilterType, chatCategory, disable, selectedName, setAllChats, setUnreadCount]", {"range": "1021", "text": "1022"}, "Update the dependencies array to be: [currentUser, allChats, socket, chats, setAllChats, setChats, setSelectedMobileNumber, setSelectedUserDetails, setConvPage]", {"range": "1023", "text": "1024"}, "Update the dependencies array to be: [currentUser, fetchNumberVisibility]", {"range": "1025", "text": "1026"}, "Update the dependencies array to be: [currentUser, setWpProfile]", {"range": "1027", "text": "1028"}, "Update the dependencies array to be: [text, currentUser, showTypingStatus]", {"range": "1029", "text": "1030"}, "Update the dependencies array to be: [selectedLanguage, isTranslationEnabled, currentUser.parent_id, currentUser.parent_token, setText]", {"range": "1031", "text": "1032"}, "Wrap the definition of 'findCurrentWord' in its own useCallback() Hook.", {"range": "1033", "text": "1034"}, "Update the dependencies array to be: [isTranslationEnabled, DEBOUNCE_DELAY, translateWordSilently]", {"range": "1035", "text": "1036"}, "Update the dependencies array to be: [currentUser, phone, setActivities]", {"range": "1037", "text": "1038"}, "Update the dependencies array to be: [debouncedInputText, localSentences]", {"range": "1039", "text": "1040"}, "Update the dependencies array to be: [textareaRef]", {"range": "1041", "text": "1042"}, "Update the dependencies array to be: [text, socket, currentUser]", {"range": "1043", "text": "1044"}, {"range": "1045", "text": "1016"}, [2528, 2541], "[currentUser, requestPermission]", [3554, 3575], "[currentUser, setCallData, socket]", [6022, 6090], "[setShowReminder, setShowAllReminder]", [6046, 6059], "[current<PERSON><PERSON>, graphReport]", [6119, 6132], "[current<PERSON><PERSON>, fetchNewContactList]", [7436, 7438], "[getWhatsaAppNumberList]", [1065, 1067], "[Balance]", [6505, 6534], "[currentUser, fetchAgent, selectedOption]", [9719, 9732], "[currentUser, fetchAgents, fetchTemplate]", [1030, 1032], "[intial_data]", [1657, 1678], "[currentUser, fetchCustomerData, mobile]", [3033, 3064], "[current<PERSON><PERSON>, fetchNewContactList, fromDate, toDate]", [5434, 5456], "[debouncedSearchInput, fetchSearch]", [1085, 1098], "[currentUser, fetchReportData, fromDate, toDate]", [1199, 1233], "[setFetchDataFn, fromDate, toDate, fetchReportData]", [951, 964], "[currentUser, fetchAgentData, fromDate, toDate]", [1120, 1154], "[setFetchDataFn, fromDate, toDate, fetchAgentData]", [1369, 1391], "[currentUser, agentId, fetchkeywords]", [8271, 8302], "[currentUser, fetchContacts, fromDate, toDate]", [8363, 8376], "[current<PERSON><PERSON>, fetchAgents]", [3757, 3797], "[currentUser, agentId, fromDate, toDate, fetchAgentData, fetchActivityLog]", [1575, 1586], "[breakInfo, endBreak]", [3486, 3499], "[current<PERSON><PERSON>, fetchA<PERSON>s, fetchNewContactList, fromDate, toDate]", [3609, 3640], "[current<PERSON><PERSON>, fetchNewContactList, fromDate, setFetchDataFn, toDate]", [4176, 4197], "[Balance, currentUser, socket]", [5770, 5783], "[current<PERSON><PERSON>, showAgent]", [2262, 2277], "[remainingTime, setIsOldMsg, setRemainingTime]", [5115, 5117], "[setKeyBoardOpen]", [5843, 5878], "[current<PERSON><PERSON>, selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, selected<PERSON><PERSON>, setUserLabels]", [3450, 3463], [8686, 8761], "[currentUser, setChatsLoading, disable, chatCategory, chatFilterType.value, fetchStarredChats, fetchChats]", [2220, 2233], "[currentUser, fetchTemplate]", [770, 782], "[fetchSearch, inputValue]", [2221, 2267], "[fetchType, fetchAllParams, fetchMobileParams, currentUser.parent_id, currentUser.parent_token, selectedMobileNumber]", [22321, 22432], "[currentUser, selectedMobileNumber, allChats, dispatch, unReadChat, data, socket, chatFilterType, chatCategory, disable, selectedName, setAllChats, setUnreadCount]", [28281, 28319], "[currentUser, allChats, socket, chats, setAllChats, setChats, setSelectedMobileNumber, setSelectedUserDetails, setConvPage]", [29332, 29345], "[currentUser, fetchNumberVisibility]", [1291, 1304], "[current<PERSON><PERSON>, setWpProfile]", [4889, 4908], "[text, currentUser, showTypingStatus]", [11266, 11319], "[selectedLanguage, isTranslationEnabled, currentUser.parent_id, currentUser.parent_token, setText]", [12085, 12480], "useCallback((text, cursorPos) => {\r\n    let start = cursorPos;\r\n    let end = cursorPos;\r\n\r\n    // Find word boundaries\r\n    while (start > 0 && text[start - 1] !== ' ') {\r\n      start--;\r\n    }\r\n    while (end < text.length && text[end] !== ' ') {\r\n      end++;\r\n    }\r\n\r\n    const word = text.substring(start, end);\r\n    return {\r\n      word: word.trim(),\r\n      start: start,\r\n      end: end\r\n    };\r\n  })", [15025, 15087], "[isTranslationEnabled, DEBOUNCE_DELAY, translateWordSilently]", [1613, 1632], "[currentUser, phone, setActivities]", [1892, 1912], "[debouncedInputText, localSentences]", [13754, 13756], "[textareaRef]", [14742, 14756], "[text, socket, currentUser]", [1430, 1443]]