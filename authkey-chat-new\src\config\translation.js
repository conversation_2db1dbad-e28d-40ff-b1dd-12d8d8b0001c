// Translation service configuration
export const TRANSLATION_CONFIG = {
  // Base URL for the translation service
  BASE_URL: 'http://localhost:5000',
  
  // API endpoints
  ENDPOINTS: {
    LANGUAGES: '/api/languages',
    TRANSLATE: '/api/translate',
    HEALTH: '/api/health'
  },
  
  // Translation settings
  SETTINGS: {
    DEBOUNCE_DELAY: 1500, // milliseconds
    AUTO_TRANSLATE: true,
    INSTANT_TRANSLATE_ON_SPACE: true
  }
};

// Helper function to get full URL
export const getTranslationUrl = (endpoint) => {
  return `${TRANSLATION_CONFIG.BASE_URL}${endpoint}`;
};
