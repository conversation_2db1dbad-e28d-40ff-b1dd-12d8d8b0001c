/* Modal Backdrop */
.modalBackdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5); /* Semi-transparent background */
    z-index: 999; /* Ensure the modal is on top */
}

/* Modal Container */
.modalContainer {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    width: 400px;
    max-width: 90%; /* Make it responsive */
    padding: 20px;
}

/* Modal Header */
.modalHeader {
    display: flex;
    justify-content: flex-end;
}

.closeButton {
    background: transparent;
    border: none;
    font-size: 20px;
    cursor: pointer;
}

/* Modal Body */
.modalBody {
    padding: 20px 0;
}

/* Modal Footer */
.modalFooter {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Buttons */
.cancelButton, .saveButton {
    padding: 10px 20px;
    border: none;
    cursor: pointer;
    border-radius: 4px;
}

.cancelButton {
    background: #ccc;
}

.saveButton {
    background: #007bff;
    color: white;
}

.saveButton:disabled {
    background: #bbb;
    cursor: not-allowed;
}
