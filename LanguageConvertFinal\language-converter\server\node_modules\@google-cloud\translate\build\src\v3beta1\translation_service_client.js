"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranslationServiceClient = void 0;
const jsonProtos = require("../../protos/protos.json");
const google_gax_1 = require("google-gax");
/**
 * Client JSON configuration object, loaded from
 * `src/v3beta1/translation_service_client_config.json`.
 * This file defines retry strategy and timeouts for all API methods in this library.
 */
const gapicConfig = require("./translation_service_client_config.json");
const version = require('../../../package.json').version;
/**
 *  Provides natural language translation operations.
 * @class
 * @memberof v3beta1
 */
class TranslationServiceClient {
    _terminated = false;
    _opts;
    _providedCustomServicePath;
    _gaxModule;
    _gaxGrpc;
    _protos;
    _defaults;
    _universeDomain;
    _servicePath;
    _log = google_gax_1.loggingUtils.log('translate');
    auth;
    descriptors = {
        page: {},
        stream: {},
        longrunning: {},
        batching: {},
    };
    warn;
    innerApiCalls;
    iamClient;
    locationsClient;
    pathTemplates;
    operationsClient;
    translationServiceStub;
    /**
     * Construct an instance of TranslationServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://cloud.google.com/docs/authentication/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new TranslationServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts, gaxInstance) {
        // Ensure that options include all the required fields.
        const staticMembers = this.constructor;
        if (opts?.universe_domain && opts?.universeDomain && opts?.universe_domain !== opts?.universeDomain) {
            throw new Error('Please set either universe_domain or universeDomain, but not both.');
        }
        const universeDomainEnvVar = (typeof process === 'object' && typeof process.env === 'object') ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN'] : undefined;
        this._universeDomain = opts?.universeDomain ?? opts?.universe_domain ?? universeDomainEnvVar ?? 'googleapis.com';
        this._servicePath = 'translate.' + this._universeDomain;
        const servicePath = opts?.servicePath || opts?.apiEndpoint || this._servicePath;
        this._providedCustomServicePath = !!(opts?.servicePath || opts?.apiEndpoint);
        const port = opts?.port || staticMembers.port;
        const clientConfig = opts?.clientConfig ?? {};
        const fallback = opts?.fallback ?? (typeof window !== 'undefined' && typeof window?.fetch === 'function');
        opts = Object.assign({ servicePath, port, clientConfig, fallback }, opts);
        // Request numeric enum values if REST transport is used.
        opts.numericEnums = true;
        // If scopes are unset in options and we're connecting to a non-default endpoint, set scopes just in case.
        if (servicePath !== this._servicePath && !('scopes' in opts)) {
            opts['scopes'] = staticMembers.scopes;
        }
        // Load google-gax module synchronously if needed
        if (!gaxInstance) {
            gaxInstance = require('google-gax');
        }
        // Choose either gRPC or proto-over-HTTP implementation of google-gax.
        this._gaxModule = opts.fallback ? gaxInstance.fallback : gaxInstance;
        // Create a `gaxGrpc` object, with any grpc-specific options sent to the client.
        this._gaxGrpc = new this._gaxModule.GrpcClient(opts);
        // Save options to use in initialize() method.
        this._opts = opts;
        // Save the auth object to the client, for use by other methods.
        this.auth = this._gaxGrpc.auth;
        // Set useJWTAccessWithScope on the auth object.
        this.auth.useJWTAccessWithScope = true;
        // Set defaultServicePath on the auth object.
        this.auth.defaultServicePath = this._servicePath;
        // Set the default scopes in auth client if needed.
        if (servicePath === this._servicePath) {
            this.auth.defaultScopes = staticMembers.scopes;
        }
        this.iamClient = new this._gaxModule.IamClient(this._gaxGrpc, opts);
        this.locationsClient = new this._gaxModule.LocationsClient(this._gaxGrpc, opts);
        // Determine the client header string.
        const clientHeader = [
            `gax/${this._gaxModule.version}`,
            `gapic/${version}`,
        ];
        if (typeof process === 'object' && 'versions' in process) {
            clientHeader.push(`gl-node/${process.versions.node}`);
        }
        else {
            clientHeader.push(`gl-web/${this._gaxModule.version}`);
        }
        if (!opts.fallback) {
            clientHeader.push(`grpc/${this._gaxGrpc.grpcVersion}`);
        }
        else {
            clientHeader.push(`rest/${this._gaxGrpc.grpcVersion}`);
        }
        if (opts.libName && opts.libVersion) {
            clientHeader.push(`${opts.libName}/${opts.libVersion}`);
        }
        // Load the applicable protos.
        this._protos = this._gaxGrpc.loadProtoJSON(jsonProtos);
        // This API contains "path templates"; forward-slash-separated
        // identifiers to uniquely identify resources within the API.
        // Create useful helper objects for these.
        this.pathTemplates = {
            glossaryPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/glossaries/{glossary}'),
            locationPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}'),
        };
        // Some of the methods on this service return "paged" results,
        // (e.g. 50 results at a time, with tokens to get subsequent
        // pages). Denote the keys used for pagination and results.
        this.descriptors.page = {
            listGlossaries: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'glossaries')
        };
        const protoFilesRoot = this._gaxModule.protobufFromJSON(jsonProtos);
        // This API contains "long-running operations", which return a
        // an Operation object that allows for tracking of the operation,
        // rather than holding a request open.
        const lroOptions = {
            auth: this.auth,
            grpc: 'grpc' in this._gaxGrpc ? this._gaxGrpc.grpc : undefined
        };
        if (opts.fallback) {
            lroOptions.protoJson = protoFilesRoot;
            lroOptions.httpRules = [{ selector: 'google.cloud.location.Locations.GetLocation', get: '/v3beta1/{name=projects/*/locations/*}', }, { selector: 'google.cloud.location.Locations.ListLocations', get: '/v3beta1/{name=projects/*}/locations', }, { selector: 'google.longrunning.Operations.CancelOperation', post: '/v3beta1/{name=projects/*/locations/*/operations/*}:cancel', body: '*', }, { selector: 'google.longrunning.Operations.DeleteOperation', delete: '/v3beta1/{name=projects/*/locations/*/operations/*}', }, { selector: 'google.longrunning.Operations.GetOperation', get: '/v3beta1/{name=projects/*/locations/*/operations/*}', }, { selector: 'google.longrunning.Operations.ListOperations', get: '/v3beta1/{name=projects/*/locations/*}/operations', }, { selector: 'google.longrunning.Operations.WaitOperation', post: '/v3beta1/{name=projects/*/locations/*/operations/*}:wait', body: '*', }];
        }
        this.operationsClient = this._gaxModule.lro(lroOptions).operationsClient(opts);
        const batchTranslateTextResponse = protoFilesRoot.lookup('.google.cloud.translation.v3beta1.BatchTranslateResponse');
        const batchTranslateTextMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3beta1.BatchTranslateMetadata');
        const batchTranslateDocumentResponse = protoFilesRoot.lookup('.google.cloud.translation.v3beta1.BatchTranslateDocumentResponse');
        const batchTranslateDocumentMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3beta1.BatchTranslateDocumentMetadata');
        const createGlossaryResponse = protoFilesRoot.lookup('.google.cloud.translation.v3beta1.Glossary');
        const createGlossaryMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3beta1.CreateGlossaryMetadata');
        const deleteGlossaryResponse = protoFilesRoot.lookup('.google.cloud.translation.v3beta1.DeleteGlossaryResponse');
        const deleteGlossaryMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3beta1.DeleteGlossaryMetadata');
        this.descriptors.longrunning = {
            batchTranslateText: new this._gaxModule.LongrunningDescriptor(this.operationsClient, batchTranslateTextResponse.decode.bind(batchTranslateTextResponse), batchTranslateTextMetadata.decode.bind(batchTranslateTextMetadata)),
            batchTranslateDocument: new this._gaxModule.LongrunningDescriptor(this.operationsClient, batchTranslateDocumentResponse.decode.bind(batchTranslateDocumentResponse), batchTranslateDocumentMetadata.decode.bind(batchTranslateDocumentMetadata)),
            createGlossary: new this._gaxModule.LongrunningDescriptor(this.operationsClient, createGlossaryResponse.decode.bind(createGlossaryResponse), createGlossaryMetadata.decode.bind(createGlossaryMetadata)),
            deleteGlossary: new this._gaxModule.LongrunningDescriptor(this.operationsClient, deleteGlossaryResponse.decode.bind(deleteGlossaryResponse), deleteGlossaryMetadata.decode.bind(deleteGlossaryMetadata))
        };
        // Put together the default options sent with requests.
        this._defaults = this._gaxGrpc.constructSettings('google.cloud.translation.v3beta1.TranslationService', gapicConfig, opts.clientConfig || {}, { 'x-goog-api-client': clientHeader.join(' ') });
        // Set up a dictionary of "inner API calls"; the core implementation
        // of calling the API is handled in `google-gax`, with this code
        // merely providing the destination and request information.
        this.innerApiCalls = {};
        // Add a warn function to the client constructor so it can be easily tested.
        this.warn = this._gaxModule.warn;
    }
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize() {
        // If the client stub promise is already initialized, return immediately.
        if (this.translationServiceStub) {
            return this.translationServiceStub;
        }
        // Put together the "service stub" for
        // google.cloud.translation.v3beta1.TranslationService.
        this.translationServiceStub = this._gaxGrpc.createStub(this._opts.fallback ?
            this._protos.lookupService('google.cloud.translation.v3beta1.TranslationService') :
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            this._protos.google.cloud.translation.v3beta1.TranslationService, this._opts, this._providedCustomServicePath);
        // Iterate over each of the methods that the service provides
        // and create an API call method for each.
        const translationServiceStubMethods = ['translateText', 'detectLanguage', 'getSupportedLanguages', 'translateDocument', 'batchTranslateText', 'batchTranslateDocument', 'createGlossary', 'listGlossaries', 'getGlossary', 'deleteGlossary'];
        for (const methodName of translationServiceStubMethods) {
            const callPromise = this.translationServiceStub.then(stub => (...args) => {
                if (this._terminated) {
                    return Promise.reject('The client has already been closed.');
                }
                const func = stub[methodName];
                return func.apply(stub, args);
            }, (err) => () => {
                throw err;
            });
            const descriptor = this.descriptors.page[methodName] ||
                this.descriptors.longrunning[methodName] ||
                undefined;
            const apiCall = this._gaxModule.createApiCall(callPromise, this._defaults[methodName], descriptor, this._opts.fallback);
            this.innerApiCalls[methodName] = apiCall;
        }
        return this.translationServiceStub;
    }
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath() {
        if (typeof process === 'object' && typeof process.emitWarning === 'function') {
            process.emitWarning('Static servicePath is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'translate.googleapis.com';
    }
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint() {
        if (typeof process === 'object' && typeof process.emitWarning === 'function') {
            process.emitWarning('Static apiEndpoint is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'translate.googleapis.com';
    }
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint() {
        return this._servicePath;
    }
    get universeDomain() {
        return this._universeDomain;
    }
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port() {
        return 443;
    }
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes() {
        return [
            'https://www.googleapis.com/auth/cloud-platform',
            'https://www.googleapis.com/auth/cloud-translation'
        ];
    }
    /**
     * Return the project ID used by this class.
     * @returns {Promise} A promise that resolves to string containing the project ID.
     */
    getProjectId(callback) {
        if (callback) {
            this.auth.getProjectId(callback);
            return;
        }
        return this.auth.getProjectId();
    }
    translateText(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('translateText request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('translateText response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.translateText(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('translateText response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    detectLanguage(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('detectLanguage request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('detectLanguage response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.detectLanguage(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('detectLanguage response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    getSupportedLanguages(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('getSupportedLanguages request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getSupportedLanguages response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.getSupportedLanguages(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getSupportedLanguages response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    translateDocument(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('translateDocument request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('translateDocument response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.translateDocument(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('translateDocument response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    getGlossary(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'name': request.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('getGlossary request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getGlossary response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.getGlossary(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getGlossary response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    batchTranslateText(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('batchTranslateText response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('batchTranslateText request %j', request);
        return this.innerApiCalls.batchTranslateText(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('batchTranslateText response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `batchTranslateText()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3beta1/translation_service.batch_translate_text.js</caption>
     * region_tag:translate_v3beta1_generated_TranslationService_BatchTranslateText_async
     */
    async checkBatchTranslateTextProgress(name) {
        this._log.info('batchTranslateText long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.batchTranslateText, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    batchTranslateDocument(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('batchTranslateDocument response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('batchTranslateDocument request %j', request);
        return this.innerApiCalls.batchTranslateDocument(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('batchTranslateDocument response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `batchTranslateDocument()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3beta1/translation_service.batch_translate_document.js</caption>
     * region_tag:translate_v3beta1_generated_TranslationService_BatchTranslateDocument_async
     */
    async checkBatchTranslateDocumentProgress(name) {
        this._log.info('batchTranslateDocument long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.batchTranslateDocument, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    createGlossary(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('createGlossary response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('createGlossary request %j', request);
        return this.innerApiCalls.createGlossary(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('createGlossary response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `createGlossary()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3beta1/translation_service.create_glossary.js</caption>
     * region_tag:translate_v3beta1_generated_TranslationService_CreateGlossary_async
     */
    async checkCreateGlossaryProgress(name) {
        this._log.info('createGlossary long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.createGlossary, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    deleteGlossary(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'name': request.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('deleteGlossary response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('deleteGlossary request %j', request);
        return this.innerApiCalls.deleteGlossary(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('deleteGlossary response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `deleteGlossary()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3beta1/translation_service.delete_glossary.js</caption>
     * region_tag:translate_v3beta1_generated_TranslationService_DeleteGlossary_async
     */
    async checkDeleteGlossaryProgress(name) {
        this._log.info('deleteGlossary long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.deleteGlossary, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    listGlossaries(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listGlossaries values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listGlossaries request %j', request);
        return this.innerApiCalls
            .listGlossaries(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listGlossaries values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listGlossaries`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the project from which to list all of the glossaries.
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server may return fewer glossaries than
     *   requested. If unspecified, the server picks an appropriate default.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of [ListGlossariesResponse.next_page_token]
     *   returned from the previous call to `ListGlossaries` method.
     *   The first page is returned if `page_token`is empty or missing.
     * @param {string} [request.filter]
     *   Optional. Filter specifying constraints of a list operation.
     *   Specify the constraint by the format of "key=value", where key must be
     *   "src" or "tgt", and the value must be a valid language code.
     *   For multiple restrictions, concatenate them by "AND" (uppercase only),
     *   such as: "src=en-US AND tgt=zh-CN". Notice that the exact match is used
     *   here, which means using 'en-US' and 'en' can lead to different results,
     *   which depends on the language code you used when you create the glossary.
     *   For the unidirectional glossaries, the "src" and "tgt" add restrictions
     *   on the source and target language code separately.
     *   For the equivalent term set glossaries, the "src" and/or "tgt" add
     *   restrictions on the term set.
     *   For example: "src=en-US AND tgt=zh-CN" will only pick the unidirectional
     *   glossaries which exactly match the source language code as "en-US" and the
     *   target language code "zh-CN", but all equivalent term set glossaries which
     *   contain "en-US" and "zh-CN" in their language set will be picked.
     *   If missing, no filtering is performed.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3beta1.Glossary|Glossary} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listGlossariesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listGlossariesStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listGlossaries'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listGlossaries stream %j', request);
        return this.descriptors.page.listGlossaries.createStream(this.innerApiCalls.listGlossaries, request, callSettings);
    }
    /**
     * Equivalent to `listGlossaries`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the project from which to list all of the glossaries.
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server may return fewer glossaries than
     *   requested. If unspecified, the server picks an appropriate default.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of [ListGlossariesResponse.next_page_token]
     *   returned from the previous call to `ListGlossaries` method.
     *   The first page is returned if `page_token`is empty or missing.
     * @param {string} [request.filter]
     *   Optional. Filter specifying constraints of a list operation.
     *   Specify the constraint by the format of "key=value", where key must be
     *   "src" or "tgt", and the value must be a valid language code.
     *   For multiple restrictions, concatenate them by "AND" (uppercase only),
     *   such as: "src=en-US AND tgt=zh-CN". Notice that the exact match is used
     *   here, which means using 'en-US' and 'en' can lead to different results,
     *   which depends on the language code you used when you create the glossary.
     *   For the unidirectional glossaries, the "src" and "tgt" add restrictions
     *   on the source and target language code separately.
     *   For the equivalent term set glossaries, the "src" and/or "tgt" add
     *   restrictions on the term set.
     *   For example: "src=en-US AND tgt=zh-CN" will only pick the unidirectional
     *   glossaries which exactly match the source language code as "en-US" and the
     *   target language code "zh-CN", but all equivalent term set glossaries which
     *   contain "en-US" and "zh-CN" in their language set will be picked.
     *   If missing, no filtering is performed.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3beta1.Glossary|Glossary}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3beta1/translation_service.list_glossaries.js</caption>
     * region_tag:translate_v3beta1_generated_TranslationService_ListGlossaries_async
     */
    listGlossariesAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listGlossaries'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listGlossaries iterate %j', request);
        return this.descriptors.page.listGlossaries.asyncIterate(this.innerApiCalls['listGlossaries'], request, callSettings);
    }
    /**
     * Gets the access control policy for a resource. Returns an empty policy
     * if the resource exists and does not have a policy set.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {Object} [request.options]
     *   OPTIONAL: A `GetPolicyOptions` object for specifying options to
     *   `GetIamPolicy`. This field is only used by Cloud IAM.
     *
     *   This object should have the same structure as {@link google.iam.v1.GetPolicyOptions | GetPolicyOptions}.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.Policy | Policy}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.Policy | Policy}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    getIamPolicy(request, options, callback) {
        return this.iamClient.getIamPolicy(request, options, callback);
    }
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    setIamPolicy(request, options, callback) {
        return this.iamClient.setIamPolicy(request, options, callback);
    }
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     *
     */
    testIamPermissions(request, options, callback) {
        return this.iamClient.testIamPermissions(request, options, callback);
    }
    /**
       * Gets information about a location.
       *
       * @param {Object} request
       *   The request object that will be sent.
       * @param {string} request.name
       *   Resource name for the location.
       * @param {object} [options]
       *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html | CallOptions} for more details.
       * @returns {Promise} - The promise which resolves to an array.
       *   The first element of the array is an object representing {@link google.cloud.location.Location | Location}.
       *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
       *   for more details and examples.
       * @example
       * ```
       * const [response] = await client.getLocation(request);
       * ```
       */
    getLocation(request, options, callback) {
        return this.locationsClient.getLocation(request, options, callback);
    }
    /**
       * Lists information about the supported locations for this service. Returns an iterable object.
       *
       * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
       * @param {Object} request
       *   The request object that will be sent.
       * @param {string} request.name
       *   The resource that owns the locations collection, if applicable.
       * @param {string} request.filter
       *   The standard list filter.
       * @param {number} request.pageSize
       *   The standard list page size.
       * @param {string} request.pageToken
       *   The standard list page token.
       * @param {object} [options]
       *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
       * @returns {Object}
       *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
       *   When you iterate the returned iterable, each element will be an object representing
       *   {@link google.cloud.location.Location | Location}. The API will be called under the hood as needed, once per the page,
       *   so you can stop the iteration when you don't need more results.
       *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
       *   for more details and examples.
       * @example
       * ```
       * const iterable = client.listLocationsAsync(request);
       * for await (const response of iterable) {
       *   // process response
       * }
       * ```
       */
    listLocationsAsync(request, options) {
        return this.locationsClient.listLocationsAsync(request, options);
    }
    /**
       * Gets the latest state of a long-running operation.  Clients can use this
       * method to poll the operation result at intervals as recommended by the API
       * service.
       *
       * @param {Object} request - The request object that will be sent.
       * @param {string} request.name - The name of the operation resource.
       * @param {Object=} options
       *   Optional parameters. You can override the default settings for this call,
       *   e.g, timeout, retries, paginations, etc. See {@link
       *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
       *   for the details.
       * @param {function(?Error, ?Object)=} callback
       *   The function which will be called with the result of the API call.
       *
       *   The second parameter to the callback is an object representing
       *   {@link google.longrunning.Operation | google.longrunning.Operation}.
       * @return {Promise} - The promise which resolves to an array.
       *   The first element of the array is an object representing
       * {@link google.longrunning.Operation | google.longrunning.Operation}.
       * The promise has a method named "cancel" which cancels the ongoing API call.
       *
       * @example
       * ```
       * const client = longrunning.operationsClient();
       * const name = '';
       * const [response] = await client.getOperation({name});
       * // doThingsWith(response)
       * ```
       */
    getOperation(request, optionsOrCallback, callback) {
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        return this.operationsClient.getOperation(request, options, callback);
    }
    /**
     * Lists operations that match the specified filter in the request. If the
     * server doesn't support this method, it returns `UNIMPLEMENTED`. Returns an iterable object.
     *
     * For-await-of syntax is used with the iterable to recursively get response element on-demand.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation collection.
     * @param {string} request.filter - The standard list filter.
     * @param {number=} request.pageSize -
     *   The maximum number of resources contained in the underlying API
     *   response. If page streaming is performed per-resource, this
     *   parameter does not affect the return value. If page streaming is
     *   performed per-page, this determines the maximum number of
     *   resources in a page.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     *   details.
     * @returns {Object}
     *   An iterable Object that conforms to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | iteration protocols}.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * for await (const response of client.listOperationsAsync(request));
     * // doThingsWith(response)
     * ```
     */
    listOperationsAsync(request, options) {
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        return this.operationsClient.listOperationsAsync(request, options);
    }
    /**
     * Starts asynchronous cancellation on a long-running operation.  The server
     * makes a best effort to cancel the operation, but success is not
     * guaranteed.  If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.  Clients can use
     * {@link Operations.GetOperation} or
     * other methods to check whether the cancellation succeeded or whether the
     * operation completed despite cancellation. On successful cancellation,
     * the operation is not deleted; instead, it becomes an operation with
     * an {@link Operation.error} value with a {@link google.rpc.Status.code} of
     * 1, corresponding to `Code.CANCELLED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be cancelled.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     * details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.cancelOperation({name: ''});
     * ```
     */
    cancelOperation(request, optionsOrCallback, callback) {
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        return this.operationsClient.cancelOperation(request, options, callback);
    }
    /**
     * Deletes a long-running operation. This method indicates that the client is
     * no longer interested in the operation result. It does not cancel the
     * operation. If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be deleted.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     * for the details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.deleteOperation({name: ''});
     * ```
     */
    deleteOperation(request, optionsOrCallback, callback) {
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        return this.operationsClient.deleteOperation(request, options, callback);
    }
    // --------------------
    // -- Path templates --
    // --------------------
    /**
     * Return a fully-qualified glossary resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} glossary
     * @returns {string} Resource name string.
     */
    glossaryPath(project, location, glossary) {
        return this.pathTemplates.glossaryPathTemplate.render({
            project: project,
            location: location,
            glossary: glossary,
        });
    }
    /**
     * Parse the project from Glossary resource.
     *
     * @param {string} glossaryName
     *   A fully-qualified path representing Glossary resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromGlossaryName(glossaryName) {
        return this.pathTemplates.glossaryPathTemplate.match(glossaryName).project;
    }
    /**
     * Parse the location from Glossary resource.
     *
     * @param {string} glossaryName
     *   A fully-qualified path representing Glossary resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromGlossaryName(glossaryName) {
        return this.pathTemplates.glossaryPathTemplate.match(glossaryName).location;
    }
    /**
     * Parse the glossary from Glossary resource.
     *
     * @param {string} glossaryName
     *   A fully-qualified path representing Glossary resource.
     * @returns {string} A string representing the glossary.
     */
    matchGlossaryFromGlossaryName(glossaryName) {
        return this.pathTemplates.glossaryPathTemplate.match(glossaryName).glossary;
    }
    /**
     * Return a fully-qualified location resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    locationPath(project, location) {
        return this.pathTemplates.locationPathTemplate.render({
            project: project,
            location: location,
        });
    }
    /**
     * Parse the project from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromLocationName(locationName) {
        return this.pathTemplates.locationPathTemplate.match(locationName).project;
    }
    /**
     * Parse the location from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromLocationName(locationName) {
        return this.pathTemplates.locationPathTemplate.match(locationName).location;
    }
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close() {
        if (this.translationServiceStub && !this._terminated) {
            return this.translationServiceStub.then(stub => {
                this._log.info('ending gRPC channel');
                this._terminated = true;
                stub.close();
                this.iamClient.close().catch(err => { throw err; });
                this.locationsClient.close().catch(err => { throw err; });
                void this.operationsClient.close();
            });
        }
        return Promise.resolve();
    }
}
exports.TranslationServiceClient = TranslationServiceClient;
//# sourceMappingURL=translation_service_client.js.map