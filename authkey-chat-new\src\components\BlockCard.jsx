import { MdBlock } from "react-icons/md";
import useBlockUser from "../customHooks/useBlockUser";

const BlockCard = ({ selectedMobileNumber }) => {
    const { handleBlock,bLoading } = useBlockUser();

    return (
        <div className="d-flex w-100 justify-content-center align-items-center bg-light p-3 border rounded">
            <div className="text-dark me-3">
                This contact is blocked. Unblock this user to send & receive messages.
            </div>
            <button className="btn btn-outline-primary 
      d-flex align-items-center"
                onClick={() => handleBlock(selectedMobileNumber, 0)}
                disabled={bLoading}
            >
                <MdBlock size={20} className="me-1" />
                {bLoading?"Unblocking...": "Unblock"}
            </button>
        </div>
    )
}

export default BlockCard