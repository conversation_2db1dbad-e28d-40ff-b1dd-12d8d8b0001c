import React from "react";
import { useNavigate } from "react-router-dom";
import { ChatState } from "../../context/AllProviders";
import style from './overviewCard.module.css'
import Skeleton from "@mui/material/Skeleton";
import Box from "@mui/material/Box";
const OverviewCard = ({ title, value, data, loading }) => {
    const navigate = useNavigate();
    const { setFilteredChats } = ChatState();


    const handleClick = (title) => {
        if (data.length === 0) return;
        const encodedTitle = btoa(title);
        setFilteredChats([...data]);
        navigate(`/home/<USER>
    }

   
    return (
        <div className={`${style.overviewCard} ${data.length === 0 ? style.disabledCard : ''}`} role="button"
            onClick={() => handleClick(title)}>
            {/* <div className="mr-4">{getIcon()}</div> */}
            <div className="w-100">
                {loading.report ? (
                    // Skeleton loader for title and value
                    <>
                        <Skeleton variant="text" width="80%" height={20} />
                        <Skeleton variant="text" width="50%" height={32} />
                    </>
                ) : (
                    // Actual content
                    <>
                        <div className="font-semibold">{title}</div>
                        <h6
                            className="font-bold text-danger"
                            style={{ textShadow: "none" }}
                        >
                            {value}
                        </h6>
                    </>
                )}
            </div>
        </div>
    )
}

export default OverviewCard;