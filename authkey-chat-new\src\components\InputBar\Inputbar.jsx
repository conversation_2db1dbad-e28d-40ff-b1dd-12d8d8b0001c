import React from "react";
import styles from "./inputBar.module.css"; // Import the CSS module
import { useContext, useState, useEffect, useRef } from "react";
import { ChatContext } from "../../context/ChatContext";
import Input from "../Input";
import useSentences from "../../customHooks/useSentences";
import { ChatState } from "../../context/AllProviders";
import { FaBold } from "react-icons/fa";
import { FaItalic } from "react-icons/fa";
import { FaStrikethrough } from "react-icons/fa";
import ReplyPreview from "../ReplyPreview/ReplyPreview";
import BlockCard from "../BlockCard";
import { AuthContext } from "../../context/AuthContext";
import axios from "axios";
import { BASE_URL2 } from "../../api/api";
import { toast } from "react-toastify";
const Inputbar = ({
  setFaqOpen,
  notes,
  setshowNotesCard,
  setShowContactDetail,
  activeTab,
  setActiveTab,
  setshowQuickReply,

}) => {
  const { data } = useContext(ChatContext);
  const { text, setText, reply, setReply, selectedMobileNumber, selectedUserDetails } = ChatState();
  const textareaRef = useRef(null);
  const [showPreview, setShowPreview] = useState(false);
  const { currentUser } = useContext(AuthContext);
  const lastTypingTimeRef = useRef(0);


  const { filteredSentences, saveSentences, splitParagraphIntoSentences } =
    useSentences();

  const handleSuggestionClick = (suggestedText) => {
    setText((prevState) => {
      // Split the current input into words
      const words = prevState.split(/\s+/);

      // Check if the last word matches the suggestion
      const lastWord = words[words.length - 1];
      const suggestionFirstWord = suggestedText.split(/\s+/)[0];

      if (suggestionFirstWord.toLowerCase().includes(lastWord.toLowerCase())) {
        // If the last word matches the suggestion, replace it
        words[words.length - 1] = suggestedText;
      } else {
        // Otherwise, append the suggestion
        words.push(suggestedText);
      }
      // Join the words back into a single string and return
      return words.join(" ");
    });
    textareaRef.current?.focus();
  };
  const handleTabClick = (tabName) => {
    setActiveTab(tabName);

    if (tabName === "Note") {
      setshowNotesCard((prevState) => {
        const newState = !prevState; // Calculate the new state
        if (!newState) {
          // If the new state is `false`, reset the active tab to ""
          setActiveTab("");
        }
        return newState;
      });
      setShowContactDetail(false);
      setFaqOpen(false);
      setshowQuickReply(false);
    } else if (tabName === "quickReplies") {
      setshowQuickReply((prevState) => {
        const newState = !prevState; // Calculate the new state
        if (!newState) {
          // If the new state is `false`, reset the active tab to ""
          setActiveTab("");
        }
        return newState;
      });
      setShowContactDetail(false);
      setFaqOpen(false);
      setshowNotesCard(false);
    }
  };


  const showTypingStatus = async () => {
    const now = Date.now();
    if (now - lastTypingTimeRef.current < 25000) {
      return;
    }

    lastTypingTimeRef.current = now;
    const payload = {
      user_id: currentUser.user_id,
      token: currentUser.token,
      method: "typing",
      user_type: currentUser.user_type,
      mobile: selectedMobileNumber,
      brand_number: currentUser.brand_number,

    }
    try {

      const { data } = await axios.post(`${BASE_URL2}/conversation`, payload);

    } catch (error) {
      if (error.response && error.response.status === 429) {
        toast.error("Too many requests. Please try again later.")

      } else {
        console.error(error);
        toast.error("Something went wrong, please try again later")
      }
    }
  }

  useEffect(() => {
    if (!text || !currentUser.user_id
      || !currentUser.user_type || !currentUser.brand_number
      || !currentUser.token) return;

    showTypingStatus();
  }, [text, currentUser]);


  const applyFormat = (wrapSymbol) => {
    const input = textareaRef.current;
    if (!input) return;

    const startPos = input.selectionStart;
    const endPos = input.selectionEnd;

    const beforeSelection = text.substring(0, startPos);
    const selectedText = text.substring(startPos, endPos);
    const afterSelection = text.substring(endPos);

    if (selectedText === "") {
      const newText = beforeSelection + wrapSymbol + wrapSymbol + afterSelection;
      setText(newText);
      setShowPreview(true);

      // Move cursor between added symbols
      setTimeout(() => {
        input.focus();
        input.setSelectionRange(startPos + wrapSymbol.length, startPos + wrapSymbol.length);
      }, 10);
    } else {
      const newText = beforeSelection + wrapSymbol + selectedText + wrapSymbol + afterSelection;
      setText(newText);
      setShowPreview(true);

      setTimeout(() => input.focus(), 10);
    }
  };

  return (
    <div className={styles.inputContainer}>
      {selectedUserDetails.isBlock === 1 ? <BlockCard selectedMobileNumber={selectedMobileNumber} selectedUserDetails={selectedUserDetails} /> :
        <>
          {reply && <ReplyPreview reply={reply} onClose={() => setReply(null)} />}
          <header className="d-flex justify-content-start align-items-center w-100">
            <button
              onClick={() => handleTabClick("Note")}
              className={`btn ${styles.button} ms-2 ${activeTab === "Note" ? styles.activeButton : ""
                }`}
            >
              Notes {notes.length > 0 ? `(${notes.length})` : ""}
            </button>
            <button
              onClick={() => handleTabClick("quickReplies")}
              className={`btn ${styles.button} ms-2 ${activeTab === "quickReplies" ? styles.activeButton : ""
                }`}
            >
              Quick Replies
            </button>
            <div
              className="mx-1 px-1"
              role="button"
              onClick={() => applyFormat("*")}
            >
              <FaBold />
            </div>
            <div
              className="mx-1 px-1"
              role="button"
              onClick={() => applyFormat("_")}
            >
              <FaItalic />
            </div>
            <div
              className="mx-1 px-1"
              role="button"
              onClick={() => applyFormat("~")}
            >
              <FaStrikethrough />
            </div>
                        <div className="dropdown ms-2">
              <button 
                className="btn btn-light dropdown-toggle d-flex align-items-center" 
                type="button" 
                data-bs-toggle="dropdown" 
                aria-expanded="false"
                style={{ fontSize: '14px', padding: '4px 8px' }}
              >
                🌐 {languages.find(lang => lang.code === selectedLanguage)?.name || 'Language'}
                {isTranslatingRef.current && <span className="ms-1 text-primary">...</span>}
              </button>
              <ul className="dropdown-menu">
                <li><h6 className="dropdown-header">Translate to:</h6></li>
                {languages.map((language) => (
                  <li key={language.code}>
                    <button 
                      className={`dropdown-item ${selectedLanguage === language.code ? 'active' : ''}`}
                      onClick={() => setSelectedLanguage(language.code)}
                    >
                      {language.name}
                    </button>
                  </li>
                ))}
                <li><hr className="dropdown-divider" /></li>
                <li><small className="dropdown-item-text text-muted px-3">Press space for instant translation</small></li>
              </ul>
            </div>
          </header>
                    {translationError && (
            <div className="alert alert-warning alert-dismissible fade show mt-2" role="alert" style={{ fontSize: '12px', padding: '8px' }}>
              ⚠ {translationError}
              <button type="button" className="btn-close" onClick={() => setTranslationError('')} aria-label="Close"></button>
            </div>
          )}



          {/* Suggestion Section */}
          {filteredSentences.length ? (
            <div className={styles.suggestionsContainer}>
              <ul className={styles.suggestionsList}>
                {filteredSentences.map((sentence, index) => (
                  <li
                    key={index}
                    className={styles.suggestionPill}
                    onClick={() => handleSuggestionClick(sentence)}
                  >
                    {sentence}
                  </li>
                ))}
              </ul>
              <div
                className="d-none d-md-flex justify-content-center align-items-end w-100"
                style={{ fontSize: ".7rem", color: "grey" }}
              >
                <div>Press Tab to add text</div>
              </div>
            </div>
          ) : null}

          <div className={styles.textArea}>
            <Input
              showPreview={showPreview} setShowPreview={setShowPreview}
              textareaRef={textareaRef}
              handleSuggestionClick={handleSuggestionClick}
              saveSentences={saveSentences}
              splitParagraphIntoSentences={splitParagraphIntoSentences}
              filteredSentences={filteredSentences}
              selectedMobile={data.selectedMobile}
              convData={data}
            />
          </div>
        </>
      }
    </div>
  );
};

export default Inputbar;
