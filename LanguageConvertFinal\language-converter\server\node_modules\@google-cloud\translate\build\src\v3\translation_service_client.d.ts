import type * as gax from 'google-gax';
import type { Callback, CallOptions, Descriptors, ClientOptions, LROperation, PaginationCallback, IamClient, IamProtos, LocationsClient, LocationProtos } from 'google-gax';
import { Transform } from 'stream';
import * as protos from '../../protos/protos';
/**
 *  Provides natural language translation operations.
 * @class
 * @memberof v3
 */
export declare class TranslationServiceClient {
    private _terminated;
    private _opts;
    private _providedCustomServicePath;
    private _gaxModule;
    private _gaxGrpc;
    private _protos;
    private _defaults;
    private _universeDomain;
    private _servicePath;
    private _log;
    auth: gax.GoogleAuth;
    descriptors: Descriptors;
    warn: (code: string, message: string, warnType?: string) => void;
    innerApiCalls: {
        [name: string]: Function;
    };
    iamClient: IamClient;
    locationsClient: LocationsClient;
    pathTemplates: {
        [name: string]: gax.PathTemplate;
    };
    operationsClient: gax.OperationsClient;
    translationServiceStub?: Promise<{
        [name: string]: Function;
    }>;
    /**
     * Construct an instance of TranslationServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://cloud.google.com/docs/authentication/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new TranslationServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts?: ClientOptions, gaxInstance?: typeof gax | typeof gax.fallback);
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize(): Promise<{
        [name: string]: Function;
    }>;
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath(): string;
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint(): string;
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint(): string;
    get universeDomain(): string;
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port(): number;
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes(): string[];
    getProjectId(): Promise<string>;
    getProjectId(callback: Callback<string, undefined, undefined>): void;
    /**
     * Translates input text and returns translated text.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string[]} request.contents
     *   Required. The content of the input in string format.
     *   We recommend the total content be less than 30,000 codepoints. The max
     *   length of this field is 1024. Use BatchTranslateText for larger text.
     * @param {string} [request.mimeType]
     *   Optional. The format of the source text, for example, "text/html",
     *    "text/plain". If left blank, the MIME type defaults to "text/html".
     * @param {string} [request.sourceLanguageCode]
     *   Optional. The ISO-639 language code of the input text if
     *   known, for example, "en-US" or "sr-Latn". Supported language codes are
     *   listed in [Language
     *   Support](https://cloud.google.com/translate/docs/languages). If the source
     *   language isn't specified, the API attempts to identify the source language
     *   automatically and returns the source language within the response.
     * @param {string} request.targetLanguageCode
     *   Required. The ISO-639 language code to use for translation of the input
     *   text, set to one of the language codes listed in [Language
     *   Support](https://cloud.google.com/translate/docs/languages).
     * @param {string} request.parent
     *   Required. Project or location to make a call. Must refer to a caller's
     *   project.
     *
     *   Format: `projects/{project-number-or-id}` or
     *   `projects/{project-number-or-id}/locations/{location-id}`.
     *
     *   For global calls, use `projects/{project-number-or-id}/locations/global` or
     *   `projects/{project-number-or-id}`.
     *
     *   Non-global location is required for requests using AutoML models or
     *   custom glossaries.
     *
     *   Models and glossaries must be within the same region (have same
     *   location-id), otherwise an INVALID_ARGUMENT (400) error is returned.
     * @param {string} [request.model]
     *   Optional. The `model` type requested for this translation.
     *
     *   The format depends on model type:
     *
     *   - AutoML Translation models:
     *     `projects/{project-number-or-id}/locations/{location-id}/models/{model-id}`
     *
     *   - General (built-in) models:
     *     `projects/{project-number-or-id}/locations/{location-id}/models/general/nmt`,
     *
     *   - Translation LLM models:
     *     `projects/{project-number-or-id}/locations/{location-id}/models/general/translation-llm`,
     *
     *   For global (non-regionalized) requests, use `location-id` `global`.
     *   For example,
     *   `projects/{project-number-or-id}/locations/global/models/general/nmt`.
     *
     *   If not provided, the default Google model (NMT) will be used
     * @param {google.cloud.translation.v3.TranslateTextGlossaryConfig} [request.glossaryConfig]
     *   Optional. Glossary to be applied. The glossary must be
     *   within the same region (have the same location-id) as the model, otherwise
     *   an INVALID_ARGUMENT (400) error is returned.
     * @param {google.cloud.translation.v3.TransliterationConfig} [request.transliterationConfig]
     *   Optional. Transliteration to be applied.
     * @param {number[]} [request.labels]
     *   Optional. The labels with user-defined metadata for the request.
     *
     *   Label keys and values can be no longer than 63 characters
     *   (Unicode codepoints), can only contain lowercase letters, numeric
     *   characters, underscores and dashes. International characters are allowed.
     *   Label values are optional. Label keys must start with a letter.
     *
     *   See https://cloud.google.com/translate/docs/advanced/labels for more
     *   information.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.TranslateTextResponse|TranslateTextResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.translate_text.js</caption>
     * region_tag:translate_v3_generated_TranslationService_TranslateText_async
     */
    translateText(request?: protos.google.cloud.translation.v3.ITranslateTextRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.ITranslateTextResponse,
        protos.google.cloud.translation.v3.ITranslateTextRequest | undefined,
        {} | undefined
    ]>;
    translateText(request: protos.google.cloud.translation.v3.ITranslateTextRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.ITranslateTextResponse, protos.google.cloud.translation.v3.ITranslateTextRequest | null | undefined, {} | null | undefined>): void;
    translateText(request: protos.google.cloud.translation.v3.ITranslateTextRequest, callback: Callback<protos.google.cloud.translation.v3.ITranslateTextResponse, protos.google.cloud.translation.v3.ITranslateTextRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Romanize input text written in non-Latin scripts to Latin text.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Project or location to make a call. Must refer to a caller's
     *   project.
     *
     *   Format: `projects/{project-number-or-id}/locations/{location-id}` or
     *   `projects/{project-number-or-id}`.
     *
     *   For global calls, use `projects/{project-number-or-id}/locations/global` or
     *   `projects/{project-number-or-id}`.
     * @param {string[]} request.contents
     *   Required. The content of the input in string format.
     * @param {string} [request.sourceLanguageCode]
     *   Optional. The ISO-639 language code of the input text if
     *   known, for example, "hi" or "zh". Supported language codes are
     *   listed in [Language
     *   Support](https://cloud.google.com/translate/docs/languages#roman). If the
     *   source language isn't specified, the API attempts to identify the source
     *   language automatically and returns the source language for each content in
     *   the response.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.RomanizeTextResponse|RomanizeTextResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.romanize_text.js</caption>
     * region_tag:translate_v3_generated_TranslationService_RomanizeText_async
     */
    romanizeText(request?: protos.google.cloud.translation.v3.IRomanizeTextRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IRomanizeTextResponse,
        protos.google.cloud.translation.v3.IRomanizeTextRequest | undefined,
        {} | undefined
    ]>;
    romanizeText(request: protos.google.cloud.translation.v3.IRomanizeTextRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.IRomanizeTextResponse, protos.google.cloud.translation.v3.IRomanizeTextRequest | null | undefined, {} | null | undefined>): void;
    romanizeText(request: protos.google.cloud.translation.v3.IRomanizeTextRequest, callback: Callback<protos.google.cloud.translation.v3.IRomanizeTextResponse, protos.google.cloud.translation.v3.IRomanizeTextRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Detects the language of text within a request.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Project or location to make a call. Must refer to a caller's
     *   project.
     *
     *   Format: `projects/{project-number-or-id}/locations/{location-id}` or
     *   `projects/{project-number-or-id}`.
     *
     *   For global calls, use `projects/{project-number-or-id}/locations/global` or
     *   `projects/{project-number-or-id}`.
     *
     *   Only models within the same region (has same location-id) can be used.
     *   Otherwise an INVALID_ARGUMENT (400) error is returned.
     * @param {string} [request.model]
     *   Optional. The language detection model to be used.
     *
     *   Format:
     *   `projects/{project-number-or-id}/locations/{location-id}/models/language-detection/{model-id}`
     *
     *   Only one language detection model is currently supported:
     *   `projects/{project-number-or-id}/locations/{location-id}/models/language-detection/default`.
     *
     *   If not specified, the default model is used.
     * @param {string} request.content
     *   The content of the input stored as a string.
     * @param {string} [request.mimeType]
     *   Optional. The format of the source text, for example, "text/html",
     *   "text/plain". If left blank, the MIME type defaults to "text/html".
     * @param {number[]} [request.labels]
     *   Optional. The labels with user-defined metadata for the request.
     *
     *   Label keys and values can be no longer than 63 characters
     *   (Unicode codepoints), can only contain lowercase letters, numeric
     *   characters, underscores and dashes. International characters are allowed.
     *   Label values are optional. Label keys must start with a letter.
     *
     *   See https://cloud.google.com/translate/docs/advanced/labels for more
     *   information.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.DetectLanguageResponse|DetectLanguageResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.detect_language.js</caption>
     * region_tag:translate_v3_generated_TranslationService_DetectLanguage_async
     */
    detectLanguage(request?: protos.google.cloud.translation.v3.IDetectLanguageRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IDetectLanguageResponse,
        protos.google.cloud.translation.v3.IDetectLanguageRequest | undefined,
        {} | undefined
    ]>;
    detectLanguage(request: protos.google.cloud.translation.v3.IDetectLanguageRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.IDetectLanguageResponse, protos.google.cloud.translation.v3.IDetectLanguageRequest | null | undefined, {} | null | undefined>): void;
    detectLanguage(request: protos.google.cloud.translation.v3.IDetectLanguageRequest, callback: Callback<protos.google.cloud.translation.v3.IDetectLanguageResponse, protos.google.cloud.translation.v3.IDetectLanguageRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Returns a list of supported languages for translation.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Project or location to make a call. Must refer to a caller's
     *   project.
     *
     *   Format: `projects/{project-number-or-id}` or
     *   `projects/{project-number-or-id}/locations/{location-id}`.
     *
     *   For global calls, use `projects/{project-number-or-id}/locations/global` or
     *   `projects/{project-number-or-id}`.
     *
     *   Non-global location is required for AutoML models.
     *
     *   Only models within the same region (have same location-id) can be used,
     *   otherwise an INVALID_ARGUMENT (400) error is returned.
     * @param {string} [request.displayLanguageCode]
     *   Optional. The language to use to return localized, human readable names
     *   of supported languages. If missing, then display names are not returned
     *   in a response.
     * @param {string} [request.model]
     *   Optional. Get supported languages of this model.
     *
     *   The format depends on model type:
     *
     *   - AutoML Translation models:
     *     `projects/{project-number-or-id}/locations/{location-id}/models/{model-id}`
     *
     *   - General (built-in) models:
     *     `projects/{project-number-or-id}/locations/{location-id}/models/general/nmt`,
     *
     *
     *   Returns languages supported by the specified model.
     *   If missing, we get supported languages of Google general NMT model.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.SupportedLanguages|SupportedLanguages}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.get_supported_languages.js</caption>
     * region_tag:translate_v3_generated_TranslationService_GetSupportedLanguages_async
     */
    getSupportedLanguages(request?: protos.google.cloud.translation.v3.IGetSupportedLanguagesRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.ISupportedLanguages,
        protos.google.cloud.translation.v3.IGetSupportedLanguagesRequest | undefined,
        {} | undefined
    ]>;
    getSupportedLanguages(request: protos.google.cloud.translation.v3.IGetSupportedLanguagesRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.ISupportedLanguages, protos.google.cloud.translation.v3.IGetSupportedLanguagesRequest | null | undefined, {} | null | undefined>): void;
    getSupportedLanguages(request: protos.google.cloud.translation.v3.IGetSupportedLanguagesRequest, callback: Callback<protos.google.cloud.translation.v3.ISupportedLanguages, protos.google.cloud.translation.v3.IGetSupportedLanguagesRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Translates documents in synchronous mode.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Location to make a regional call.
     *
     *   Format: `projects/{project-number-or-id}/locations/{location-id}`.
     *
     *   For global calls, use `projects/{project-number-or-id}/locations/global` or
     *   `projects/{project-number-or-id}`.
     *
     *   Non-global location is required for requests using AutoML models or custom
     *   glossaries.
     *
     *   Models and glossaries must be within the same region (have the same
     *   location-id), otherwise an INVALID_ARGUMENT (400) error is returned.
     * @param {string} [request.sourceLanguageCode]
     *   Optional. The ISO-639 language code of the input document if known, for
     *   example, "en-US" or "sr-Latn". Supported language codes are listed in
     *   [Language Support](https://cloud.google.com/translate/docs/languages). If
     *   the source language isn't specified, the API attempts to identify the
     *   source language automatically and returns the source language within the
     *   response. Source language must be specified if the request contains a
     *   glossary or a custom model.
     * @param {string} request.targetLanguageCode
     *   Required. The ISO-639 language code to use for translation of the input
     *   document, set to one of the language codes listed in [Language
     *   Support](https://cloud.google.com/translate/docs/languages).
     * @param {google.cloud.translation.v3.DocumentInputConfig} request.documentInputConfig
     *   Required. Input configurations.
     * @param {google.cloud.translation.v3.DocumentOutputConfig} [request.documentOutputConfig]
     *   Optional. Output configurations.
     *   Defines if the output file should be stored within Cloud Storage as well
     *   as the desired output format. If not provided the translated file will
     *   only be returned through a byte-stream and its output mime type will be
     *   the same as the input file's mime type.
     * @param {string} [request.model]
     *   Optional. The `model` type requested for this translation.
     *
     *   The format depends on model type:
     *
     *   - AutoML Translation models:
     *     `projects/{project-number-or-id}/locations/{location-id}/models/{model-id}`
     *
     *   - General (built-in) models:
     *     `projects/{project-number-or-id}/locations/{location-id}/models/general/nmt`,
     *
     *
     *   If not provided, the default Google model (NMT) will be used for
     *   translation.
     * @param {google.cloud.translation.v3.TranslateTextGlossaryConfig} [request.glossaryConfig]
     *   Optional. Glossary to be applied. The glossary must be within the same
     *   region (have the same location-id) as the model, otherwise an
     *   INVALID_ARGUMENT (400) error is returned.
     * @param {number[]} [request.labels]
     *   Optional. The labels with user-defined metadata for the request.
     *
     *   Label keys and values can be no longer than 63 characters (Unicode
     *   codepoints), can only contain lowercase letters, numeric characters,
     *   underscores and dashes. International characters are allowed. Label values
     *   are optional. Label keys must start with a letter.
     *
     *   See https://cloud.google.com/translate/docs/advanced/labels for more
     *   information.
     * @param {string} [request.customizedAttribution]
     *   Optional. This flag is to support user customized attribution.
     *   If not provided, the default is `Machine Translated by Google`.
     *   Customized attribution should follow rules in
     *   https://cloud.google.com/translate/attribution#attribution_and_logos
     * @param {boolean} [request.isTranslateNativePdfOnly]
     *   Optional. is_translate_native_pdf_only field for external customers.
     *   If true, the page limit of online native pdf translation is 300 and only
     *   native pdf pages will be translated.
     * @param {boolean} [request.enableShadowRemovalNativePdf]
     *   Optional. If true, use the text removal server to remove the shadow text on
     *   background image for native pdf translation.
     *   Shadow removal feature can only be enabled when
     *   is_translate_native_pdf_only: false && pdf_native_only: false
     * @param {boolean} [request.enableRotationCorrection]
     *   Optional. If true, enable auto rotation correction in DVS.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.TranslateDocumentResponse|TranslateDocumentResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.translate_document.js</caption>
     * region_tag:translate_v3_generated_TranslationService_TranslateDocument_async
     */
    translateDocument(request?: protos.google.cloud.translation.v3.ITranslateDocumentRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.ITranslateDocumentResponse,
        protos.google.cloud.translation.v3.ITranslateDocumentRequest | undefined,
        {} | undefined
    ]>;
    translateDocument(request: protos.google.cloud.translation.v3.ITranslateDocumentRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.ITranslateDocumentResponse, protos.google.cloud.translation.v3.ITranslateDocumentRequest | null | undefined, {} | null | undefined>): void;
    translateDocument(request: protos.google.cloud.translation.v3.ITranslateDocumentRequest, callback: Callback<protos.google.cloud.translation.v3.ITranslateDocumentResponse, protos.google.cloud.translation.v3.ITranslateDocumentRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets a glossary. Returns NOT_FOUND, if the glossary doesn't
     * exist.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the glossary to retrieve.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.Glossary|Glossary}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.get_glossary.js</caption>
     * region_tag:translate_v3_generated_TranslationService_GetGlossary_async
     */
    getGlossary(request?: protos.google.cloud.translation.v3.IGetGlossaryRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IGlossary,
        protos.google.cloud.translation.v3.IGetGlossaryRequest | undefined,
        {} | undefined
    ]>;
    getGlossary(request: protos.google.cloud.translation.v3.IGetGlossaryRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.IGlossary, protos.google.cloud.translation.v3.IGetGlossaryRequest | null | undefined, {} | null | undefined>): void;
    getGlossary(request: protos.google.cloud.translation.v3.IGetGlossaryRequest, callback: Callback<protos.google.cloud.translation.v3.IGlossary, protos.google.cloud.translation.v3.IGetGlossaryRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets a single glossary entry by the given id.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the glossary entry to get
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.GlossaryEntry|GlossaryEntry}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.get_glossary_entry.js</caption>
     * region_tag:translate_v3_generated_TranslationService_GetGlossaryEntry_async
     */
    getGlossaryEntry(request?: protos.google.cloud.translation.v3.IGetGlossaryEntryRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IGlossaryEntry,
        protos.google.cloud.translation.v3.IGetGlossaryEntryRequest | undefined,
        {} | undefined
    ]>;
    getGlossaryEntry(request: protos.google.cloud.translation.v3.IGetGlossaryEntryRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.IGlossaryEntry, protos.google.cloud.translation.v3.IGetGlossaryEntryRequest | null | undefined, {} | null | undefined>): void;
    getGlossaryEntry(request: protos.google.cloud.translation.v3.IGetGlossaryEntryRequest, callback: Callback<protos.google.cloud.translation.v3.IGlossaryEntry, protos.google.cloud.translation.v3.IGetGlossaryEntryRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Creates a glossary entry.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the glossary to create the entry under.
     * @param {google.cloud.translation.v3.GlossaryEntry} request.glossaryEntry
     *   Required. The glossary entry to create
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.GlossaryEntry|GlossaryEntry}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.create_glossary_entry.js</caption>
     * region_tag:translate_v3_generated_TranslationService_CreateGlossaryEntry_async
     */
    createGlossaryEntry(request?: protos.google.cloud.translation.v3.ICreateGlossaryEntryRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IGlossaryEntry,
        protos.google.cloud.translation.v3.ICreateGlossaryEntryRequest | undefined,
        {} | undefined
    ]>;
    createGlossaryEntry(request: protos.google.cloud.translation.v3.ICreateGlossaryEntryRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.IGlossaryEntry, protos.google.cloud.translation.v3.ICreateGlossaryEntryRequest | null | undefined, {} | null | undefined>): void;
    createGlossaryEntry(request: protos.google.cloud.translation.v3.ICreateGlossaryEntryRequest, callback: Callback<protos.google.cloud.translation.v3.IGlossaryEntry, protos.google.cloud.translation.v3.ICreateGlossaryEntryRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Updates a glossary entry.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.translation.v3.GlossaryEntry} request.glossaryEntry
     *   Required. The glossary entry to update.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.GlossaryEntry|GlossaryEntry}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.update_glossary_entry.js</caption>
     * region_tag:translate_v3_generated_TranslationService_UpdateGlossaryEntry_async
     */
    updateGlossaryEntry(request?: protos.google.cloud.translation.v3.IUpdateGlossaryEntryRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IGlossaryEntry,
        protos.google.cloud.translation.v3.IUpdateGlossaryEntryRequest | undefined,
        {} | undefined
    ]>;
    updateGlossaryEntry(request: protos.google.cloud.translation.v3.IUpdateGlossaryEntryRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.IGlossaryEntry, protos.google.cloud.translation.v3.IUpdateGlossaryEntryRequest | null | undefined, {} | null | undefined>): void;
    updateGlossaryEntry(request: protos.google.cloud.translation.v3.IUpdateGlossaryEntryRequest, callback: Callback<protos.google.cloud.translation.v3.IGlossaryEntry, protos.google.cloud.translation.v3.IUpdateGlossaryEntryRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Deletes a single entry from the glossary
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the glossary entry to delete
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.delete_glossary_entry.js</caption>
     * region_tag:translate_v3_generated_TranslationService_DeleteGlossaryEntry_async
     */
    deleteGlossaryEntry(request?: protos.google.cloud.translation.v3.IDeleteGlossaryEntryRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        protos.google.cloud.translation.v3.IDeleteGlossaryEntryRequest | undefined,
        {} | undefined
    ]>;
    deleteGlossaryEntry(request: protos.google.cloud.translation.v3.IDeleteGlossaryEntryRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IDeleteGlossaryEntryRequest | null | undefined, {} | null | undefined>): void;
    deleteGlossaryEntry(request: protos.google.cloud.translation.v3.IDeleteGlossaryEntryRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IDeleteGlossaryEntryRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets a Dataset.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the dataset to retrieve.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.Dataset|Dataset}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.get_dataset.js</caption>
     * region_tag:translate_v3_generated_TranslationService_GetDataset_async
     */
    getDataset(request?: protos.google.cloud.translation.v3.IGetDatasetRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IDataset,
        protos.google.cloud.translation.v3.IGetDatasetRequest | undefined,
        {} | undefined
    ]>;
    getDataset(request: protos.google.cloud.translation.v3.IGetDatasetRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.IDataset, protos.google.cloud.translation.v3.IGetDatasetRequest | null | undefined, {} | null | undefined>): void;
    getDataset(request: protos.google.cloud.translation.v3.IGetDatasetRequest, callback: Callback<protos.google.cloud.translation.v3.IDataset, protos.google.cloud.translation.v3.IGetDatasetRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Creates an Adaptive MT dataset.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Name of the parent project. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}`
     * @param {google.cloud.translation.v3.AdaptiveMtDataset} request.adaptiveMtDataset
     *   Required. The AdaptiveMtDataset to be created.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.AdaptiveMtDataset|AdaptiveMtDataset}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.create_adaptive_mt_dataset.js</caption>
     * region_tag:translate_v3_generated_TranslationService_CreateAdaptiveMtDataset_async
     */
    createAdaptiveMtDataset(request?: protos.google.cloud.translation.v3.ICreateAdaptiveMtDatasetRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IAdaptiveMtDataset,
        protos.google.cloud.translation.v3.ICreateAdaptiveMtDatasetRequest | undefined,
        {} | undefined
    ]>;
    createAdaptiveMtDataset(request: protos.google.cloud.translation.v3.ICreateAdaptiveMtDatasetRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.IAdaptiveMtDataset, protos.google.cloud.translation.v3.ICreateAdaptiveMtDatasetRequest | null | undefined, {} | null | undefined>): void;
    createAdaptiveMtDataset(request: protos.google.cloud.translation.v3.ICreateAdaptiveMtDatasetRequest, callback: Callback<protos.google.cloud.translation.v3.IAdaptiveMtDataset, protos.google.cloud.translation.v3.ICreateAdaptiveMtDatasetRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Deletes an Adaptive MT dataset, including all its entries and associated
     * metadata.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. Name of the dataset. In the form of
     *   `projects/{project-number-or-id}/locations/{location-id}/adaptiveMtDatasets/{adaptive-mt-dataset-id}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.delete_adaptive_mt_dataset.js</caption>
     * region_tag:translate_v3_generated_TranslationService_DeleteAdaptiveMtDataset_async
     */
    deleteAdaptiveMtDataset(request?: protos.google.cloud.translation.v3.IDeleteAdaptiveMtDatasetRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        protos.google.cloud.translation.v3.IDeleteAdaptiveMtDatasetRequest | undefined,
        {} | undefined
    ]>;
    deleteAdaptiveMtDataset(request: protos.google.cloud.translation.v3.IDeleteAdaptiveMtDatasetRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IDeleteAdaptiveMtDatasetRequest | null | undefined, {} | null | undefined>): void;
    deleteAdaptiveMtDataset(request: protos.google.cloud.translation.v3.IDeleteAdaptiveMtDatasetRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IDeleteAdaptiveMtDatasetRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets the Adaptive MT dataset.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. Name of the dataset. In the form of
     *   `projects/{project-number-or-id}/locations/{location-id}/adaptiveMtDatasets/{adaptive-mt-dataset-id}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.AdaptiveMtDataset|AdaptiveMtDataset}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.get_adaptive_mt_dataset.js</caption>
     * region_tag:translate_v3_generated_TranslationService_GetAdaptiveMtDataset_async
     */
    getAdaptiveMtDataset(request?: protos.google.cloud.translation.v3.IGetAdaptiveMtDatasetRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IAdaptiveMtDataset,
        protos.google.cloud.translation.v3.IGetAdaptiveMtDatasetRequest | undefined,
        {} | undefined
    ]>;
    getAdaptiveMtDataset(request: protos.google.cloud.translation.v3.IGetAdaptiveMtDatasetRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.IAdaptiveMtDataset, protos.google.cloud.translation.v3.IGetAdaptiveMtDatasetRequest | null | undefined, {} | null | undefined>): void;
    getAdaptiveMtDataset(request: protos.google.cloud.translation.v3.IGetAdaptiveMtDatasetRequest, callback: Callback<protos.google.cloud.translation.v3.IAdaptiveMtDataset, protos.google.cloud.translation.v3.IGetAdaptiveMtDatasetRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Translate text using Adaptive MT.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Location to make a regional call.
     *
     *   Format: `projects/{project-number-or-id}/locations/{location-id}`.
     * @param {string} request.dataset
     *   Required. The resource name for the dataset to use for adaptive MT.
     *   `projects/{project}/locations/{location-id}/adaptiveMtDatasets/{dataset}`
     * @param {string[]} request.content
     *   Required. The content of the input in string format.
     * @param {google.cloud.translation.v3.AdaptiveMtTranslateRequest.ReferenceSentenceConfig} request.referenceSentenceConfig
     *   Configuration for caller provided reference sentences.
     * @param {google.cloud.translation.v3.AdaptiveMtTranslateRequest.GlossaryConfig} [request.glossaryConfig]
     *   Optional. Glossary to be applied. The glossary must be
     *   within the same region (have the same location-id) as the model, otherwise
     *   an INVALID_ARGUMENT (400) error is returned.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.AdaptiveMtTranslateResponse|AdaptiveMtTranslateResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.adaptive_mt_translate.js</caption>
     * region_tag:translate_v3_generated_TranslationService_AdaptiveMtTranslate_async
     */
    adaptiveMtTranslate(request?: protos.google.cloud.translation.v3.IAdaptiveMtTranslateRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IAdaptiveMtTranslateResponse,
        protos.google.cloud.translation.v3.IAdaptiveMtTranslateRequest | undefined,
        {} | undefined
    ]>;
    adaptiveMtTranslate(request: protos.google.cloud.translation.v3.IAdaptiveMtTranslateRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.IAdaptiveMtTranslateResponse, protos.google.cloud.translation.v3.IAdaptiveMtTranslateRequest | null | undefined, {} | null | undefined>): void;
    adaptiveMtTranslate(request: protos.google.cloud.translation.v3.IAdaptiveMtTranslateRequest, callback: Callback<protos.google.cloud.translation.v3.IAdaptiveMtTranslateResponse, protos.google.cloud.translation.v3.IAdaptiveMtTranslateRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets and AdaptiveMtFile
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the file, in form of
     *   `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.AdaptiveMtFile|AdaptiveMtFile}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.get_adaptive_mt_file.js</caption>
     * region_tag:translate_v3_generated_TranslationService_GetAdaptiveMtFile_async
     */
    getAdaptiveMtFile(request?: protos.google.cloud.translation.v3.IGetAdaptiveMtFileRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IAdaptiveMtFile,
        protos.google.cloud.translation.v3.IGetAdaptiveMtFileRequest | undefined,
        {} | undefined
    ]>;
    getAdaptiveMtFile(request: protos.google.cloud.translation.v3.IGetAdaptiveMtFileRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.IAdaptiveMtFile, protos.google.cloud.translation.v3.IGetAdaptiveMtFileRequest | null | undefined, {} | null | undefined>): void;
    getAdaptiveMtFile(request: protos.google.cloud.translation.v3.IGetAdaptiveMtFileRequest, callback: Callback<protos.google.cloud.translation.v3.IAdaptiveMtFile, protos.google.cloud.translation.v3.IGetAdaptiveMtFileRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Deletes an AdaptiveMtFile along with its sentences.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the file to delete, in form of
     *   `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}`
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.protobuf.Empty|Empty}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.delete_adaptive_mt_file.js</caption>
     * region_tag:translate_v3_generated_TranslationService_DeleteAdaptiveMtFile_async
     */
    deleteAdaptiveMtFile(request?: protos.google.cloud.translation.v3.IDeleteAdaptiveMtFileRequest, options?: CallOptions): Promise<[
        protos.google.protobuf.IEmpty,
        protos.google.cloud.translation.v3.IDeleteAdaptiveMtFileRequest | undefined,
        {} | undefined
    ]>;
    deleteAdaptiveMtFile(request: protos.google.cloud.translation.v3.IDeleteAdaptiveMtFileRequest, options: CallOptions, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IDeleteAdaptiveMtFileRequest | null | undefined, {} | null | undefined>): void;
    deleteAdaptiveMtFile(request: protos.google.cloud.translation.v3.IDeleteAdaptiveMtFileRequest, callback: Callback<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IDeleteAdaptiveMtFileRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Imports an AdaptiveMtFile and adds all of its sentences into the
     * AdaptiveMtDataset.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the file, in form of
     *   `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatasets/{dataset}`
     * @param {google.cloud.translation.v3.FileInputSource} request.fileInputSource
     *   Inline file source.
     * @param {google.cloud.translation.v3.GcsInputSource} request.gcsInputSource
     *   Google Cloud Storage file source.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.ImportAdaptiveMtFileResponse|ImportAdaptiveMtFileResponse}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.import_adaptive_mt_file.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ImportAdaptiveMtFile_async
     */
    importAdaptiveMtFile(request?: protos.google.cloud.translation.v3.IImportAdaptiveMtFileRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IImportAdaptiveMtFileResponse,
        protos.google.cloud.translation.v3.IImportAdaptiveMtFileRequest | undefined,
        {} | undefined
    ]>;
    importAdaptiveMtFile(request: protos.google.cloud.translation.v3.IImportAdaptiveMtFileRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.IImportAdaptiveMtFileResponse, protos.google.cloud.translation.v3.IImportAdaptiveMtFileRequest | null | undefined, {} | null | undefined>): void;
    importAdaptiveMtFile(request: protos.google.cloud.translation.v3.IImportAdaptiveMtFileRequest, callback: Callback<protos.google.cloud.translation.v3.IImportAdaptiveMtFileResponse, protos.google.cloud.translation.v3.IImportAdaptiveMtFileRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Gets a model.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The resource name of the model to retrieve.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link protos.google.cloud.translation.v3.Model|Model}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.get_model.js</caption>
     * region_tag:translate_v3_generated_TranslationService_GetModel_async
     */
    getModel(request?: protos.google.cloud.translation.v3.IGetModelRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IModel,
        protos.google.cloud.translation.v3.IGetModelRequest | undefined,
        {} | undefined
    ]>;
    getModel(request: protos.google.cloud.translation.v3.IGetModelRequest, options: CallOptions, callback: Callback<protos.google.cloud.translation.v3.IModel, protos.google.cloud.translation.v3.IGetModelRequest | null | undefined, {} | null | undefined>): void;
    getModel(request: protos.google.cloud.translation.v3.IGetModelRequest, callback: Callback<protos.google.cloud.translation.v3.IModel, protos.google.cloud.translation.v3.IGetModelRequest | null | undefined, {} | null | undefined>): void;
    /**
     * Translates a large volume of text in asynchronous batch mode.
     * This function provides real-time output as the inputs are being processed.
     * If caller cancels a request, the partial results (for an input file, it's
     * all or nothing) may still be available on the specified output location.
     *
     * This call returns immediately and you can
     * use google.longrunning.Operation.name to poll the status of the call.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Location to make a call. Must refer to a caller's project.
     *
     *   Format: `projects/{project-number-or-id}/locations/{location-id}`.
     *
     *   The `global` location is not supported for batch translation.
     *
     *   Only AutoML Translation models or glossaries within the same region (have
     *   the same location-id) can be used, otherwise an INVALID_ARGUMENT (400)
     *   error is returned.
     * @param {string} request.sourceLanguageCode
     *   Required. Source language code. Supported language codes are listed in
     *   [Language
     *   Support](https://cloud.google.com/translate/docs/languages).
     * @param {string[]} request.targetLanguageCodes
     *   Required. Specify up to 10 language codes here. Supported language codes
     *   are listed in [Language
     *   Support](https://cloud.google.com/translate/docs/languages).
     * @param {number[]} [request.models]
     *   Optional. The models to use for translation. Map's key is target language
     *   code. Map's value is model name. Value can be a built-in general model,
     *   or an AutoML Translation model.
     *
     *   The value format depends on model type:
     *
     *   - AutoML Translation models:
     *     `projects/{project-number-or-id}/locations/{location-id}/models/{model-id}`
     *
     *   - General (built-in) models:
     *     `projects/{project-number-or-id}/locations/{location-id}/models/general/nmt`,
     *
     *
     *   If the map is empty or a specific model is
     *   not requested for a language pair, then default google model (nmt) is used.
     * @param {number[]} request.inputConfigs
     *   Required. Input configurations.
     *   The total number of files matched should be <= 100.
     *   The total content size should be <= 100M Unicode codepoints.
     *   The files must use UTF-8 encoding.
     * @param {google.cloud.translation.v3.OutputConfig} request.outputConfig
     *   Required. Output configuration.
     *   If 2 input configs match to the same file (that is, same input path),
     *   we don't generate output for duplicate inputs.
     * @param {number[]} [request.glossaries]
     *   Optional. Glossaries to be applied for translation.
     *   It's keyed by target language code.
     * @param {number[]} [request.labels]
     *   Optional. The labels with user-defined metadata for the request.
     *
     *   Label keys and values can be no longer than 63 characters
     *   (Unicode codepoints), can only contain lowercase letters, numeric
     *   characters, underscores and dashes. International characters are allowed.
     *   Label values are optional. Label keys must start with a letter.
     *
     *   See https://cloud.google.com/translate/docs/advanced/labels for more
     *   information.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.batch_translate_text.js</caption>
     * region_tag:translate_v3_generated_TranslationService_BatchTranslateText_async
     */
    batchTranslateText(request?: protos.google.cloud.translation.v3.IBatchTranslateTextRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.translation.v3.IBatchTranslateResponse, protos.google.cloud.translation.v3.IBatchTranslateMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    batchTranslateText(request: protos.google.cloud.translation.v3.IBatchTranslateTextRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.translation.v3.IBatchTranslateResponse, protos.google.cloud.translation.v3.IBatchTranslateMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    batchTranslateText(request: protos.google.cloud.translation.v3.IBatchTranslateTextRequest, callback: Callback<LROperation<protos.google.cloud.translation.v3.IBatchTranslateResponse, protos.google.cloud.translation.v3.IBatchTranslateMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `batchTranslateText()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.batch_translate_text.js</caption>
     * region_tag:translate_v3_generated_TranslationService_BatchTranslateText_async
     */
    checkBatchTranslateTextProgress(name: string): Promise<LROperation<protos.google.cloud.translation.v3.BatchTranslateResponse, protos.google.cloud.translation.v3.BatchTranslateMetadata>>;
    /**
     * Translates a large volume of document in asynchronous batch mode.
     * This function provides real-time output as the inputs are being processed.
     * If caller cancels a request, the partial results (for an input file, it's
     * all or nothing) may still be available on the specified output location.
     *
     * This call returns immediately and you can use
     * google.longrunning.Operation.name to poll the status of the call.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Location to make a regional call.
     *
     *   Format: `projects/{project-number-or-id}/locations/{location-id}`.
     *
     *   The `global` location is not supported for batch translation.
     *
     *   Only AutoML Translation models or glossaries within the same region (have
     *   the same location-id) can be used, otherwise an INVALID_ARGUMENT (400)
     *   error is returned.
     * @param {string} request.sourceLanguageCode
     *   Required. The ISO-639 language code of the input document if known, for
     *   example, "en-US" or "sr-Latn". Supported language codes are listed in
     *   [Language Support](https://cloud.google.com/translate/docs/languages).
     * @param {string[]} request.targetLanguageCodes
     *   Required. The ISO-639 language code to use for translation of the input
     *   document. Specify up to 10 language codes here. Supported language codes
     *   are listed in [Language
     *   Support](https://cloud.google.com/translate/docs/languages).
     * @param {number[]} request.inputConfigs
     *   Required. Input configurations.
     *   The total number of files matched should be <= 100.
     *   The total content size to translate should be <= 100M Unicode codepoints.
     *   The files must use UTF-8 encoding.
     * @param {google.cloud.translation.v3.BatchDocumentOutputConfig} request.outputConfig
     *   Required. Output configuration.
     *   If 2 input configs match to the same file (that is, same input path),
     *   we don't generate output for duplicate inputs.
     * @param {number[]} [request.models]
     *   Optional. The models to use for translation. Map's key is target language
     *   code. Map's value is the model name. Value can be a built-in general model,
     *   or an AutoML Translation model.
     *
     *   The value format depends on model type:
     *
     *   - AutoML Translation models:
     *     `projects/{project-number-or-id}/locations/{location-id}/models/{model-id}`
     *
     *   - General (built-in) models:
     *     `projects/{project-number-or-id}/locations/{location-id}/models/general/nmt`,
     *
     *
     *   If the map is empty or a specific model is
     *   not requested for a language pair, then default google model (nmt) is used.
     * @param {number[]} [request.glossaries]
     *   Optional. Glossaries to be applied. It's keyed by target language code.
     * @param {number[]} [request.formatConversions]
     *   Optional. The file format conversion map that is applied to all input
     *   files. The map key is the original mime_type. The map value is the target
     *   mime_type of translated documents.
     *
     *   Supported file format conversion includes:
     *   - `application/pdf` to
     *     `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
     *
     *   If nothing specified, output files will be in the same format as the
     *   original file.
     * @param {string} [request.customizedAttribution]
     *   Optional. This flag is to support user customized attribution.
     *   If not provided, the default is `Machine Translated by Google`.
     *   Customized attribution should follow rules in
     *   https://cloud.google.com/translate/attribution#attribution_and_logos
     * @param {boolean} [request.enableShadowRemovalNativePdf]
     *   Optional. If true, use the text removal server to remove the shadow text on
     *   background image for native pdf translation.
     *   Shadow removal feature can only be enabled when
     *   is_translate_native_pdf_only: false && pdf_native_only: false
     * @param {boolean} [request.enableRotationCorrection]
     *   Optional. If true, enable auto rotation correction in DVS.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.batch_translate_document.js</caption>
     * region_tag:translate_v3_generated_TranslationService_BatchTranslateDocument_async
     */
    batchTranslateDocument(request?: protos.google.cloud.translation.v3.IBatchTranslateDocumentRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.translation.v3.IBatchTranslateDocumentResponse, protos.google.cloud.translation.v3.IBatchTranslateDocumentMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    batchTranslateDocument(request: protos.google.cloud.translation.v3.IBatchTranslateDocumentRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.translation.v3.IBatchTranslateDocumentResponse, protos.google.cloud.translation.v3.IBatchTranslateDocumentMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    batchTranslateDocument(request: protos.google.cloud.translation.v3.IBatchTranslateDocumentRequest, callback: Callback<LROperation<protos.google.cloud.translation.v3.IBatchTranslateDocumentResponse, protos.google.cloud.translation.v3.IBatchTranslateDocumentMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `batchTranslateDocument()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.batch_translate_document.js</caption>
     * region_tag:translate_v3_generated_TranslationService_BatchTranslateDocument_async
     */
    checkBatchTranslateDocumentProgress(name: string): Promise<LROperation<protos.google.cloud.translation.v3.BatchTranslateDocumentResponse, protos.google.cloud.translation.v3.BatchTranslateDocumentMetadata>>;
    /**
     * Creates a glossary and returns the long-running operation. Returns
     * NOT_FOUND, if the project doesn't exist.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project name.
     * @param {google.cloud.translation.v3.Glossary} request.glossary
     *   Required. The glossary to create.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.create_glossary.js</caption>
     * region_tag:translate_v3_generated_TranslationService_CreateGlossary_async
     */
    createGlossary(request?: protos.google.cloud.translation.v3.ICreateGlossaryRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.translation.v3.IGlossary, protos.google.cloud.translation.v3.ICreateGlossaryMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    createGlossary(request: protos.google.cloud.translation.v3.ICreateGlossaryRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.translation.v3.IGlossary, protos.google.cloud.translation.v3.ICreateGlossaryMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    createGlossary(request: protos.google.cloud.translation.v3.ICreateGlossaryRequest, callback: Callback<LROperation<protos.google.cloud.translation.v3.IGlossary, protos.google.cloud.translation.v3.ICreateGlossaryMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `createGlossary()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.create_glossary.js</caption>
     * region_tag:translate_v3_generated_TranslationService_CreateGlossary_async
     */
    checkCreateGlossaryProgress(name: string): Promise<LROperation<protos.google.cloud.translation.v3.Glossary, protos.google.cloud.translation.v3.CreateGlossaryMetadata>>;
    /**
     * Updates a glossary. A LRO is used since the update can be async if the
     * glossary's entry file is updated.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {google.cloud.translation.v3.Glossary} request.glossary
     *   Required. The glossary entry to update.
     * @param {google.protobuf.FieldMask} request.updateMask
     *   The list of fields to be updated. Currently only `display_name` and
     *   'input_config'
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.update_glossary.js</caption>
     * region_tag:translate_v3_generated_TranslationService_UpdateGlossary_async
     */
    updateGlossary(request?: protos.google.cloud.translation.v3.IUpdateGlossaryRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.translation.v3.IGlossary, protos.google.cloud.translation.v3.IUpdateGlossaryMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    updateGlossary(request: protos.google.cloud.translation.v3.IUpdateGlossaryRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.translation.v3.IGlossary, protos.google.cloud.translation.v3.IUpdateGlossaryMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    updateGlossary(request: protos.google.cloud.translation.v3.IUpdateGlossaryRequest, callback: Callback<LROperation<protos.google.cloud.translation.v3.IGlossary, protos.google.cloud.translation.v3.IUpdateGlossaryMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `updateGlossary()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.update_glossary.js</caption>
     * region_tag:translate_v3_generated_TranslationService_UpdateGlossary_async
     */
    checkUpdateGlossaryProgress(name: string): Promise<LROperation<protos.google.cloud.translation.v3.Glossary, protos.google.cloud.translation.v3.UpdateGlossaryMetadata>>;
    /**
     * Deletes a glossary, or cancels glossary construction
     * if the glossary isn't created yet.
     * Returns NOT_FOUND, if the glossary doesn't exist.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the glossary to delete.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.delete_glossary.js</caption>
     * region_tag:translate_v3_generated_TranslationService_DeleteGlossary_async
     */
    deleteGlossary(request?: protos.google.cloud.translation.v3.IDeleteGlossaryRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.translation.v3.IDeleteGlossaryResponse, protos.google.cloud.translation.v3.IDeleteGlossaryMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    deleteGlossary(request: protos.google.cloud.translation.v3.IDeleteGlossaryRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.translation.v3.IDeleteGlossaryResponse, protos.google.cloud.translation.v3.IDeleteGlossaryMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    deleteGlossary(request: protos.google.cloud.translation.v3.IDeleteGlossaryRequest, callback: Callback<LROperation<protos.google.cloud.translation.v3.IDeleteGlossaryResponse, protos.google.cloud.translation.v3.IDeleteGlossaryMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `deleteGlossary()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.delete_glossary.js</caption>
     * region_tag:translate_v3_generated_TranslationService_DeleteGlossary_async
     */
    checkDeleteGlossaryProgress(name: string): Promise<LROperation<protos.google.cloud.translation.v3.DeleteGlossaryResponse, protos.google.cloud.translation.v3.DeleteGlossaryMetadata>>;
    /**
     * Creates a Dataset.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project name.
     * @param {google.cloud.translation.v3.Dataset} request.dataset
     *   Required. The Dataset to create.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.create_dataset.js</caption>
     * region_tag:translate_v3_generated_TranslationService_CreateDataset_async
     */
    createDataset(request?: protos.google.cloud.translation.v3.ICreateDatasetRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.translation.v3.IDataset, protos.google.cloud.translation.v3.ICreateDatasetMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    createDataset(request: protos.google.cloud.translation.v3.ICreateDatasetRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.translation.v3.IDataset, protos.google.cloud.translation.v3.ICreateDatasetMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    createDataset(request: protos.google.cloud.translation.v3.ICreateDatasetRequest, callback: Callback<LROperation<protos.google.cloud.translation.v3.IDataset, protos.google.cloud.translation.v3.ICreateDatasetMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `createDataset()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.create_dataset.js</caption>
     * region_tag:translate_v3_generated_TranslationService_CreateDataset_async
     */
    checkCreateDatasetProgress(name: string): Promise<LROperation<protos.google.cloud.translation.v3.Dataset, protos.google.cloud.translation.v3.CreateDatasetMetadata>>;
    /**
     * Deletes a dataset and all of its contents.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the dataset to delete.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.delete_dataset.js</caption>
     * region_tag:translate_v3_generated_TranslationService_DeleteDataset_async
     */
    deleteDataset(request?: protos.google.cloud.translation.v3.IDeleteDatasetRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IDeleteDatasetMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    deleteDataset(request: protos.google.cloud.translation.v3.IDeleteDatasetRequest, options: CallOptions, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IDeleteDatasetMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    deleteDataset(request: protos.google.cloud.translation.v3.IDeleteDatasetRequest, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IDeleteDatasetMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `deleteDataset()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.delete_dataset.js</caption>
     * region_tag:translate_v3_generated_TranslationService_DeleteDataset_async
     */
    checkDeleteDatasetProgress(name: string): Promise<LROperation<protos.google.protobuf.Empty, protos.google.cloud.translation.v3.DeleteDatasetMetadata>>;
    /**
     * Import sentence pairs into translation Dataset.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.dataset
     *   Required. Name of the dataset. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}/datasets/{dataset-id}`
     * @param {google.cloud.translation.v3.DatasetInputConfig} request.inputConfig
     *   Required. The config for the input content.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.import_data.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ImportData_async
     */
    importData(request?: protos.google.cloud.translation.v3.IImportDataRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IImportDataMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    importData(request: protos.google.cloud.translation.v3.IImportDataRequest, options: CallOptions, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IImportDataMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    importData(request: protos.google.cloud.translation.v3.IImportDataRequest, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IImportDataMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `importData()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.import_data.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ImportData_async
     */
    checkImportDataProgress(name: string): Promise<LROperation<protos.google.protobuf.Empty, protos.google.cloud.translation.v3.ImportDataMetadata>>;
    /**
     * Exports dataset's data to the provided output location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.dataset
     *   Required. Name of the dataset. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}/datasets/{dataset-id}`
     * @param {google.cloud.translation.v3.DatasetOutputConfig} request.outputConfig
     *   Required. The config for the output content.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.export_data.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ExportData_async
     */
    exportData(request?: protos.google.cloud.translation.v3.IExportDataRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IExportDataMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    exportData(request: protos.google.cloud.translation.v3.IExportDataRequest, options: CallOptions, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IExportDataMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    exportData(request: protos.google.cloud.translation.v3.IExportDataRequest, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IExportDataMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `exportData()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.export_data.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ExportData_async
     */
    checkExportDataProgress(name: string): Promise<LROperation<protos.google.protobuf.Empty, protos.google.cloud.translation.v3.ExportDataMetadata>>;
    /**
     * Creates a Model.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project name, in form of
     *   `projects/{project}/locations/{location}`
     * @param {google.cloud.translation.v3.Model} request.model
     *   Required. The Model to create.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.create_model.js</caption>
     * region_tag:translate_v3_generated_TranslationService_CreateModel_async
     */
    createModel(request?: protos.google.cloud.translation.v3.ICreateModelRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.cloud.translation.v3.IModel, protos.google.cloud.translation.v3.ICreateModelMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    createModel(request: protos.google.cloud.translation.v3.ICreateModelRequest, options: CallOptions, callback: Callback<LROperation<protos.google.cloud.translation.v3.IModel, protos.google.cloud.translation.v3.ICreateModelMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    createModel(request: protos.google.cloud.translation.v3.ICreateModelRequest, callback: Callback<LROperation<protos.google.cloud.translation.v3.IModel, protos.google.cloud.translation.v3.ICreateModelMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `createModel()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.create_model.js</caption>
     * region_tag:translate_v3_generated_TranslationService_CreateModel_async
     */
    checkCreateModelProgress(name: string): Promise<LROperation<protos.google.cloud.translation.v3.Model, protos.google.cloud.translation.v3.CreateModelMetadata>>;
    /**
     * Deletes a model.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The name of the model to delete.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing
     *   a long running operation. Its `promise()` method returns a promise
     *   you can `await` for.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.delete_model.js</caption>
     * region_tag:translate_v3_generated_TranslationService_DeleteModel_async
     */
    deleteModel(request?: protos.google.cloud.translation.v3.IDeleteModelRequest, options?: CallOptions): Promise<[
        LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IDeleteModelMetadata>,
        protos.google.longrunning.IOperation | undefined,
        {} | undefined
    ]>;
    deleteModel(request: protos.google.cloud.translation.v3.IDeleteModelRequest, options: CallOptions, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IDeleteModelMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    deleteModel(request: protos.google.cloud.translation.v3.IDeleteModelRequest, callback: Callback<LROperation<protos.google.protobuf.IEmpty, protos.google.cloud.translation.v3.IDeleteModelMetadata>, protos.google.longrunning.IOperation | null | undefined, {} | null | undefined>): void;
    /**
     * Check the status of the long running operation returned by `deleteModel()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.delete_model.js</caption>
     * region_tag:translate_v3_generated_TranslationService_DeleteModel_async
     */
    checkDeleteModelProgress(name: string): Promise<LROperation<protos.google.protobuf.Empty, protos.google.cloud.translation.v3.DeleteModelMetadata>>;
    /**
    * Lists glossaries in a project. Returns NOT_FOUND, if the project doesn't
    * exist.
    *
    * @param {Object} request
    *   The request object that will be sent.
    * @param {string} request.parent
    *   Required. The name of the project from which to list all of the glossaries.
    * @param {number} [request.pageSize]
    *   Optional. Requested page size. The server may return fewer glossaries than
    *   requested. If unspecified, the server picks an appropriate default.
    * @param {string} [request.pageToken]
    *   Optional. A token identifying a page of results the server should return.
    *   Typically, this is the value of [ListGlossariesResponse.next_page_token]
    *   returned from the previous call to `ListGlossaries` method.
    *   The first page is returned if `page_token`is empty or missing.
    * @param {string} [request.filter]
    *   Optional. Filter specifying constraints of a list operation.
    *   Specify the constraint by the format of "key=value", where key must be
    *   "src" or "tgt", and the value must be a valid language code.
    *   For multiple restrictions, concatenate them by "AND" (uppercase only),
    *   such as: "src=en-US AND tgt=zh-CN". Notice that the exact match is used
    *   here, which means using 'en-US' and 'en' can lead to different results,
    *   which depends on the language code you used when you create the glossary.
    *   For the unidirectional glossaries, the "src" and "tgt" add restrictions
    *   on the source and target language code separately.
    *   For the equivalent term set glossaries, the "src" and/or "tgt" add
    *   restrictions on the term set.
    *   For example: "src=en-US AND tgt=zh-CN" will only pick the unidirectional
    *   glossaries which exactly match the source language code as "en-US" and the
    *   target language code "zh-CN", but all equivalent term set glossaries which
    *   contain "en-US" and "zh-CN" in their language set will be picked.
    *   If missing, no filtering is performed.
    * @param {object} [options]
    *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
    * @returns {Promise} - The promise which resolves to an array.
    *   The first element of the array is Array of {@link protos.google.cloud.translation.v3.Glossary|Glossary}.
    *   The client library will perform auto-pagination by default: it will call the API as many
    *   times as needed and will merge results from all the pages into this array.
    *   Note that it can affect your quota.
    *   We recommend using `listGlossariesAsync()`
    *   method described below for async iteration which you can stop as needed.
    *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
    *   for more details and examples.
    */
    listGlossaries(request?: protos.google.cloud.translation.v3.IListGlossariesRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IGlossary[],
        protos.google.cloud.translation.v3.IListGlossariesRequest | null,
        protos.google.cloud.translation.v3.IListGlossariesResponse
    ]>;
    listGlossaries(request: protos.google.cloud.translation.v3.IListGlossariesRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.translation.v3.IListGlossariesRequest, protos.google.cloud.translation.v3.IListGlossariesResponse | null | undefined, protos.google.cloud.translation.v3.IGlossary>): void;
    listGlossaries(request: protos.google.cloud.translation.v3.IListGlossariesRequest, callback: PaginationCallback<protos.google.cloud.translation.v3.IListGlossariesRequest, protos.google.cloud.translation.v3.IListGlossariesResponse | null | undefined, protos.google.cloud.translation.v3.IGlossary>): void;
    /**
     * Equivalent to `listGlossaries`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the project from which to list all of the glossaries.
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server may return fewer glossaries than
     *   requested. If unspecified, the server picks an appropriate default.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of [ListGlossariesResponse.next_page_token]
     *   returned from the previous call to `ListGlossaries` method.
     *   The first page is returned if `page_token`is empty or missing.
     * @param {string} [request.filter]
     *   Optional. Filter specifying constraints of a list operation.
     *   Specify the constraint by the format of "key=value", where key must be
     *   "src" or "tgt", and the value must be a valid language code.
     *   For multiple restrictions, concatenate them by "AND" (uppercase only),
     *   such as: "src=en-US AND tgt=zh-CN". Notice that the exact match is used
     *   here, which means using 'en-US' and 'en' can lead to different results,
     *   which depends on the language code you used when you create the glossary.
     *   For the unidirectional glossaries, the "src" and "tgt" add restrictions
     *   on the source and target language code separately.
     *   For the equivalent term set glossaries, the "src" and/or "tgt" add
     *   restrictions on the term set.
     *   For example: "src=en-US AND tgt=zh-CN" will only pick the unidirectional
     *   glossaries which exactly match the source language code as "en-US" and the
     *   target language code "zh-CN", but all equivalent term set glossaries which
     *   contain "en-US" and "zh-CN" in their language set will be picked.
     *   If missing, no filtering is performed.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.Glossary|Glossary} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listGlossariesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listGlossariesStream(request?: protos.google.cloud.translation.v3.IListGlossariesRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listGlossaries`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the project from which to list all of the glossaries.
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server may return fewer glossaries than
     *   requested. If unspecified, the server picks an appropriate default.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of [ListGlossariesResponse.next_page_token]
     *   returned from the previous call to `ListGlossaries` method.
     *   The first page is returned if `page_token`is empty or missing.
     * @param {string} [request.filter]
     *   Optional. Filter specifying constraints of a list operation.
     *   Specify the constraint by the format of "key=value", where key must be
     *   "src" or "tgt", and the value must be a valid language code.
     *   For multiple restrictions, concatenate them by "AND" (uppercase only),
     *   such as: "src=en-US AND tgt=zh-CN". Notice that the exact match is used
     *   here, which means using 'en-US' and 'en' can lead to different results,
     *   which depends on the language code you used when you create the glossary.
     *   For the unidirectional glossaries, the "src" and "tgt" add restrictions
     *   on the source and target language code separately.
     *   For the equivalent term set glossaries, the "src" and/or "tgt" add
     *   restrictions on the term set.
     *   For example: "src=en-US AND tgt=zh-CN" will only pick the unidirectional
     *   glossaries which exactly match the source language code as "en-US" and the
     *   target language code "zh-CN", but all equivalent term set glossaries which
     *   contain "en-US" and "zh-CN" in their language set will be picked.
     *   If missing, no filtering is performed.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.Glossary|Glossary}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_glossaries.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListGlossaries_async
     */
    listGlossariesAsync(request?: protos.google.cloud.translation.v3.IListGlossariesRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.translation.v3.IGlossary>;
    /**
    * List the entries for the glossary.
    *
    * @param {Object} request
    *   The request object that will be sent.
    * @param {string} request.parent
    *   Required. The parent glossary resource name for listing the glossary's
    *   entries.
    * @param {number} [request.pageSize]
    *   Optional. Requested page size. The server may return fewer glossary entries
    *   than requested. If unspecified, the server picks an appropriate default.
    * @param {string} [request.pageToken]
    *   Optional. A token identifying a page of results the server should return.
    *   Typically, this is the value of
    *   [ListGlossaryEntriesResponse.next_page_token] returned from the previous
    *   call. The first page is returned if `page_token`is empty or missing.
    * @param {object} [options]
    *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
    * @returns {Promise} - The promise which resolves to an array.
    *   The first element of the array is Array of {@link protos.google.cloud.translation.v3.GlossaryEntry|GlossaryEntry}.
    *   The client library will perform auto-pagination by default: it will call the API as many
    *   times as needed and will merge results from all the pages into this array.
    *   Note that it can affect your quota.
    *   We recommend using `listGlossaryEntriesAsync()`
    *   method described below for async iteration which you can stop as needed.
    *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
    *   for more details and examples.
    */
    listGlossaryEntries(request?: protos.google.cloud.translation.v3.IListGlossaryEntriesRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IGlossaryEntry[],
        protos.google.cloud.translation.v3.IListGlossaryEntriesRequest | null,
        protos.google.cloud.translation.v3.IListGlossaryEntriesResponse
    ]>;
    listGlossaryEntries(request: protos.google.cloud.translation.v3.IListGlossaryEntriesRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.translation.v3.IListGlossaryEntriesRequest, protos.google.cloud.translation.v3.IListGlossaryEntriesResponse | null | undefined, protos.google.cloud.translation.v3.IGlossaryEntry>): void;
    listGlossaryEntries(request: protos.google.cloud.translation.v3.IListGlossaryEntriesRequest, callback: PaginationCallback<protos.google.cloud.translation.v3.IListGlossaryEntriesRequest, protos.google.cloud.translation.v3.IListGlossaryEntriesResponse | null | undefined, protos.google.cloud.translation.v3.IGlossaryEntry>): void;
    /**
     * Equivalent to `listGlossaryEntries`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The parent glossary resource name for listing the glossary's
     *   entries.
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server may return fewer glossary entries
     *   than requested. If unspecified, the server picks an appropriate default.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   [ListGlossaryEntriesResponse.next_page_token] returned from the previous
     *   call. The first page is returned if `page_token`is empty or missing.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.GlossaryEntry|GlossaryEntry} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listGlossaryEntriesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listGlossaryEntriesStream(request?: protos.google.cloud.translation.v3.IListGlossaryEntriesRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listGlossaryEntries`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The parent glossary resource name for listing the glossary's
     *   entries.
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server may return fewer glossary entries
     *   than requested. If unspecified, the server picks an appropriate default.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   [ListGlossaryEntriesResponse.next_page_token] returned from the previous
     *   call. The first page is returned if `page_token`is empty or missing.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.GlossaryEntry|GlossaryEntry}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_glossary_entries.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListGlossaryEntries_async
     */
    listGlossaryEntriesAsync(request?: protos.google.cloud.translation.v3.IListGlossaryEntriesRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.translation.v3.IGlossaryEntry>;
    /**
    * Lists datasets.
    *
    * @param {Object} request
    *   The request object that will be sent.
    * @param {string} request.parent
    *   Required. Name of the parent project. In form of
    *   `projects/{project-number-or-id}/locations/{location-id}`
    * @param {number} [request.pageSize]
    *   Optional. Requested page size. The server can return fewer results than
    *   requested.
    * @param {string} [request.pageToken]
    *   Optional. A token identifying a page of results for the server to return.
    *   Typically obtained from next_page_token field in the response of a
    *   ListDatasets call.
    * @param {object} [options]
    *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
    * @returns {Promise} - The promise which resolves to an array.
    *   The first element of the array is Array of {@link protos.google.cloud.translation.v3.Dataset|Dataset}.
    *   The client library will perform auto-pagination by default: it will call the API as many
    *   times as needed and will merge results from all the pages into this array.
    *   Note that it can affect your quota.
    *   We recommend using `listDatasetsAsync()`
    *   method described below for async iteration which you can stop as needed.
    *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
    *   for more details and examples.
    */
    listDatasets(request?: protos.google.cloud.translation.v3.IListDatasetsRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IDataset[],
        protos.google.cloud.translation.v3.IListDatasetsRequest | null,
        protos.google.cloud.translation.v3.IListDatasetsResponse
    ]>;
    listDatasets(request: protos.google.cloud.translation.v3.IListDatasetsRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.translation.v3.IListDatasetsRequest, protos.google.cloud.translation.v3.IListDatasetsResponse | null | undefined, protos.google.cloud.translation.v3.IDataset>): void;
    listDatasets(request: protos.google.cloud.translation.v3.IListDatasetsRequest, callback: PaginationCallback<protos.google.cloud.translation.v3.IListDatasetsRequest, protos.google.cloud.translation.v3.IListDatasetsResponse | null | undefined, protos.google.cloud.translation.v3.IDataset>): void;
    /**
     * Equivalent to `listDatasets`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Name of the parent project. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server can return fewer results than
     *   requested.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results for the server to return.
     *   Typically obtained from next_page_token field in the response of a
     *   ListDatasets call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.Dataset|Dataset} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listDatasetsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listDatasetsStream(request?: protos.google.cloud.translation.v3.IListDatasetsRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listDatasets`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Name of the parent project. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server can return fewer results than
     *   requested.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results for the server to return.
     *   Typically obtained from next_page_token field in the response of a
     *   ListDatasets call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.Dataset|Dataset}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_datasets.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListDatasets_async
     */
    listDatasetsAsync(request?: protos.google.cloud.translation.v3.IListDatasetsRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.translation.v3.IDataset>;
    /**
    * Lists all Adaptive MT datasets for which the caller has read permission.
    *
    * @param {Object} request
    *   The request object that will be sent.
    * @param {string} request.parent
    *   Required. The resource name of the project from which to list the Adaptive
    *   MT datasets. `projects/{project-number-or-id}/locations/{location-id}`
    * @param {number} [request.pageSize]
    *   Optional. Requested page size. The server may return fewer results than
    *   requested. If unspecified, the server picks an appropriate default.
    * @param {string} [request.pageToken]
    *   Optional. A token identifying a page of results the server should return.
    *   Typically, this is the value of
    *   ListAdaptiveMtDatasetsResponse.next_page_token returned from the
    *   previous call to `ListAdaptiveMtDatasets` method. The first page is
    *   returned if `page_token`is empty or missing.
    * @param {string} [request.filter]
    *   Optional. An expression for filtering the results of the request.
    *   Filter is not supported yet.
    * @param {object} [options]
    *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
    * @returns {Promise} - The promise which resolves to an array.
    *   The first element of the array is Array of {@link protos.google.cloud.translation.v3.AdaptiveMtDataset|AdaptiveMtDataset}.
    *   The client library will perform auto-pagination by default: it will call the API as many
    *   times as needed and will merge results from all the pages into this array.
    *   Note that it can affect your quota.
    *   We recommend using `listAdaptiveMtDatasetsAsync()`
    *   method described below for async iteration which you can stop as needed.
    *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
    *   for more details and examples.
    */
    listAdaptiveMtDatasets(request?: protos.google.cloud.translation.v3.IListAdaptiveMtDatasetsRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IAdaptiveMtDataset[],
        protos.google.cloud.translation.v3.IListAdaptiveMtDatasetsRequest | null,
        protos.google.cloud.translation.v3.IListAdaptiveMtDatasetsResponse
    ]>;
    listAdaptiveMtDatasets(request: protos.google.cloud.translation.v3.IListAdaptiveMtDatasetsRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.translation.v3.IListAdaptiveMtDatasetsRequest, protos.google.cloud.translation.v3.IListAdaptiveMtDatasetsResponse | null | undefined, protos.google.cloud.translation.v3.IAdaptiveMtDataset>): void;
    listAdaptiveMtDatasets(request: protos.google.cloud.translation.v3.IListAdaptiveMtDatasetsRequest, callback: PaginationCallback<protos.google.cloud.translation.v3.IListAdaptiveMtDatasetsRequest, protos.google.cloud.translation.v3.IListAdaptiveMtDatasetsResponse | null | undefined, protos.google.cloud.translation.v3.IAdaptiveMtDataset>): void;
    /**
     * Equivalent to `listAdaptiveMtDatasets`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the project from which to list the Adaptive
     *   MT datasets. `projects/{project-number-or-id}/locations/{location-id}`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server may return fewer results than
     *   requested. If unspecified, the server picks an appropriate default.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   ListAdaptiveMtDatasetsResponse.next_page_token returned from the
     *   previous call to `ListAdaptiveMtDatasets` method. The first page is
     *   returned if `page_token`is empty or missing.
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the results of the request.
     *   Filter is not supported yet.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.AdaptiveMtDataset|AdaptiveMtDataset} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listAdaptiveMtDatasetsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listAdaptiveMtDatasetsStream(request?: protos.google.cloud.translation.v3.IListAdaptiveMtDatasetsRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listAdaptiveMtDatasets`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the project from which to list the Adaptive
     *   MT datasets. `projects/{project-number-or-id}/locations/{location-id}`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server may return fewer results than
     *   requested. If unspecified, the server picks an appropriate default.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   ListAdaptiveMtDatasetsResponse.next_page_token returned from the
     *   previous call to `ListAdaptiveMtDatasets` method. The first page is
     *   returned if `page_token`is empty or missing.
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the results of the request.
     *   Filter is not supported yet.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.AdaptiveMtDataset|AdaptiveMtDataset}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_adaptive_mt_datasets.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListAdaptiveMtDatasets_async
     */
    listAdaptiveMtDatasetsAsync(request?: protos.google.cloud.translation.v3.IListAdaptiveMtDatasetsRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.translation.v3.IAdaptiveMtDataset>;
    /**
    * Lists all AdaptiveMtFiles associated to an AdaptiveMtDataset.
    *
    * @param {Object} request
    *   The request object that will be sent.
    * @param {string} request.parent
    *   Required. The resource name of the project from which to list the Adaptive
    *   MT files.
    *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
    * @param {number} [request.pageSize]
    *   Optional.
    * @param {string} [request.pageToken]
    *   Optional. A token identifying a page of results the server should return.
    *   Typically, this is the value of
    *   ListAdaptiveMtFilesResponse.next_page_token returned from the
    *   previous call to `ListAdaptiveMtFiles` method. The first page is
    *   returned if `page_token`is empty or missing.
    * @param {object} [options]
    *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
    * @returns {Promise} - The promise which resolves to an array.
    *   The first element of the array is Array of {@link protos.google.cloud.translation.v3.AdaptiveMtFile|AdaptiveMtFile}.
    *   The client library will perform auto-pagination by default: it will call the API as many
    *   times as needed and will merge results from all the pages into this array.
    *   Note that it can affect your quota.
    *   We recommend using `listAdaptiveMtFilesAsync()`
    *   method described below for async iteration which you can stop as needed.
    *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
    *   for more details and examples.
    */
    listAdaptiveMtFiles(request?: protos.google.cloud.translation.v3.IListAdaptiveMtFilesRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IAdaptiveMtFile[],
        protos.google.cloud.translation.v3.IListAdaptiveMtFilesRequest | null,
        protos.google.cloud.translation.v3.IListAdaptiveMtFilesResponse
    ]>;
    listAdaptiveMtFiles(request: protos.google.cloud.translation.v3.IListAdaptiveMtFilesRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.translation.v3.IListAdaptiveMtFilesRequest, protos.google.cloud.translation.v3.IListAdaptiveMtFilesResponse | null | undefined, protos.google.cloud.translation.v3.IAdaptiveMtFile>): void;
    listAdaptiveMtFiles(request: protos.google.cloud.translation.v3.IListAdaptiveMtFilesRequest, callback: PaginationCallback<protos.google.cloud.translation.v3.IListAdaptiveMtFilesRequest, protos.google.cloud.translation.v3.IListAdaptiveMtFilesResponse | null | undefined, protos.google.cloud.translation.v3.IAdaptiveMtFile>): void;
    /**
     * Equivalent to `listAdaptiveMtFiles`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the project from which to list the Adaptive
     *   MT files.
     *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
     * @param {number} [request.pageSize]
     *   Optional.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   ListAdaptiveMtFilesResponse.next_page_token returned from the
     *   previous call to `ListAdaptiveMtFiles` method. The first page is
     *   returned if `page_token`is empty or missing.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.AdaptiveMtFile|AdaptiveMtFile} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listAdaptiveMtFilesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listAdaptiveMtFilesStream(request?: protos.google.cloud.translation.v3.IListAdaptiveMtFilesRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listAdaptiveMtFiles`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the project from which to list the Adaptive
     *   MT files.
     *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
     * @param {number} [request.pageSize]
     *   Optional.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   ListAdaptiveMtFilesResponse.next_page_token returned from the
     *   previous call to `ListAdaptiveMtFiles` method. The first page is
     *   returned if `page_token`is empty or missing.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.AdaptiveMtFile|AdaptiveMtFile}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_adaptive_mt_files.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListAdaptiveMtFiles_async
     */
    listAdaptiveMtFilesAsync(request?: protos.google.cloud.translation.v3.IListAdaptiveMtFilesRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.translation.v3.IAdaptiveMtFile>;
    /**
    * Lists all AdaptiveMtSentences under a given file/dataset.
    *
    * @param {Object} request
    *   The request object that will be sent.
    * @param {string} request.parent
    *   Required. The resource name of the project from which to list the Adaptive
    *   MT files. The following format lists all sentences under a file.
    *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}`
    *   The following format lists all sentences within a dataset.
    *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
    * @param {number} request.pageSize
    * @param {string} request.pageToken
    *   A token identifying a page of results the server should return.
    *   Typically, this is the value of
    *   ListAdaptiveMtSentencesRequest.next_page_token returned from the
    *   previous call to `ListTranslationMemories` method. The first page is
    *   returned if `page_token` is empty or missing.
    * @param {object} [options]
    *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
    * @returns {Promise} - The promise which resolves to an array.
    *   The first element of the array is Array of {@link protos.google.cloud.translation.v3.AdaptiveMtSentence|AdaptiveMtSentence}.
    *   The client library will perform auto-pagination by default: it will call the API as many
    *   times as needed and will merge results from all the pages into this array.
    *   Note that it can affect your quota.
    *   We recommend using `listAdaptiveMtSentencesAsync()`
    *   method described below for async iteration which you can stop as needed.
    *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
    *   for more details and examples.
    */
    listAdaptiveMtSentences(request?: protos.google.cloud.translation.v3.IListAdaptiveMtSentencesRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IAdaptiveMtSentence[],
        protos.google.cloud.translation.v3.IListAdaptiveMtSentencesRequest | null,
        protos.google.cloud.translation.v3.IListAdaptiveMtSentencesResponse
    ]>;
    listAdaptiveMtSentences(request: protos.google.cloud.translation.v3.IListAdaptiveMtSentencesRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.translation.v3.IListAdaptiveMtSentencesRequest, protos.google.cloud.translation.v3.IListAdaptiveMtSentencesResponse | null | undefined, protos.google.cloud.translation.v3.IAdaptiveMtSentence>): void;
    listAdaptiveMtSentences(request: protos.google.cloud.translation.v3.IListAdaptiveMtSentencesRequest, callback: PaginationCallback<protos.google.cloud.translation.v3.IListAdaptiveMtSentencesRequest, protos.google.cloud.translation.v3.IListAdaptiveMtSentencesResponse | null | undefined, protos.google.cloud.translation.v3.IAdaptiveMtSentence>): void;
    /**
     * Equivalent to `listAdaptiveMtSentences`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the project from which to list the Adaptive
     *   MT files. The following format lists all sentences under a file.
     *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}`
     *   The following format lists all sentences within a dataset.
     *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
     * @param {number} request.pageSize
     * @param {string} request.pageToken
     *   A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   ListAdaptiveMtSentencesRequest.next_page_token returned from the
     *   previous call to `ListTranslationMemories` method. The first page is
     *   returned if `page_token` is empty or missing.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.AdaptiveMtSentence|AdaptiveMtSentence} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listAdaptiveMtSentencesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listAdaptiveMtSentencesStream(request?: protos.google.cloud.translation.v3.IListAdaptiveMtSentencesRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listAdaptiveMtSentences`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the project from which to list the Adaptive
     *   MT files. The following format lists all sentences under a file.
     *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}`
     *   The following format lists all sentences within a dataset.
     *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
     * @param {number} request.pageSize
     * @param {string} request.pageToken
     *   A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   ListAdaptiveMtSentencesRequest.next_page_token returned from the
     *   previous call to `ListTranslationMemories` method. The first page is
     *   returned if `page_token` is empty or missing.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.AdaptiveMtSentence|AdaptiveMtSentence}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_adaptive_mt_sentences.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListAdaptiveMtSentences_async
     */
    listAdaptiveMtSentencesAsync(request?: protos.google.cloud.translation.v3.IListAdaptiveMtSentencesRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.translation.v3.IAdaptiveMtSentence>;
    /**
    * Lists sentence pairs in the dataset.
    *
    * @param {Object} request
    *   The request object that will be sent.
    * @param {string} request.parent
    *   Required. Name of the parent dataset. In form of
    *   `projects/{project-number-or-id}/locations/{location-id}/datasets/{dataset-id}`
    * @param {string} [request.filter]
    *   Optional. An expression for filtering the examples that will be returned.
    *   Example filter:
    *   * `usage=TRAIN`
    * @param {number} [request.pageSize]
    *   Optional. Requested page size. The server can return fewer results than
    *   requested.
    * @param {string} [request.pageToken]
    *   Optional. A token identifying a page of results for the server to return.
    *   Typically obtained from next_page_token field in the response of a
    *   ListExamples call.
    * @param {object} [options]
    *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
    * @returns {Promise} - The promise which resolves to an array.
    *   The first element of the array is Array of {@link protos.google.cloud.translation.v3.Example|Example}.
    *   The client library will perform auto-pagination by default: it will call the API as many
    *   times as needed and will merge results from all the pages into this array.
    *   Note that it can affect your quota.
    *   We recommend using `listExamplesAsync()`
    *   method described below for async iteration which you can stop as needed.
    *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
    *   for more details and examples.
    */
    listExamples(request?: protos.google.cloud.translation.v3.IListExamplesRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IExample[],
        protos.google.cloud.translation.v3.IListExamplesRequest | null,
        protos.google.cloud.translation.v3.IListExamplesResponse
    ]>;
    listExamples(request: protos.google.cloud.translation.v3.IListExamplesRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.translation.v3.IListExamplesRequest, protos.google.cloud.translation.v3.IListExamplesResponse | null | undefined, protos.google.cloud.translation.v3.IExample>): void;
    listExamples(request: protos.google.cloud.translation.v3.IListExamplesRequest, callback: PaginationCallback<protos.google.cloud.translation.v3.IListExamplesRequest, protos.google.cloud.translation.v3.IListExamplesResponse | null | undefined, protos.google.cloud.translation.v3.IExample>): void;
    /**
     * Equivalent to `listExamples`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Name of the parent dataset. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}/datasets/{dataset-id}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the examples that will be returned.
     *   Example filter:
     *   * `usage=TRAIN`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server can return fewer results than
     *   requested.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results for the server to return.
     *   Typically obtained from next_page_token field in the response of a
     *   ListExamples call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.Example|Example} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listExamplesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listExamplesStream(request?: protos.google.cloud.translation.v3.IListExamplesRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listExamples`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Name of the parent dataset. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}/datasets/{dataset-id}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the examples that will be returned.
     *   Example filter:
     *   * `usage=TRAIN`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server can return fewer results than
     *   requested.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results for the server to return.
     *   Typically obtained from next_page_token field in the response of a
     *   ListExamples call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.Example|Example}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_examples.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListExamples_async
     */
    listExamplesAsync(request?: protos.google.cloud.translation.v3.IListExamplesRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.translation.v3.IExample>;
    /**
    * Lists models.
    *
    * @param {Object} request
    *   The request object that will be sent.
    * @param {string} request.parent
    *   Required. Name of the parent project. In form of
    *   `projects/{project-number-or-id}/locations/{location-id}`
    * @param {string} [request.filter]
    *   Optional. An expression for filtering the models that will be returned.
    *   Supported filter:
    *   `dataset_id=${dataset_id}`
    * @param {number} [request.pageSize]
    *   Optional. Requested page size. The server can return fewer results than
    *   requested.
    * @param {string} [request.pageToken]
    *   Optional. A token identifying a page of results for the server to return.
    *   Typically obtained from next_page_token field in the response of a
    *   ListModels call.
    * @param {object} [options]
    *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
    * @returns {Promise} - The promise which resolves to an array.
    *   The first element of the array is Array of {@link protos.google.cloud.translation.v3.Model|Model}.
    *   The client library will perform auto-pagination by default: it will call the API as many
    *   times as needed and will merge results from all the pages into this array.
    *   Note that it can affect your quota.
    *   We recommend using `listModelsAsync()`
    *   method described below for async iteration which you can stop as needed.
    *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
    *   for more details and examples.
    */
    listModels(request?: protos.google.cloud.translation.v3.IListModelsRequest, options?: CallOptions): Promise<[
        protos.google.cloud.translation.v3.IModel[],
        protos.google.cloud.translation.v3.IListModelsRequest | null,
        protos.google.cloud.translation.v3.IListModelsResponse
    ]>;
    listModels(request: protos.google.cloud.translation.v3.IListModelsRequest, options: CallOptions, callback: PaginationCallback<protos.google.cloud.translation.v3.IListModelsRequest, protos.google.cloud.translation.v3.IListModelsResponse | null | undefined, protos.google.cloud.translation.v3.IModel>): void;
    listModels(request: protos.google.cloud.translation.v3.IListModelsRequest, callback: PaginationCallback<protos.google.cloud.translation.v3.IListModelsRequest, protos.google.cloud.translation.v3.IListModelsResponse | null | undefined, protos.google.cloud.translation.v3.IModel>): void;
    /**
     * Equivalent to `listModels`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Name of the parent project. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the models that will be returned.
     *   Supported filter:
     *   `dataset_id=${dataset_id}`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server can return fewer results than
     *   requested.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results for the server to return.
     *   Typically obtained from next_page_token field in the response of a
     *   ListModels call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.Model|Model} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listModelsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listModelsStream(request?: protos.google.cloud.translation.v3.IListModelsRequest, options?: CallOptions): Transform;
    /**
     * Equivalent to `listModels`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Name of the parent project. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the models that will be returned.
     *   Supported filter:
     *   `dataset_id=${dataset_id}`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server can return fewer results than
     *   requested.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results for the server to return.
     *   Typically obtained from next_page_token field in the response of a
     *   ListModels call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.Model|Model}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_models.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListModels_async
     */
    listModelsAsync(request?: protos.google.cloud.translation.v3.IListModelsRequest, options?: CallOptions): AsyncIterable<protos.google.cloud.translation.v3.IModel>;
    /**
     * Gets the access control policy for a resource. Returns an empty policy
     * if the resource exists and does not have a policy set.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {Object} [request.options]
     *   OPTIONAL: A `GetPolicyOptions` object for specifying options to
     *   `GetIamPolicy`. This field is only used by Cloud IAM.
     *
     *   This object should have the same structure as {@link google.iam.v1.GetPolicyOptions | GetPolicyOptions}.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.Policy | Policy}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.Policy | Policy}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    getIamPolicy(request: IamProtos.google.iam.v1.GetIamPolicyRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.GetIamPolicyRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.GetIamPolicyRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.Policy]>;
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    setIamPolicy(request: IamProtos.google.iam.v1.SetIamPolicyRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.SetIamPolicyRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.Policy, IamProtos.google.iam.v1.SetIamPolicyRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.Policy]>;
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     *
     */
    testIamPermissions(request: IamProtos.google.iam.v1.TestIamPermissionsRequest, options?: gax.CallOptions | Callback<IamProtos.google.iam.v1.TestIamPermissionsResponse, IamProtos.google.iam.v1.TestIamPermissionsRequest | null | undefined, {} | null | undefined>, callback?: Callback<IamProtos.google.iam.v1.TestIamPermissionsResponse, IamProtos.google.iam.v1.TestIamPermissionsRequest | null | undefined, {} | null | undefined>): Promise<[IamProtos.google.iam.v1.TestIamPermissionsResponse]>;
    /**
       * Gets information about a location.
       *
       * @param {Object} request
       *   The request object that will be sent.
       * @param {string} request.name
       *   Resource name for the location.
       * @param {object} [options]
       *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html | CallOptions} for more details.
       * @returns {Promise} - The promise which resolves to an array.
       *   The first element of the array is an object representing {@link google.cloud.location.Location | Location}.
       *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
       *   for more details and examples.
       * @example
       * ```
       * const [response] = await client.getLocation(request);
       * ```
       */
    getLocation(request: LocationProtos.google.cloud.location.IGetLocationRequest, options?: gax.CallOptions | Callback<LocationProtos.google.cloud.location.ILocation, LocationProtos.google.cloud.location.IGetLocationRequest | null | undefined, {} | null | undefined>, callback?: Callback<LocationProtos.google.cloud.location.ILocation, LocationProtos.google.cloud.location.IGetLocationRequest | null | undefined, {} | null | undefined>): Promise<LocationProtos.google.cloud.location.ILocation>;
    /**
       * Lists information about the supported locations for this service. Returns an iterable object.
       *
       * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
       * @param {Object} request
       *   The request object that will be sent.
       * @param {string} request.name
       *   The resource that owns the locations collection, if applicable.
       * @param {string} request.filter
       *   The standard list filter.
       * @param {number} request.pageSize
       *   The standard list page size.
       * @param {string} request.pageToken
       *   The standard list page token.
       * @param {object} [options]
       *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
       * @returns {Object}
       *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
       *   When you iterate the returned iterable, each element will be an object representing
       *   {@link google.cloud.location.Location | Location}. The API will be called under the hood as needed, once per the page,
       *   so you can stop the iteration when you don't need more results.
       *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
       *   for more details and examples.
       * @example
       * ```
       * const iterable = client.listLocationsAsync(request);
       * for await (const response of iterable) {
       *   // process response
       * }
       * ```
       */
    listLocationsAsync(request: LocationProtos.google.cloud.location.IListLocationsRequest, options?: CallOptions): AsyncIterable<LocationProtos.google.cloud.location.ILocation>;
    /**
       * Gets the latest state of a long-running operation.  Clients can use this
       * method to poll the operation result at intervals as recommended by the API
       * service.
       *
       * @param {Object} request - The request object that will be sent.
       * @param {string} request.name - The name of the operation resource.
       * @param {Object=} options
       *   Optional parameters. You can override the default settings for this call,
       *   e.g, timeout, retries, paginations, etc. See {@link
       *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
       *   for the details.
       * @param {function(?Error, ?Object)=} callback
       *   The function which will be called with the result of the API call.
       *
       *   The second parameter to the callback is an object representing
       *   {@link google.longrunning.Operation | google.longrunning.Operation}.
       * @return {Promise} - The promise which resolves to an array.
       *   The first element of the array is an object representing
       * {@link google.longrunning.Operation | google.longrunning.Operation}.
       * The promise has a method named "cancel" which cancels the ongoing API call.
       *
       * @example
       * ```
       * const client = longrunning.operationsClient();
       * const name = '';
       * const [response] = await client.getOperation({name});
       * // doThingsWith(response)
       * ```
       */
    getOperation(request: protos.google.longrunning.GetOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.longrunning.Operation, protos.google.longrunning.GetOperationRequest, {} | null | undefined>, callback?: Callback<protos.google.longrunning.Operation, protos.google.longrunning.GetOperationRequest, {} | null | undefined>): Promise<[protos.google.longrunning.Operation]>;
    /**
     * Lists operations that match the specified filter in the request. If the
     * server doesn't support this method, it returns `UNIMPLEMENTED`. Returns an iterable object.
     *
     * For-await-of syntax is used with the iterable to recursively get response element on-demand.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation collection.
     * @param {string} request.filter - The standard list filter.
     * @param {number=} request.pageSize -
     *   The maximum number of resources contained in the underlying API
     *   response. If page streaming is performed per-resource, this
     *   parameter does not affect the return value. If page streaming is
     *   performed per-page, this determines the maximum number of
     *   resources in a page.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     *   details.
     * @returns {Object}
     *   An iterable Object that conforms to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | iteration protocols}.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * for await (const response of client.listOperationsAsync(request));
     * // doThingsWith(response)
     * ```
     */
    listOperationsAsync(request: protos.google.longrunning.ListOperationsRequest, options?: gax.CallOptions): AsyncIterable<protos.google.longrunning.IOperation>;
    /**
     * Starts asynchronous cancellation on a long-running operation.  The server
     * makes a best effort to cancel the operation, but success is not
     * guaranteed.  If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.  Clients can use
     * {@link Operations.GetOperation} or
     * other methods to check whether the cancellation succeeded or whether the
     * operation completed despite cancellation. On successful cancellation,
     * the operation is not deleted; instead, it becomes an operation with
     * an {@link Operation.error} value with a {@link google.rpc.Status.code} of
     * 1, corresponding to `Code.CANCELLED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be cancelled.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     * details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.cancelOperation({name: ''});
     * ```
     */
    cancelOperation(request: protos.google.longrunning.CancelOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.longrunning.CancelOperationRequest, protos.google.protobuf.Empty, {} | undefined | null>, callback?: Callback<protos.google.longrunning.CancelOperationRequest, protos.google.protobuf.Empty, {} | undefined | null>): Promise<protos.google.protobuf.Empty>;
    /**
     * Deletes a long-running operation. This method indicates that the client is
     * no longer interested in the operation result. It does not cancel the
     * operation. If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be deleted.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     * for the details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.deleteOperation({name: ''});
     * ```
     */
    deleteOperation(request: protos.google.longrunning.DeleteOperationRequest, optionsOrCallback?: gax.CallOptions | Callback<protos.google.protobuf.Empty, protos.google.longrunning.DeleteOperationRequest, {} | null | undefined>, callback?: Callback<protos.google.protobuf.Empty, protos.google.longrunning.DeleteOperationRequest, {} | null | undefined>): Promise<protos.google.protobuf.Empty>;
    /**
     * Return a fully-qualified adaptiveMtDataset resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @returns {string} Resource name string.
     */
    adaptiveMtDatasetPath(project: string, location: string, dataset: string): string;
    /**
     * Parse the project from AdaptiveMtDataset resource.
     *
     * @param {string} adaptiveMtDatasetName
     *   A fully-qualified path representing AdaptiveMtDataset resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAdaptiveMtDatasetName(adaptiveMtDatasetName: string): string | number;
    /**
     * Parse the location from AdaptiveMtDataset resource.
     *
     * @param {string} adaptiveMtDatasetName
     *   A fully-qualified path representing AdaptiveMtDataset resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAdaptiveMtDatasetName(adaptiveMtDatasetName: string): string | number;
    /**
     * Parse the dataset from AdaptiveMtDataset resource.
     *
     * @param {string} adaptiveMtDatasetName
     *   A fully-qualified path representing AdaptiveMtDataset resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAdaptiveMtDatasetName(adaptiveMtDatasetName: string): string | number;
    /**
     * Return a fully-qualified adaptiveMtFile resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} file
     * @returns {string} Resource name string.
     */
    adaptiveMtFilePath(project: string, location: string, dataset: string, file: string): string;
    /**
     * Parse the project from AdaptiveMtFile resource.
     *
     * @param {string} adaptiveMtFileName
     *   A fully-qualified path representing AdaptiveMtFile resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAdaptiveMtFileName(adaptiveMtFileName: string): string | number;
    /**
     * Parse the location from AdaptiveMtFile resource.
     *
     * @param {string} adaptiveMtFileName
     *   A fully-qualified path representing AdaptiveMtFile resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAdaptiveMtFileName(adaptiveMtFileName: string): string | number;
    /**
     * Parse the dataset from AdaptiveMtFile resource.
     *
     * @param {string} adaptiveMtFileName
     *   A fully-qualified path representing AdaptiveMtFile resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAdaptiveMtFileName(adaptiveMtFileName: string): string | number;
    /**
     * Parse the file from AdaptiveMtFile resource.
     *
     * @param {string} adaptiveMtFileName
     *   A fully-qualified path representing AdaptiveMtFile resource.
     * @returns {string} A string representing the file.
     */
    matchFileFromAdaptiveMtFileName(adaptiveMtFileName: string): string | number;
    /**
     * Return a fully-qualified adaptiveMtSentence resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} file
     * @param {string} sentence
     * @returns {string} Resource name string.
     */
    adaptiveMtSentencePath(project: string, location: string, dataset: string, file: string, sentence: string): string;
    /**
     * Parse the project from AdaptiveMtSentence resource.
     *
     * @param {string} adaptiveMtSentenceName
     *   A fully-qualified path representing AdaptiveMtSentence resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAdaptiveMtSentenceName(adaptiveMtSentenceName: string): string | number;
    /**
     * Parse the location from AdaptiveMtSentence resource.
     *
     * @param {string} adaptiveMtSentenceName
     *   A fully-qualified path representing AdaptiveMtSentence resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAdaptiveMtSentenceName(adaptiveMtSentenceName: string): string | number;
    /**
     * Parse the dataset from AdaptiveMtSentence resource.
     *
     * @param {string} adaptiveMtSentenceName
     *   A fully-qualified path representing AdaptiveMtSentence resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAdaptiveMtSentenceName(adaptiveMtSentenceName: string): string | number;
    /**
     * Parse the file from AdaptiveMtSentence resource.
     *
     * @param {string} adaptiveMtSentenceName
     *   A fully-qualified path representing AdaptiveMtSentence resource.
     * @returns {string} A string representing the file.
     */
    matchFileFromAdaptiveMtSentenceName(adaptiveMtSentenceName: string): string | number;
    /**
     * Parse the sentence from AdaptiveMtSentence resource.
     *
     * @param {string} adaptiveMtSentenceName
     *   A fully-qualified path representing AdaptiveMtSentence resource.
     * @returns {string} A string representing the sentence.
     */
    matchSentenceFromAdaptiveMtSentenceName(adaptiveMtSentenceName: string): string | number;
    /**
     * Return a fully-qualified dataset resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @returns {string} Resource name string.
     */
    datasetPath(project: string, location: string, dataset: string): string;
    /**
     * Parse the project from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDatasetName(datasetName: string): string | number;
    /**
     * Parse the location from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDatasetName(datasetName: string): string | number;
    /**
     * Parse the dataset from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDatasetName(datasetName: string): string | number;
    /**
     * Return a fully-qualified example resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} example
     * @returns {string} Resource name string.
     */
    examplePath(project: string, location: string, dataset: string, example: string): string;
    /**
     * Parse the project from Example resource.
     *
     * @param {string} exampleName
     *   A fully-qualified path representing Example resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromExampleName(exampleName: string): string | number;
    /**
     * Parse the location from Example resource.
     *
     * @param {string} exampleName
     *   A fully-qualified path representing Example resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromExampleName(exampleName: string): string | number;
    /**
     * Parse the dataset from Example resource.
     *
     * @param {string} exampleName
     *   A fully-qualified path representing Example resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromExampleName(exampleName: string): string | number;
    /**
     * Parse the example from Example resource.
     *
     * @param {string} exampleName
     *   A fully-qualified path representing Example resource.
     * @returns {string} A string representing the example.
     */
    matchExampleFromExampleName(exampleName: string): string | number;
    /**
     * Return a fully-qualified glossary resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} glossary
     * @returns {string} Resource name string.
     */
    glossaryPath(project: string, location: string, glossary: string): string;
    /**
     * Parse the project from Glossary resource.
     *
     * @param {string} glossaryName
     *   A fully-qualified path representing Glossary resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromGlossaryName(glossaryName: string): string | number;
    /**
     * Parse the location from Glossary resource.
     *
     * @param {string} glossaryName
     *   A fully-qualified path representing Glossary resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromGlossaryName(glossaryName: string): string | number;
    /**
     * Parse the glossary from Glossary resource.
     *
     * @param {string} glossaryName
     *   A fully-qualified path representing Glossary resource.
     * @returns {string} A string representing the glossary.
     */
    matchGlossaryFromGlossaryName(glossaryName: string): string | number;
    /**
     * Return a fully-qualified glossaryEntry resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} glossary
     * @param {string} glossary_entry
     * @returns {string} Resource name string.
     */
    glossaryEntryPath(project: string, location: string, glossary: string, glossaryEntry: string): string;
    /**
     * Parse the project from GlossaryEntry resource.
     *
     * @param {string} glossaryEntryName
     *   A fully-qualified path representing GlossaryEntry resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromGlossaryEntryName(glossaryEntryName: string): string | number;
    /**
     * Parse the location from GlossaryEntry resource.
     *
     * @param {string} glossaryEntryName
     *   A fully-qualified path representing GlossaryEntry resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromGlossaryEntryName(glossaryEntryName: string): string | number;
    /**
     * Parse the glossary from GlossaryEntry resource.
     *
     * @param {string} glossaryEntryName
     *   A fully-qualified path representing GlossaryEntry resource.
     * @returns {string} A string representing the glossary.
     */
    matchGlossaryFromGlossaryEntryName(glossaryEntryName: string): string | number;
    /**
     * Parse the glossary_entry from GlossaryEntry resource.
     *
     * @param {string} glossaryEntryName
     *   A fully-qualified path representing GlossaryEntry resource.
     * @returns {string} A string representing the glossary_entry.
     */
    matchGlossaryEntryFromGlossaryEntryName(glossaryEntryName: string): string | number;
    /**
     * Return a fully-qualified location resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    locationPath(project: string, location: string): string;
    /**
     * Parse the project from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromLocationName(locationName: string): string | number;
    /**
     * Parse the location from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromLocationName(locationName: string): string | number;
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(project: string, location: string, model: string): string;
    /**
     * Parse the project from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelName(modelName: string): string | number;
    /**
     * Parse the location from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelName(modelName: string): string | number;
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName: string): string | number;
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close(): Promise<void>;
}
