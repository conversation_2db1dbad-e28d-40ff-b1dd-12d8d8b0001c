[{"D:\\DataGen\\authkey-chat-new\\src\\index.js": "1", "D:\\DataGen\\authkey-chat-new\\src\\App.js": "2", "D:\\DataGen\\authkey-chat-new\\src\\context\\ChatContext.js": "3", "D:\\DataGen\\authkey-chat-new\\src\\context\\AuthContext.js": "4", "D:\\DataGen\\authkey-chat-new\\src\\context\\AllProviders.js": "5", "D:\\DataGen\\authkey-chat-new\\src\\utils\\Utils.js": "6", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Login.jsx": "7", "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentManagementPage.jsx": "8", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Home.jsx": "9", "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentReportPage.jsx": "10", "D:\\DataGen\\authkey-chat-new\\src\\pages\\CreateAgentPage.jsx": "11", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Setting.jsx": "12", "D:\\DataGen\\authkey-chat-new\\src\\components\\VerifyAccount.jsx": "13", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\CustomerView.jsx": "14", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\Customers.jsx": "15", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Report\\Report.jsx": "16", "D:\\DataGen\\authkey-chat-new\\src\\components\\BroadcastReport\\BroadcastReport.jsx": "17", "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentReport\\AgentReport.jsx": "18", "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentSettings\\AgentSettings.jsx": "19", "D:\\DataGen\\authkey-chat-new\\src\\api\\api.js": "20", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\CreateNewPipeline.jsx": "21", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LeadsDashboard.jsx": "22", "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentDetails\\AgentDetailPage.jsx": "23", "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentBreakModal\\AgentBreakStatusCard.jsx": "24", "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerReport\\CustomerReport.jsx": "25", "D:\\DataGen\\authkey-chat-new\\src\\components\\LeftMenu.jsx": "26", "D:\\DataGen\\authkey-chat-new\\src\\components\\Navbar.jsx": "27", "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentManagement.jsx": "28", "D:\\DataGen\\authkey-chat-new\\src\\components\\Offline.jsx": "29", "D:\\DataGen\\authkey-chat-new\\src\\components\\Chat.jsx": "30", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useDebounce.js": "31", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useMaskNo.js": "32", "D:\\DataGen\\authkey-chat-new\\src\\components\\Sidebar.jsx": "33", "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\ActivityLog.jsx": "34", "D:\\DataGen\\authkey-chat-new\\src\\components\\ui-conponents\\NewChatCall.jsx": "35", "D:\\DataGen\\authkey-chat-new\\src\\components\\Contactdetailcard\\ContactDetailCard.jsx": "36", "D:\\DataGen\\authkey-chat-new\\src\\components\\QuickReply\\QuickReply.jsx": "37", "D:\\DataGen\\authkey-chat-new\\src\\components\\faq\\FaqPanel.jsx": "38", "D:\\DataGen\\authkey-chat-new\\src\\components\\Notescard\\NotesCard.jsx": "39", "D:\\DataGen\\authkey-chat-new\\src\\components\\faq\\Faqsettings.jsx": "40", "D:\\DataGen\\authkey-chat-new\\src\\components\\Labels\\LabelCard.jsx": "41", "D:\\DataGen\\authkey-chat-new\\src\\components\\NewDashboard\\NewDashboard.jsx": "42", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LabelSelector.jsx": "43", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\StageCard.jsx": "44", "D:\\DataGen\\authkey-chat-new\\src\\components\\AutoReply\\AutoReplyRules.jsx": "45", "D:\\DataGen\\authkey-chat-new\\src\\components\\DeleteModal\\DeleteModal.jsx": "46", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LabelColumn.jsx": "47", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\ContactCard.jsx": "48", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\FilterSortBar.jsx": "49", "D:\\DataGen\\authkey-chat-new\\src\\components\\Broadcast\\Broadcast.jsx": "50", "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerReport\\AssignModal.jsx": "51", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useBlockUser.js": "52", "D:\\DataGen\\authkey-chat-new\\src\\components\\Messages.jsx": "53", "D:\\DataGen\\authkey-chat-new\\src\\components\\Search.jsx": "54", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useReminders.js": "55", "D:\\DataGen\\authkey-chat-new\\src\\components\\Welcome.jsx": "56", "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentBreakModal\\AgentBreakModal.jsx": "57", "D:\\DataGen\\authkey-chat-new\\src\\components\\Chats.jsx": "58", "D:\\DataGen\\authkey-chat-new\\src\\components\\ChangePassword.jsx": "59", "D:\\DataGen\\authkey-chat-new\\src\\components\\bookmark\\Bookmark.js": "60", "D:\\DataGen\\authkey-chat-new\\src\\components\\Labels\\Labels.jsx": "61", "D:\\DataGen\\authkey-chat-new\\src\\components\\Reminder\\Reminder.jsx": "62", "D:\\DataGen\\authkey-chat-new\\src\\components\\Reminder\\RemiderToast.jsx": "63", "D:\\DataGen\\authkey-chat-new\\src\\components\\profile\\UserProfile.jsx": "64", "D:\\DataGen\\authkey-chat-new\\src\\components\\InputBar\\Inputbar.jsx": "65", "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\ContactActivityPopup.jsx": "66", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\ChartSection.jsx": "67", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\Table.jsx": "68", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\TodayStats.jsx": "69", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\OverviewCard.jsx": "70", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\ChatOverviewCard.jsx": "71", "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerHistory\\CustomerHistoryModal.jsx": "72", "D:\\DataGen\\authkey-chat-new\\src\\components\\TemplatePrev.jsx": "73", "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useSentences.js": "74", "D:\\DataGen\\authkey-chat-new\\src\\components\\Message.jsx": "75", "D:\\DataGen\\authkey-chat-new\\src\\components\\BlockCard.jsx": "76", "D:\\DataGen\\authkey-chat-new\\src\\components\\Input.jsx": "77", "D:\\DataGen\\authkey-chat-new\\src\\components\\WaitingCard\\WaitingCard.jsx": "78", "D:\\DataGen\\authkey-chat-new\\src\\components\\ReplyPreview\\ReplyPreview.jsx": "79", "D:\\DataGen\\authkey-chat-new\\src\\components\\SendTemplate.jsx": "80", "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\MessageCard.jsx": "81", "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\ReplyDropdown.jsx": "82", "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\OrderModal.jsx": "83", "D:\\DataGen\\authkey-chat-new\\src\\components\\MenuList\\MenuList.jsx": "84"}, {"size": 772, "mtime": 1753168233939, "results": "85", "hashOfConfig": "86"}, {"size": 8668, "mtime": 1753168233729, "results": "87", "hashOfConfig": "86"}, {"size": 807, "mtime": 1753168233936, "results": "88", "hashOfConfig": "86"}, {"size": 719, "mtime": 1753168233936, "results": "89", "hashOfConfig": "86"}, {"size": 11215, "mtime": 1753168233936, "results": "90", "hashOfConfig": "86"}, {"size": 4276, "mtime": 1753168233951, "results": "91", "hashOfConfig": "86"}, {"size": 7124, "mtime": 1753168233948, "results": "92", "hashOfConfig": "86"}, {"size": 496, "mtime": 1753168233940, "results": "93", "hashOfConfig": "86"}, {"size": 8589, "mtime": 1753168233944, "results": "94", "hashOfConfig": "86"}, {"size": 11705, "mtime": 1753168233941, "results": "95", "hashOfConfig": "86"}, {"size": 13433, "mtime": 1753168233942, "results": "96", "hashOfConfig": "86"}, {"size": 36897, "mtime": 1753168233950, "results": "97", "hashOfConfig": "86"}, {"size": 18151, "mtime": 1753168233929, "results": "98", "hashOfConfig": "86"}, {"size": 11949, "mtime": 1753168233943, "results": "99", "hashOfConfig": "86"}, {"size": 34651, "mtime": 1753168233943, "results": "100", "hashOfConfig": "86"}, {"size": 7225, "mtime": 1753168233949, "results": "101", "hashOfConfig": "86"}, {"size": 8527, "mtime": 1753168233911, "results": "102", "hashOfConfig": "86"}, {"size": 5737, "mtime": 1753168233908, "results": "103", "hashOfConfig": "86"}, {"size": 7865, "mtime": 1753168233941, "results": "104", "hashOfConfig": "86"}, {"size": 670, "mtime": 1753168233732, "results": "105", "hashOfConfig": "86"}, {"size": 11153, "mtime": 1753168233946, "results": "106", "hashOfConfig": "86"}, {"size": 21545, "mtime": 1753168233947, "results": "107", "hashOfConfig": "86"}, {"size": 29861, "mtime": 1753168233940, "results": "108", "hashOfConfig": "86"}, {"size": 3983, "mtime": 1753168233907, "results": "109", "hashOfConfig": "86"}, {"size": 35976, "mtime": 1753168233915, "results": "110", "hashOfConfig": "86"}, {"size": 9016, "mtime": 1753168233918, "results": "111", "hashOfConfig": "86"}, {"size": 2426, "mtime": 1753168233921, "results": "112", "hashOfConfig": "86"}, {"size": 51998, "mtime": 1753168233908, "results": "113", "hashOfConfig": "86"}, {"size": 720, "mtime": 1753168233922, "results": "114", "hashOfConfig": "86"}, {"size": 19809, "mtime": 1753168233912, "results": "115", "hashOfConfig": "86"}, {"size": 422, "mtime": 1753168233937, "results": "116", "hashOfConfig": "86"}, {"size": 415, "mtime": 1753168233937, "results": "117", "hashOfConfig": "86"}, {"size": 47488, "mtime": 1753168233928, "results": "118", "hashOfConfig": "86"}, {"size": 3273, "mtime": 1753168233943, "results": "119", "hashOfConfig": "86"}, {"size": 5900, "mtime": 1753168233935, "results": "120", "hashOfConfig": "86"}, {"size": 15841, "mtime": 1753168233914, "results": "121", "hashOfConfig": "86"}, {"size": 23234, "mtime": 1753168233923, "results": "122", "hashOfConfig": "86"}, {"size": 4387, "mtime": 1753168233932, "results": "123", "hashOfConfig": "86"}, {"size": 12941, "mtime": 1753168233922, "results": "124", "hashOfConfig": "86"}, {"size": 14032, "mtime": 1753168233932, "results": "125", "hashOfConfig": "86"}, {"size": 1807, "mtime": 1753168233917, "results": "126", "hashOfConfig": "86"}, {"size": 10754, "mtime": 1753168233921, "results": "127", "hashOfConfig": "86"}, {"size": 1911, "mtime": 1753168233947, "results": "128", "hashOfConfig": "86"}, {"size": 1641, "mtime": 1753168233948, "results": "129", "hashOfConfig": "86"}, {"size": 8940, "mtime": 1753168233909, "results": "130", "hashOfConfig": "86"}, {"size": 894, "mtime": 1753168233916, "results": "131", "hashOfConfig": "86"}, {"size": 1864, "mtime": 1753168233946, "results": "132", "hashOfConfig": "86"}, {"size": 4796, "mtime": 1753168233945, "results": "133", "hashOfConfig": "86"}, {"size": 5430, "mtime": 1753168233946, "results": "134", "hashOfConfig": "86"}, {"size": 34730, "mtime": 1753168233910, "results": "135", "hashOfConfig": "86"}, {"size": 1411, "mtime": 1753168233915, "results": "136", "hashOfConfig": "86"}, {"size": 1863, "mtime": 1753168233937, "results": "137", "hashOfConfig": "86"}, {"size": 3571, "mtime": 1753168233921, "results": "138", "hashOfConfig": "86"}, {"size": 3661, "mtime": 1753168233927, "results": "139", "hashOfConfig": "86"}, {"size": 3345, "mtime": 1753168233938, "results": "140", "hashOfConfig": "86"}, {"size": 1092, "mtime": 1753168233930, "results": "141", "hashOfConfig": "86"}, {"size": 6060, "mtime": 1753168233907, "results": "142", "hashOfConfig": "86"}, {"size": 65894, "mtime": 1753168233913, "results": "143", "hashOfConfig": "86"}, {"size": 5114, "mtime": 1753168233911, "results": "144", "hashOfConfig": "86"}, {"size": 6474, "mtime": 1753168233932, "results": "145", "hashOfConfig": "86"}, {"size": 2511, "mtime": 1753168233917, "results": "146", "hashOfConfig": "86"}, {"size": 23187, "mtime": 1753168233924, "results": "147", "hashOfConfig": "86"}, {"size": 1727, "mtime": 1753168233923, "results": "148", "hashOfConfig": "86"}, {"size": 6231, "mtime": 1753168233933, "results": "149", "hashOfConfig": "86"}, {"size": 8559, "mtime": 1753169842830, "results": "150", "hashOfConfig": "86"}, {"size": 3229, "mtime": 1753168233945, "results": "151", "hashOfConfig": "86"}, {"size": 1903, "mtime": 1753168233926, "results": "152", "hashOfConfig": "86"}, {"size": 7548, "mtime": 1753168233927, "results": "153", "hashOfConfig": "86"}, {"size": 4672, "mtime": 1753168233927, "results": "154", "hashOfConfig": "86"}, {"size": 1749, "mtime": 1753168233926, "results": "155", "hashOfConfig": "86"}, {"size": 6574, "mtime": 1753168233926, "results": "156", "hashOfConfig": "86"}, {"size": 4590, "mtime": 1753168233914, "results": "157", "hashOfConfig": "86"}, {"size": 5836, "mtime": 1753168233929, "results": "158", "hashOfConfig": "86"}, {"size": 3091, "mtime": 1753168233938, "results": "159", "hashOfConfig": "86"}, {"size": 23211, "mtime": 1753168233919, "results": "160", "hashOfConfig": "86"}, {"size": 886, "mtime": 1753168233910, "results": "161", "hashOfConfig": "86"}, {"size": 41156, "mtime": 1753168233916, "results": "162", "hashOfConfig": "86"}, {"size": 5348, "mtime": 1753168233930, "results": "163", "hashOfConfig": "86"}, {"size": 6572, "mtime": 1753168233925, "results": "164", "hashOfConfig": "86"}, {"size": 14476, "mtime": 1753168233928, "results": "165", "hashOfConfig": "86"}, {"size": 15277, "mtime": 1753168233920, "results": "166", "hashOfConfig": "86"}, {"size": 519, "mtime": 1753168233920, "results": "167", "hashOfConfig": "86"}, {"size": 1780, "mtime": 1753168233920, "results": "168", "hashOfConfig": "86"}, {"size": 6056, "mtime": 1753168233919, "results": "169", "hashOfConfig": "86"}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18psi8c", {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\DataGen\\authkey-chat-new\\src\\index.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\App.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\context\\ChatContext.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\context\\AuthContext.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\context\\AllProviders.js", ["422"], [], "D:\\DataGen\\authkey-chat-new\\src\\utils\\Utils.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Login.jsx", ["423"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentManagementPage.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Home.jsx", ["424", "425", "426"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentReportPage.jsx", ["427", "428", "429", "430", "431"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\CreateAgentPage.jsx", ["432", "433"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Setting.jsx", ["434", "435", "436", "437", "438", "439", "440", "441", "442", "443"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\VerifyAccount.jsx", ["444"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\CustomerView.jsx", ["445", "446", "447"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\Customers.jsx", ["448", "449", "450", "451", "452", "453", "454", "455"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Report\\Report.jsx", ["456", "457"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\BroadcastReport\\BroadcastReport.jsx", ["458", "459"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentReport\\AgentReport.jsx", ["460"], ["461"], "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentSettings\\AgentSettings.jsx", ["462", "463"], [], "D:\\DataGen\\authkey-chat-new\\src\\api\\api.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\CreateNewPipeline.jsx", ["464"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LeadsDashboard.jsx", ["465", "466", "467", "468", "469"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\AgentDetails\\AgentDetailPage.jsx", ["470", "471", "472", "473", "474", "475", "476", "477", "478"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentBreakModal\\AgentBreakStatusCard.jsx", ["479"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerReport\\CustomerReport.jsx", ["480", "481", "482", "483", "484", "485", "486", "487", "488"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\LeftMenu.jsx", ["489"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Navbar.jsx", ["490"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentManagement.jsx", ["491", "492", "493", "494", "495", "496"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Offline.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Chat.jsx", ["497", "498", "499", "500", "501"], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useDebounce.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useMaskNo.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Sidebar.jsx", ["502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\Customers\\ActivityLog.jsx", ["517"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ui-conponents\\NewChatCall.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Contactdetailcard\\ContactDetailCard.jsx", ["518"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\QuickReply\\QuickReply.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\faq\\FaqPanel.jsx", ["519"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Notescard\\NotesCard.jsx", ["520"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\faq\\Faqsettings.jsx", ["521", "522"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Labels\\LabelCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\NewDashboard\\NewDashboard.jsx", ["523"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LabelSelector.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\StageCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AutoReply\\AutoReplyRules.jsx", ["524"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\DeleteModal\\DeleteModal.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\LabelColumn.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\ContactCard.jsx", ["525", "526", "527", "528"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\FilterSortBar.jsx", ["529", "530"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Broadcast\\Broadcast.jsx", ["531", "532", "533", "534", "535"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerReport\\AssignModal.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useBlockUser.js", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Messages.jsx", ["536"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Search.jsx", ["537"], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useReminders.js", ["538"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Welcome.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\AgentBreakModal\\AgentBreakModal.jsx", ["539"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Chats.jsx", ["540", "541", "542", "543", "544", "545", "546", "547", "548"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ChangePassword.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\bookmark\\Bookmark.js", ["549"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Labels\\Labels.jsx", ["550", "551", "552", "553", "554", "555"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Reminder\\Reminder.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Reminder\\RemiderToast.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\profile\\UserProfile.jsx", ["556", "557", "558", "559", "560"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\InputBar\\Inputbar.jsx", ["561", "562", "563", "564", "565"], [], "D:\\DataGen\\authkey-chat-new\\src\\pages\\LeadsDashboard\\ContactActivityPopup.jsx", ["566"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\ChartSection.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\Table.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\TodayStats.jsx", ["567", "568", "569", "570", "571"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\OverviewCard.jsx", ["572"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReportSections\\ChatOverviewCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\CustomerHistory\\CustomerHistoryModal.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\TemplatePrev.jsx", ["573"], [], "D:\\DataGen\\authkey-chat-new\\src\\customHooks\\useSentences.js", ["574", "575", "576"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Message.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\BlockCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Input.jsx", ["577", "578", "579", "580", "581", "582", "583"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\WaitingCard\\WaitingCard.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\ReplyPreview\\ReplyPreview.jsx", ["584"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\SendTemplate.jsx", ["585"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\MessageCard.jsx", ["586", "587"], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\ReplyDropdown.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\Messagecard\\OrderModal.jsx", [], [], "D:\\DataGen\\authkey-chat-new\\src\\components\\MenuList\\MenuList.jsx", [], [], {"ruleId": "588", "severity": 1, "message": "589", "line": 13, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 13, "endColumn": 17}, {"ruleId": "588", "severity": 1, "message": "592", "line": 1, "column": 17, "nodeType": "590", "messageId": "591", "endLine": 1, "endColumn": 26}, {"ruleId": "593", "severity": 1, "message": "594", "line": 60, "column": 6, "nodeType": "595", "endLine": 60, "endColumn": 19, "suggestions": "596"}, {"ruleId": "593", "severity": 1, "message": "597", "line": 101, "column": 6, "nodeType": "595", "endLine": 101, "endColumn": 27, "suggestions": "598"}, {"ruleId": "593", "severity": 1, "message": "599", "line": 166, "column": 5, "nodeType": "595", "endLine": 166, "endColumn": 73, "suggestions": "600"}, {"ruleId": "588", "severity": 1, "message": "601", "line": 9, "column": 8, "nodeType": "590", "messageId": "591", "endLine": 9, "endColumn": 14}, {"ruleId": "588", "severity": 1, "message": "602", "line": 33, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 33, "endColumn": 22}, {"ruleId": "593", "severity": 1, "message": "603", "line": 187, "column": 6, "nodeType": "595", "endLine": 187, "endColumn": 19, "suggestions": "604"}, {"ruleId": "593", "severity": 1, "message": "605", "line": 191, "column": 6, "nodeType": "595", "endLine": 191, "endColumn": 19, "suggestions": "606"}, {"ruleId": "593", "severity": 1, "message": "607", "line": 238, "column": 6, "nodeType": "595", "endLine": 238, "endColumn": 8, "suggestions": "608"}, {"ruleId": "588", "severity": 1, "message": "609", "line": 30, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 30, "endColumn": 21}, {"ruleId": "593", "severity": 1, "message": "610", "line": 35, "column": 6, "nodeType": "595", "endLine": 35, "endColumn": 8, "suggestions": "611"}, {"ruleId": "588", "severity": 1, "message": "612", "line": 17, "column": 26, "nodeType": "590", "messageId": "591", "endLine": 17, "endColumn": 43}, {"ruleId": "588", "severity": 1, "message": "613", "line": 43, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 43, "endColumn": 22}, {"ruleId": "588", "severity": 1, "message": "614", "line": 43, "column": 24, "nodeType": "590", "messageId": "591", "endLine": 43, "endColumn": 39}, {"ruleId": "588", "severity": 1, "message": "615", "line": 107, "column": 9, "nodeType": "590", "messageId": "591", "endLine": 107, "endColumn": 37}, {"ruleId": "588", "severity": 1, "message": "616", "line": 113, "column": 9, "nodeType": "590", "messageId": "591", "endLine": 113, "endColumn": 28}, {"ruleId": "588", "severity": 1, "message": "617", "line": 117, "column": 9, "nodeType": "590", "messageId": "591", "endLine": 117, "endColumn": 31}, {"ruleId": "593", "severity": 1, "message": "618", "line": 207, "column": 6, "nodeType": "595", "endLine": 207, "endColumn": 35, "suggestions": "619"}, {"ruleId": "593", "severity": 1, "message": "620", "line": 317, "column": 6, "nodeType": "595", "endLine": 317, "endColumn": 19, "suggestions": "621"}, {"ruleId": "588", "severity": 1, "message": "622", "line": 337, "column": 9, "nodeType": "590", "messageId": "591", "endLine": 337, "endColumn": 15}, {"ruleId": "588", "severity": 1, "message": "623", "line": 354, "column": 9, "nodeType": "590", "messageId": "591", "endLine": 354, "endColumn": 18}, {"ruleId": "593", "severity": 1, "message": "624", "line": 30, "column": 6, "nodeType": "595", "endLine": 30, "endColumn": 8, "suggestions": "625"}, {"ruleId": "588", "severity": 1, "message": "626", "line": 16, "column": 26, "nodeType": "590", "messageId": "591", "endLine": 16, "endColumn": 41}, {"ruleId": "588", "severity": 1, "message": "589", "line": 36, "column": 12, "nodeType": "590", "messageId": "591", "endLine": 36, "endColumn": 19}, {"ruleId": "593", "severity": 1, "message": "627", "line": 44, "column": 8, "nodeType": "595", "endLine": 44, "endColumn": 29, "suggestions": "628"}, {"ruleId": "588", "severity": 1, "message": "629", "line": 2, "column": 51, "nodeType": "590", "messageId": "591", "endLine": 2, "endColumn": 62}, {"ruleId": "588", "severity": 1, "message": "630", "line": 32, "column": 12, "nodeType": "590", "messageId": "591", "endLine": 32, "endColumn": 26}, {"ruleId": "593", "severity": 1, "message": "605", "line": 71, "column": 8, "nodeType": "595", "endLine": 71, "endColumn": 39, "suggestions": "631"}, {"ruleId": "593", "severity": 1, "message": "632", "line": 132, "column": 8, "nodeType": "595", "endLine": 132, "endColumn": 30, "suggestions": "633"}, {"ruleId": "588", "severity": 1, "message": "634", "line": 330, "column": 11, "nodeType": "590", "messageId": "591", "endLine": 330, "endColumn": 26}, {"ruleId": "588", "severity": 1, "message": "635", "line": 348, "column": 11, "nodeType": "590", "messageId": "591", "endLine": 348, "endColumn": 22}, {"ruleId": "588", "severity": 1, "message": "636", "line": 363, "column": 11, "nodeType": "590", "messageId": "591", "endLine": 363, "endColumn": 25}, {"ruleId": "588", "severity": 1, "message": "637", "line": 434, "column": 19, "nodeType": "590", "messageId": "591", "endLine": 434, "endColumn": 27}, {"ruleId": "588", "severity": 1, "message": "638", "line": 5, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 5, "endColumn": 20}, {"ruleId": "588", "severity": 1, "message": "639", "line": 14, "column": 11, "nodeType": "590", "messageId": "591", "endLine": 14, "endColumn": 22}, {"ruleId": "593", "severity": 1, "message": "640", "line": 29, "column": 8, "nodeType": "595", "endLine": 29, "endColumn": 21, "suggestions": "641"}, {"ruleId": "593", "severity": 1, "message": "642", "line": 32, "column": 8, "nodeType": "595", "endLine": 32, "endColumn": 42, "suggestions": "643"}, {"ruleId": "593", "severity": 1, "message": "644", "line": 28, "column": 6, "nodeType": "595", "endLine": 28, "endColumn": 19, "suggestions": "645"}, {"ruleId": "593", "severity": 1, "message": "646", "line": 33, "column": 6, "nodeType": "595", "endLine": 33, "endColumn": 40, "suggestions": "647", "suppressions": "648"}, {"ruleId": "588", "severity": 1, "message": "649", "line": 24, "column": 19, "nodeType": "590", "messageId": "591", "endLine": 24, "endColumn": 29}, {"ruleId": "593", "severity": 1, "message": "650", "line": 39, "column": 6, "nodeType": "595", "endLine": 39, "endColumn": 28, "suggestions": "651"}, {"ruleId": "588", "severity": 1, "message": "652", "line": 23, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 23, "endColumn": 26}, {"ruleId": "588", "severity": 1, "message": "653", "line": 14, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 14, "endColumn": 19}, {"ruleId": "654", "severity": 1, "message": "655", "line": 65, "column": 76, "nodeType": "656", "messageId": "657", "endLine": 65, "endColumn": 78}, {"ruleId": "658", "severity": 1, "message": "659", "line": 68, "column": 9, "nodeType": "660", "messageId": "661", "endLine": 74, "endColumn": 10}, {"ruleId": "593", "severity": 1, "message": "662", "line": 205, "column": 8, "nodeType": "595", "endLine": 205, "endColumn": 39, "suggestions": "663"}, {"ruleId": "593", "severity": 1, "message": "664", "line": 210, "column": 8, "nodeType": "595", "endLine": 210, "endColumn": 21, "suggestions": "665"}, {"ruleId": "588", "severity": 1, "message": "666", "line": 3, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 3, "endColumn": 14}, {"ruleId": "588", "severity": 1, "message": "667", "line": 5, "column": 5, "nodeType": "590", "messageId": "591", "endLine": 5, "endColumn": 15}, {"ruleId": "588", "severity": 1, "message": "668", "line": 7, "column": 5, "nodeType": "590", "messageId": "591", "endLine": 7, "endColumn": 12}, {"ruleId": "588", "severity": 1, "message": "669", "line": 22, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 22, "endColumn": 22}, {"ruleId": "588", "severity": 1, "message": "670", "line": 23, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 23, "endColumn": 14}, {"ruleId": "588", "severity": 1, "message": "652", "line": 24, "column": 18, "nodeType": "590", "messageId": "591", "endLine": 24, "endColumn": 34}, {"ruleId": "588", "severity": 1, "message": "671", "line": 53, "column": 11, "nodeType": "590", "messageId": "591", "endLine": 53, "endColumn": 16}, {"ruleId": "593", "severity": 1, "message": "672", "line": 104, "column": 8, "nodeType": "595", "endLine": 104, "endColumn": 48, "suggestions": "673"}, {"ruleId": "588", "severity": 1, "message": "674", "line": 123, "column": 11, "nodeType": "590", "messageId": "591", "endLine": 123, "endColumn": 21}, {"ruleId": "593", "severity": 1, "message": "675", "line": 50, "column": 6, "nodeType": "595", "endLine": 50, "endColumn": 17, "suggestions": "676"}, {"ruleId": "588", "severity": 1, "message": "677", "line": 3, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 3, "endColumn": 18}, {"ruleId": "588", "severity": 1, "message": "630", "line": 33, "column": 12, "nodeType": "590", "messageId": "591", "endLine": 33, "endColumn": 26}, {"ruleId": "588", "severity": 1, "message": "678", "line": 33, "column": 28, "nodeType": "590", "messageId": "591", "endLine": 33, "endColumn": 45}, {"ruleId": "588", "severity": 1, "message": "679", "line": 34, "column": 12, "nodeType": "590", "messageId": "591", "endLine": 34, "endColumn": 25}, {"ruleId": "588", "severity": 1, "message": "680", "line": 34, "column": 27, "nodeType": "590", "messageId": "591", "endLine": 34, "endColumn": 43}, {"ruleId": "588", "severity": 1, "message": "681", "line": 48, "column": 11, "nodeType": "590", "messageId": "591", "endLine": 48, "endColumn": 31}, {"ruleId": "593", "severity": 1, "message": "682", "line": 81, "column": 8, "nodeType": "595", "endLine": 81, "endColumn": 21, "suggestions": "683"}, {"ruleId": "593", "severity": 1, "message": "684", "line": 87, "column": 8, "nodeType": "595", "endLine": 87, "endColumn": 39, "suggestions": "685"}, {"ruleId": "588", "severity": 1, "message": "637", "line": 414, "column": 19, "nodeType": "590", "messageId": "591", "endLine": 414, "endColumn": 27}, {"ruleId": "588", "severity": 1, "message": "686", "line": 8, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 8, "endColumn": 24}, {"ruleId": "588", "severity": 1, "message": "687", "line": 6, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 6, "endColumn": 20}, {"ruleId": "588", "severity": 1, "message": "688", "line": 42, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 42, "endColumn": 23}, {"ruleId": "588", "severity": 1, "message": "609", "line": 44, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 44, "endColumn": 21}, {"ruleId": "593", "severity": 1, "message": "610", "line": 120, "column": 6, "nodeType": "595", "endLine": 120, "endColumn": 27, "suggestions": "689"}, {"ruleId": "593", "severity": 1, "message": "690", "line": 182, "column": 6, "nodeType": "595", "endLine": 182, "endColumn": 19, "suggestions": "691"}, {"ruleId": "588", "severity": 1, "message": "692", "line": 491, "column": 9, "nodeType": "590", "messageId": "591", "endLine": 491, "endColumn": 25}, {"ruleId": "588", "severity": 1, "message": "693", "line": 529, "column": 9, "nodeType": "590", "messageId": "591", "endLine": 529, "endColumn": 23}, {"ruleId": "588", "severity": 1, "message": "694", "line": 19, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 19, "endColumn": 15}, {"ruleId": "588", "severity": 1, "message": "695", "line": 24, "column": 24, "nodeType": "590", "messageId": "591", "endLine": 24, "endColumn": 34}, {"ruleId": "593", "severity": 1, "message": "696", "line": 60, "column": 6, "nodeType": "595", "endLine": 60, "endColumn": 21, "suggestions": "697"}, {"ruleId": "593", "severity": 1, "message": "698", "line": 149, "column": 6, "nodeType": "595", "endLine": 149, "endColumn": 8, "suggestions": "699"}, {"ruleId": "593", "severity": 1, "message": "700", "line": 176, "column": 6, "nodeType": "595", "endLine": 176, "endColumn": 41, "suggestions": "701"}, {"ruleId": "588", "severity": 1, "message": "686", "line": 22, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 22, "endColumn": 24}, {"ruleId": "588", "severity": 1, "message": "702", "line": 28, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 28, "endColumn": 18}, {"ruleId": "588", "severity": 1, "message": "703", "line": 29, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 29, "endColumn": 24}, {"ruleId": "588", "severity": 1, "message": "704", "line": 55, "column": 5, "nodeType": "590", "messageId": "591", "endLine": 55, "endColumn": 15}, {"ruleId": "588", "severity": 1, "message": "705", "line": 56, "column": 5, "nodeType": "590", "messageId": "591", "endLine": 56, "endColumn": 12}, {"ruleId": "588", "severity": 1, "message": "706", "line": 58, "column": 5, "nodeType": "590", "messageId": "591", "endLine": 58, "endColumn": 12}, {"ruleId": "588", "severity": 1, "message": "707", "line": 59, "column": 5, "nodeType": "590", "messageId": "591", "endLine": 59, "endColumn": 15}, {"ruleId": "588", "severity": 1, "message": "708", "line": 65, "column": 5, "nodeType": "590", "messageId": "591", "endLine": 65, "endColumn": 22}, {"ruleId": "588", "severity": 1, "message": "709", "line": 68, "column": 5, "nodeType": "590", "messageId": "591", "endLine": 68, "endColumn": 14}, {"ruleId": "588", "severity": 1, "message": "710", "line": 77, "column": 5, "nodeType": "590", "messageId": "591", "endLine": 77, "endColumn": 20}, {"ruleId": "588", "severity": 1, "message": "711", "line": 79, "column": 5, "nodeType": "590", "messageId": "591", "endLine": 79, "endColumn": 18}, {"ruleId": "593", "severity": 1, "message": "664", "line": 99, "column": 6, "nodeType": "595", "endLine": 99, "endColumn": 19, "suggestions": "712"}, {"ruleId": "593", "severity": 1, "message": "713", "line": 285, "column": 6, "nodeType": "595", "endLine": 285, "endColumn": 81, "suggestions": "714"}, {"ruleId": "588", "severity": 1, "message": "715", "line": 287, "column": 9, "nodeType": "590", "messageId": "591", "endLine": 287, "endColumn": 31}, {"ruleId": "588", "severity": 1, "message": "716", "line": 426, "column": 9, "nodeType": "590", "messageId": "591", "endLine": 426, "endColumn": 19}, {"ruleId": "717", "severity": 1, "message": "718", "line": 61, "column": 54, "nodeType": "719", "endLine": 61, "endColumn": 83}, {"ruleId": "717", "severity": 1, "message": "718", "line": 216, "column": 17, "nodeType": "719", "endLine": 216, "endColumn": 57}, {"ruleId": "588", "severity": 1, "message": "720", "line": 11, "column": 30, "nodeType": "590", "messageId": "591", "endLine": 11, "endColumn": 49}, {"ruleId": "588", "severity": 1, "message": "592", "line": 5, "column": 20, "nodeType": "590", "messageId": "591", "endLine": 5, "endColumn": 29}, {"ruleId": "588", "severity": 1, "message": "721", "line": 19, "column": 12, "nodeType": "590", "messageId": "591", "endLine": 19, "endColumn": 23}, {"ruleId": "588", "severity": 1, "message": "722", "line": 19, "column": 25, "nodeType": "590", "messageId": "591", "endLine": 19, "endColumn": 39}, {"ruleId": "588", "severity": 1, "message": "723", "line": 2, "column": 8, "nodeType": "590", "messageId": "591", "endLine": 2, "endColumn": 20}, {"ruleId": "588", "severity": 1, "message": "724", "line": 3, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 3, "endColumn": 13}, {"ruleId": "588", "severity": 1, "message": "592", "line": 1, "column": 35, "nodeType": "590", "messageId": "591", "endLine": 1, "endColumn": 44}, {"ruleId": "588", "severity": 1, "message": "725", "line": 8, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 8, "endColumn": 35}, {"ruleId": "588", "severity": 1, "message": "726", "line": 8, "column": 37, "nodeType": "590", "messageId": "591", "endLine": 8, "endColumn": 45}, {"ruleId": "588", "severity": 1, "message": "727", "line": 40, "column": 9, "nodeType": "590", "messageId": "591", "endLine": 40, "endColumn": 25}, {"ruleId": "588", "severity": 1, "message": "728", "line": 3, "column": 8, "nodeType": "590", "messageId": "591", "endLine": 3, "endColumn": 14}, {"ruleId": "588", "severity": 1, "message": "729", "line": 21, "column": 11, "nodeType": "590", "messageId": "591", "endLine": 21, "endColumn": 23}, {"ruleId": "588", "severity": 1, "message": "730", "line": 18, "column": 12, "nodeType": "590", "messageId": "591", "endLine": 18, "endColumn": 24}, {"ruleId": "588", "severity": 1, "message": "731", "line": 18, "column": 26, "nodeType": "590", "messageId": "591", "endLine": 18, "endColumn": 41}, {"ruleId": "588", "severity": 1, "message": "732", "line": 41, "column": 12, "nodeType": "590", "messageId": "591", "endLine": 41, "endColumn": 23}, {"ruleId": "588", "severity": 1, "message": "733", "line": 41, "column": 25, "nodeType": "590", "messageId": "591", "endLine": 41, "endColumn": 39}, {"ruleId": "593", "severity": 1, "message": "734", "line": 52, "column": 8, "nodeType": "595", "endLine": 52, "endColumn": 21, "suggestions": "735"}, {"ruleId": "588", "severity": 1, "message": "677", "line": 7, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 7, "endColumn": 18}, {"ruleId": "593", "severity": 1, "message": "632", "line": 28, "column": 6, "nodeType": "595", "endLine": 28, "endColumn": 18, "suggestions": "736"}, {"ruleId": "593", "severity": 1, "message": "737", "line": 54, "column": 8, "nodeType": "595", "endLine": 54, "endColumn": 54, "suggestions": "738"}, {"ruleId": "588", "severity": 1, "message": "592", "line": 1, "column": 27, "nodeType": "590", "messageId": "591", "endLine": 1, "endColumn": 36}, {"ruleId": "588", "severity": 1, "message": "739", "line": 12, "column": 8, "nodeType": "590", "messageId": "591", "endLine": 12, "endColumn": 19}, {"ruleId": "740", "severity": 1, "message": "741", "line": 16, "column": 16, "nodeType": "742", "messageId": "657", "endLine": 16, "endColumn": 19}, {"ruleId": "588", "severity": 1, "message": "743", "line": 27, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 27, "endColumn": 22}, {"ruleId": "588", "severity": 1, "message": "744", "line": 32, "column": 5, "nodeType": "590", "messageId": "591", "endLine": 32, "endColumn": 18}, {"ruleId": "588", "severity": 1, "message": "745", "line": 54, "column": 5, "nodeType": "590", "messageId": "591", "endLine": 54, "endColumn": 18}, {"ruleId": "588", "severity": 1, "message": "746", "line": 57, "column": 5, "nodeType": "590", "messageId": "591", "endLine": 57, "endColumn": 15}, {"ruleId": "593", "severity": 1, "message": "747", "line": 660, "column": 6, "nodeType": "595", "endLine": 660, "endColumn": 117, "suggestions": "748"}, {"ruleId": "593", "severity": 1, "message": "749", "line": 792, "column": 6, "nodeType": "595", "endLine": 792, "endColumn": 44, "suggestions": "750"}, {"ruleId": "593", "severity": 1, "message": "751", "line": 827, "column": 6, "nodeType": "595", "endLine": 827, "endColumn": 19, "suggestions": "752"}, {"ruleId": "588", "severity": 1, "message": "753", "line": 6, "column": 12, "nodeType": "590", "messageId": "591", "endLine": 6, "endColumn": 23}, {"ruleId": "588", "severity": 1, "message": "592", "line": 1, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 1, "endColumn": 19}, {"ruleId": "588", "severity": 1, "message": "754", "line": 1, "column": 31, "nodeType": "590", "messageId": "591", "endLine": 1, "endColumn": 41}, {"ruleId": "588", "severity": 1, "message": "755", "line": 3, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 3, "endColumn": 19}, {"ruleId": "588", "severity": 1, "message": "756", "line": 4, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 4, "endColumn": 21}, {"ruleId": "588", "severity": 1, "message": "757", "line": 7, "column": 20, "nodeType": "590", "messageId": "591", "endLine": 7, "endColumn": 26}, {"ruleId": "588", "severity": 1, "message": "758", "line": 10, "column": 12, "nodeType": "590", "messageId": "591", "endLine": 10, "endColumn": 25}, {"ruleId": "588", "severity": 1, "message": "759", "line": 6, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 6, "endColumn": 26}, {"ruleId": "588", "severity": 1, "message": "760", "line": 15, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 15, "endColumn": 22}, {"ruleId": "593", "severity": 1, "message": "761", "line": 32, "column": 6, "nodeType": "595", "endLine": 32, "endColumn": 19, "suggestions": "762"}, {"ruleId": "588", "severity": 1, "message": "763", "line": 33, "column": 9, "nodeType": "590", "messageId": "591", "endLine": 33, "endColumn": 18}, {"ruleId": "588", "severity": 1, "message": "764", "line": 98, "column": 9, "nodeType": "590", "messageId": "591", "endLine": 98, "endColumn": 27}, {"ruleId": "588", "severity": 1, "message": "765", "line": 108, "column": 15, "nodeType": "590", "messageId": "591", "endLine": 108, "endColumn": 19}, {"ruleId": "593", "severity": 1, "message": "766", "line": 127, "column": 6, "nodeType": "595", "endLine": 127, "endColumn": 25, "suggestions": "767"}, {"ruleId": "717", "severity": 1, "message": "718", "line": 206, "column": 9, "nodeType": "719", "endLine": 206, "endColumn": 43}, {"ruleId": "717", "severity": 1, "message": "718", "line": 207, "column": 9, "nodeType": "719", "endLine": 207, "endColumn": 43}, {"ruleId": "717", "severity": 1, "message": "718", "line": 208, "column": 9, "nodeType": "719", "endLine": 208, "endColumn": 43}, {"ruleId": "593", "severity": 1, "message": "768", "line": 51, "column": 5, "nodeType": "595", "endLine": 51, "endColumn": 24, "suggestions": "769"}, {"ruleId": "588", "severity": 1, "message": "770", "line": 1, "column": 56, "nodeType": "590", "messageId": "591", "endLine": 1, "endColumn": 64}, {"ruleId": "588", "severity": 1, "message": "771", "line": 1, "column": 66, "nodeType": "590", "messageId": "591", "endLine": 1, "endColumn": 69}, {"ruleId": "588", "severity": 1, "message": "772", "line": 1, "column": 71, "nodeType": "590", "messageId": "591", "endLine": 1, "endColumn": 75}, {"ruleId": "588", "severity": 1, "message": "773", "line": 42, "column": 11, "nodeType": "590", "messageId": "591", "endLine": 42, "endColumn": 23}, {"ruleId": "588", "severity": 1, "message": "774", "line": 45, "column": 11, "nodeType": "590", "messageId": "591", "endLine": 45, "endColumn": 17}, {"ruleId": "588", "severity": 1, "message": "775", "line": 6, "column": 8, "nodeType": "590", "messageId": "591", "endLine": 6, "endColumn": 11}, {"ruleId": "588", "severity": 1, "message": "765", "line": 55, "column": 13, "nodeType": "590", "messageId": "591", "endLine": 55, "endColumn": 17}, {"ruleId": "588", "severity": 1, "message": "776", "line": 1, "column": 8, "nodeType": "590", "messageId": "591", "endLine": 1, "endColumn": 13}, {"ruleId": "588", "severity": 1, "message": "777", "line": 2, "column": 8, "nodeType": "590", "messageId": "591", "endLine": 2, "endColumn": 13}, {"ruleId": "593", "severity": 1, "message": "778", "line": 48, "column": 8, "nodeType": "595", "endLine": 48, "endColumn": 28, "suggestions": "779"}, {"ruleId": "588", "severity": 1, "message": "780", "line": 8, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 8, "endColumn": 21}, {"ruleId": "588", "severity": 1, "message": "781", "line": 16, "column": 11, "nodeType": "590", "messageId": "591", "endLine": 16, "endColumn": 24}, {"ruleId": "588", "severity": 1, "message": "782", "line": 16, "column": 45, "nodeType": "590", "messageId": "591", "endLine": 16, "endColumn": 72}, {"ruleId": "588", "severity": 1, "message": "783", "line": 17, "column": 54, "nodeType": "590", "messageId": "591", "endLine": 17, "endColumn": 68}, {"ruleId": "588", "severity": 1, "message": "784", "line": 104, "column": 15, "nodeType": "590", "messageId": "591", "endLine": 104, "endColumn": 18}, {"ruleId": "593", "severity": 1, "message": "785", "line": 435, "column": 6, "nodeType": "595", "endLine": 435, "endColumn": 8, "suggestions": "786"}, {"ruleId": "593", "severity": 1, "message": "787", "line": 472, "column": 6, "nodeType": "595", "endLine": 472, "endColumn": 20, "suggestions": "788"}, {"ruleId": "789", "severity": 1, "message": "790", "line": 73, "column": 25, "nodeType": "719", "endLine": 73, "endColumn": 97}, {"ruleId": "593", "severity": 1, "message": "734", "line": 34, "column": 6, "nodeType": "595", "endLine": 34, "endColumn": 19, "suggestions": "791"}, {"ruleId": "588", "severity": 1, "message": "792", "line": 8, "column": 10, "nodeType": "590", "messageId": "591", "endLine": 8, "endColumn": 29}, {"ruleId": "588", "severity": 1, "message": "793", "line": 37, "column": 21, "nodeType": "590", "messageId": "591", "endLine": 37, "endColumn": 26}, "no-unused-vars", "'loading' is assigned a value but never used.", "Identifier", "unusedVar", "'useEffect' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'requestPermission'. Either include it or remove the dependency array.", "ArrayExpression", ["794"], "React Hook useEffect has a missing dependency: 'setCallData'. Either include it or remove the dependency array.", ["795"], "React Hook useCallback has unnecessary dependencies: 'showAllReminder' and 'showReminder'. Either exclude them or remove the dependency array.", ["796"], "'Navbar' is defined but never used.", "'chatsLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'graphReport'. Either include it or remove the dependency array.", ["797"], "React Hook useEffect has a missing dependency: 'fetchNewContactList'. Either include it or remove the dependency array.", ["798"], "React Hook useEffect has a missing dependency: 'getWhatsaAppNumberList'. Either include it or remove the dependency array.", ["799"], "'userBalance' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'Balance'. Either include it or remove the dependency array.", ["800"], "'setSelectedOption' is assigned a value but never used.", "'contactLists' is assigned a value but never used.", "'setContactLists' is assigned a value but never used.", "'handleManualAssignmentChange' is assigned a value but never used.", "'addManualAssignment' is assigned a value but never used.", "'removeManualAssignment' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAgent'. Either include it or remove the dependency array.", ["801"], "React Hook useEffect has missing dependencies: 'fetchAgents' and 'fetchTemplate'. Either include them or remove the dependency array.", ["802"], "'Option' is assigned a value but never used.", "'agentList' is defined but never used.", "React Hook useEffect has a missing dependency: 'intial_data'. Either include it or remove the dependency array.", ["803"], "'setCustomerData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCustomerData'. Either include it or remove the dependency array.", ["804"], "'useCallback' is defined but never used.", "'showDatePicker' is assigned a value but never used.", ["805"], "React Hook useEffect has a missing dependency: 'fetchSearch'. Either include it or remove the dependency array.", ["806"], "'handleDateClick' is assigned a value but never used.", "'clearFilter' is assigned a value but never used.", "'selectedOption' is assigned a value but never used.", "'nextPage' is assigned a value but never used.", "'SlCalender' is defined but never used.", "'currentUser' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchReportData', 'fromDate', and 'toDate'. Either include them or remove the dependency array.", ["807"], "React Hook useEffect has a missing dependency: 'fetchReportData'. Either include it or remove the dependency array.", ["808"], "React Hook useEffect has missing dependencies: 'fetchAgentData', 'fromDate', and 'toDate'. Either include them or remove the dependency array.", ["809"], "React Hook useEffect has a missing dependency: 'fetchAgentData'. Either include it or remove the dependency array.", ["810"], ["811"], "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchkeywords'. Either include it or remove the dependency array.", ["812"], "'CircularProgress' is defined but never used.", "'arrayMove' is defined but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "React Hook useEffect has a missing dependency: 'fetchContacts'. Either include it or remove the dependency array.", ["813"], "React Hook useEffect has a missing dependency: 'fetchAgents'. Either include it or remove the dependency array.", ["814"], "'Card' is defined but never used.", "'FaEnvelope' is defined but never used.", "'FaClock' is defined but never used.", "'IoMdSettings' is defined but never used.", "'Link' is defined but never used.", "'agent' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchActivityLog' and 'fetchAgentData'. Either include them or remove the dependency array.", ["815"], "'formatDate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'endBreak'. Either include it or remove the dependency array.", ["816"], "'BASE_URL' is defined but never used.", "'setShowDatePicker' is assigned a value but never used.", "'templatePopUp' is assigned a value but never used.", "'setTemplatePopUp' is assigned a value but never used.", "'debouncedSearchInput' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAgents', 'fetchNewContactList', 'fromDate', and 'toDate'. Either include them or remove the dependency array.", ["817"], "React Hook useEffect has missing dependencies: 'fetchNewContactList' and 'setFetchDataFn'. Either include them or remove the dependency array.", ["818"], "'MdSupportAgent' is defined but never used.", "'permission' is assigned a value but never used.", "'isExpiryClose' is assigned a value but never used.", ["819"], "React Hook useEffect has a missing dependency: 'showAgent'. Either include it or remove the dependency array.", ["820"], "'handleShowMobile' is assigned a value but never used.", "'handleNavigate' is assigned a value but never used.", "'toast' is defined but never used.", "'updateUser' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setIsOldMsg' and 'setRemainingTime'. Either include them or remove the dependency array.", ["821"], "React Hook useEffect has a missing dependency: 'setKeyBoardOpen'. Either include it or remove the dependency array. If 'setKeyBoardOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["822"], "React Hook useEffect has missing dependencies: 'selectedName' and 'setUserLabels'. Either include them or remove the dependency array. If 'setUserLabels' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["823"], "'BsFilter' is defined but never used.", "'AiOutlineClose' is defined but never used.", "'unReadChat' is assigned a value but never used.", "'setPage' is assigned a value but never used.", "'channel' is assigned a value but never used.", "'setChannel' is assigned a value but never used.", "'setRoleFilterType' is assigned a value but never used.", "'starChats' is assigned a value but never used.", "'setWaitingChats' is assigned a value but never used.", "'filteredChats' is assigned a value but never used.", ["824"], "React Hook useEffect has missing dependencies: 'fetchChats' and 'fetchStarredChats'. Either include them or remove the dependency array.", ["825"], "'getWhatsaAppNumberList' is assigned a value but never used.", "'waitingBtn' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'setSelectedCategory' is assigned a value but never used.", "'deleteIndex' is assigned a value but never used.", "'setDeleteIndex' is assigned a value but never used.", "'OverviewCard' is defined but never used.", "'use' is defined but never used.", "'IoIosArrowDroprightCircle' is defined but never used.", "'IoMdMove' is defined but never used.", "'toggleActivities' is assigned a value but never used.", "'Select' is defined but never used.", "'labelOptions' is assigned a value but never used.", "'templateList' is assigned a value but never used.", "'setTemplateList' is assigned a value but never used.", "'countryCode' is assigned a value but never used.", "'setCountryCode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTemplate'. Either include it or remove the dependency array.", ["826"], ["827"], "React Hook useEffect has missing dependencies: 'currentUser.parent_id', 'currentUser.parent_token', and 'selectedMobileNumber'. Either include them or remove the dependency array.", ["828"], "'WaitingCard' is defined but never used.", "no-empty-pattern", "Unexpected empty object pattern.", "ObjectPattern", "'waitingChats' is assigned a value but never used.", "'setUnReadChat' is assigned a value but never used.", "'mobileVisible' is assigned a value but never used.", "'nextConvId' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'disable', 'selectedName', 'setAllChats', and 'setUnreadCount'. Either include them or remove the dependency array.", ["829"], "React Hook useEffect has missing dependencies: 'setAllChats', 'setChats', 'setConvPage', 'setSelectedMobileNumber', and 'setSelectedUserDetails'. Either include them or remove the dependency array.", ["830"], "React Hook useEffect has a missing dependency: 'fetchNumberVisibility'. Either include it or remove the dependency array.", ["831"], "'searchInput' is assigned a value but never used.", "'useContext' is defined but never used.", "'ChatState' is defined but never used.", "'AuthContext' is defined but never used.", "'Button' is defined but never used.", "'selected<PERSON><PERSON><PERSON>' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", "'activeStatus' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setWpProfile'. Either include it or remove the dependency array.", ["832"], "'IOSSwitch' is assigned a value but never used.", "'handleActiveStatus' is assigned a value but never used.", "'data' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'showTypingStatus'. Either include it or remove the dependency array.", ["833"], "React Hook useEffect has a missing dependency: 'setActivities'. Either include it or remove the dependency array. If 'setActivities' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["834"], "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'pieChartData' is assigned a value but never used.", "'COLORS' is assigned a value but never used.", "'Box' is defined but never used.", "'React' is defined but never used.", "'axios' is defined but never used.", "React Hook useEffect has a missing dependency: 'localSentences'. Either include it or remove the dependency array.", ["835"], "'useDebounce' is defined but never used.", "'saveSentences' is assigned a value but never used.", "'splitParagraphIntoSentences' is assigned a value but never used.", "'setShowPreview' is assigned a value but never used.", "'msg' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'textareaRef'. Either include it or remove the dependency array.", ["836"], "React Hook useEffect has a missing dependency: 'currentUser'. Either include it or remove the dependency array.", ["837"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", ["838"], "'RiArrowDropDownLine' is defined but never used.", "'reply' is assigned a value but never used.", {"desc": "839", "fix": "840"}, {"desc": "841", "fix": "842"}, {"desc": "843", "fix": "844"}, {"desc": "845", "fix": "846"}, {"desc": "847", "fix": "848"}, {"desc": "849", "fix": "850"}, {"desc": "851", "fix": "852"}, {"desc": "853", "fix": "854"}, {"desc": "855", "fix": "856"}, {"desc": "857", "fix": "858"}, {"desc": "859", "fix": "860"}, {"desc": "861", "fix": "862"}, {"desc": "863", "fix": "864"}, {"desc": "865", "fix": "866"}, {"desc": "867", "fix": "868"}, {"desc": "869", "fix": "870"}, {"desc": "871", "fix": "872"}, {"kind": "873", "justification": "874"}, {"desc": "875", "fix": "876"}, {"desc": "877", "fix": "878"}, {"desc": "879", "fix": "880"}, {"desc": "881", "fix": "882"}, {"desc": "883", "fix": "884"}, {"desc": "885", "fix": "886"}, {"desc": "887", "fix": "888"}, {"desc": "889", "fix": "890"}, {"desc": "891", "fix": "892"}, {"desc": "893", "fix": "894"}, {"desc": "895", "fix": "896"}, {"desc": "897", "fix": "898"}, {"desc": "879", "fix": "899"}, {"desc": "900", "fix": "901"}, {"desc": "902", "fix": "903"}, {"desc": "904", "fix": "905"}, {"desc": "906", "fix": "907"}, {"desc": "908", "fix": "909"}, {"desc": "910", "fix": "911"}, {"desc": "912", "fix": "913"}, {"desc": "914", "fix": "915"}, {"desc": "916", "fix": "917"}, {"desc": "918", "fix": "919"}, {"desc": "920", "fix": "921"}, {"desc": "922", "fix": "923"}, {"desc": "924", "fix": "925"}, {"desc": "902", "fix": "926"}, "Update the dependencies array to be: [currentUser, requestPermission]", {"range": "927", "text": "928"}, "Update the dependencies array to be: [currentUser, setCallData, socket]", {"range": "929", "text": "930"}, "Update the dependencies array to be: [setShowReminder, setShowAllReminder]", {"range": "931", "text": "932"}, "Update the dependencies array to be: [currentUser, graphReport]", {"range": "933", "text": "934"}, "Update the dependencies array to be: [currentUser, fetchNewContactList]", {"range": "935", "text": "936"}, "Update the dependencies array to be: [getWhatsaAppNumberList]", {"range": "937", "text": "938"}, "Update the dependencies array to be: [Balance]", {"range": "939", "text": "940"}, "Update the dependencies array to be: [currentUser, fetchAgent, selectedOption]", {"range": "941", "text": "942"}, "Update the dependencies array to be: [currentUser, fetchAgents, fetchTemplate]", {"range": "943", "text": "944"}, "Update the dependencies array to be: [intial_data]", {"range": "945", "text": "946"}, "Update the dependencies array to be: [currentUser, fetchCustomerData, mobile]", {"range": "947", "text": "948"}, "Update the dependencies array to be: [currentUser, fetchNewContactList, fromDate, toDate]", {"range": "949", "text": "950"}, "Update the dependencies array to be: [debouncedSearchInput, fetchSearch]", {"range": "951", "text": "952"}, "Update the dependencies array to be: [currentUser, fetchReportData, fromDate, toDate]", {"range": "953", "text": "954"}, "Update the dependencies array to be: [setFetchDataFn, fromDate, toDate, fetchReportData]", {"range": "955", "text": "956"}, "Update the dependencies array to be: [currentUser, fetchAgentData, fromDate, toDate]", {"range": "957", "text": "958"}, "Update the dependencies array to be: [setFetchDataFn, fromDate, toDate, fetchAgentData]", {"range": "959", "text": "960"}, "directive", "", "Update the dependencies array to be: [currentUser, agentId, fetchkeywords]", {"range": "961", "text": "962"}, "Update the dependencies array to be: [currentUser, fetchContacts, fromDate, toDate]", {"range": "963", "text": "964"}, "Update the dependencies array to be: [currentUser, fetchAgents]", {"range": "965", "text": "966"}, "Update the dependencies array to be: [currentUser, agentId, fromDate, toDate, fetchAgentData, fetchActivityLog]", {"range": "967", "text": "968"}, "Update the dependencies array to be: [breakInfo, endBreak]", {"range": "969", "text": "970"}, "Update the dependencies array to be: [currentUser, fetchAgents, fetchNewContactList, fromDate, toDate]", {"range": "971", "text": "972"}, "Update the dependencies array to be: [currentUser, fetchNewContactList, fromDate, setFetchDataFn, toDate]", {"range": "973", "text": "974"}, "Update the dependencies array to be: [Balance, currentUser, socket]", {"range": "975", "text": "976"}, "Update the dependencies array to be: [currentUser, showAgent]", {"range": "977", "text": "978"}, "Update the dependencies array to be: [remainingTime, setIsOldMsg, setRemainingTime]", {"range": "979", "text": "980"}, "Update the dependencies array to be: [setKeyBoardOpen]", {"range": "981", "text": "982"}, "Update the dependencies array to be: [currentUser, selectedMobileNumber, selectedName, setUserLabels]", {"range": "983", "text": "984"}, {"range": "985", "text": "966"}, "Update the dependencies array to be: [currentUser, setChatsLoading, disable, chatCategory, chatFilterType.value, fetchStarredChats, fetchChats]", {"range": "986", "text": "987"}, "Update the dependencies array to be: [currentUser, fetchTemplate]", {"range": "988", "text": "989"}, "Update the dependencies array to be: [fetchSearch, inputValue]", {"range": "990", "text": "991"}, "Update the dependencies array to be: [fetchType, fetchAllParams, fetchMobileParams, currentUser.parent_id, currentUser.parent_token, selectedMobileNumber]", {"range": "992", "text": "993"}, "Update the dependencies array to be: [currentUser, selectedMobileNumber, allChats, dispatch, unReadChat, data, socket, chatFilterType, chatCategory, disable, selectedName, setAllChats, setUnreadCount]", {"range": "994", "text": "995"}, "Update the dependencies array to be: [currentUser, allChats, socket, chats, setAllChats, setChats, setSelectedMobileNumber, setSelectedUserDetails, setConvPage]", {"range": "996", "text": "997"}, "Update the dependencies array to be: [currentUser, fetchNumberVisibility]", {"range": "998", "text": "999"}, "Update the dependencies array to be: [currentUser, setWpProfile]", {"range": "1000", "text": "1001"}, "Update the dependencies array to be: [text, currentUser, showTypingStatus]", {"range": "1002", "text": "1003"}, "Update the dependencies array to be: [currentUser, phone, setActivities]", {"range": "1004", "text": "1005"}, "Update the dependencies array to be: [debouncedInputText, localSentences]", {"range": "1006", "text": "1007"}, "Update the dependencies array to be: [textareaRef]", {"range": "1008", "text": "1009"}, "Update the dependencies array to be: [text, socket, currentUser]", {"range": "1010", "text": "1011"}, {"range": "1012", "text": "989"}, [2528, 2541], "[currentUser, requestPermission]", [3554, 3575], "[currentUser, setCallData, socket]", [6022, 6090], "[setShowReminder, setShowAllReminder]", [6046, 6059], "[current<PERSON><PERSON>, graphReport]", [6119, 6132], "[current<PERSON><PERSON>, fetchNewContactList]", [7436, 7438], "[getWhatsaAppNumberList]", [1065, 1067], "[Balance]", [6505, 6534], "[currentUser, fetchAgent, selectedOption]", [9719, 9732], "[currentUser, fetchAgents, fetchTemplate]", [1030, 1032], "[intial_data]", [1657, 1678], "[currentUser, fetchCustomerData, mobile]", [3033, 3064], "[current<PERSON><PERSON>, fetchNewContactList, fromDate, toDate]", [5434, 5456], "[debouncedSearchInput, fetchSearch]", [1085, 1098], "[currentUser, fetchReportData, fromDate, toDate]", [1199, 1233], "[setFetchDataFn, fromDate, toDate, fetchReportData]", [951, 964], "[currentUser, fetchAgentData, fromDate, toDate]", [1120, 1154], "[setFetchDataFn, fromDate, toDate, fetchAgentData]", [1369, 1391], "[currentUser, agentId, fetchkeywords]", [8271, 8302], "[currentUser, fetchContacts, fromDate, toDate]", [8363, 8376], "[current<PERSON><PERSON>, fetchAgents]", [3757, 3797], "[currentUser, agentId, fromDate, toDate, fetchAgentData, fetchActivityLog]", [1575, 1586], "[breakInfo, endBreak]", [3486, 3499], "[current<PERSON><PERSON>, fetchA<PERSON>s, fetchNewContactList, fromDate, toDate]", [3609, 3640], "[current<PERSON><PERSON>, fetchNewContactList, fromDate, setFetchDataFn, toDate]", [4176, 4197], "[Balance, currentUser, socket]", [5770, 5783], "[current<PERSON><PERSON>, showAgent]", [2262, 2277], "[remainingTime, setIsOldMsg, setRemainingTime]", [5115, 5117], "[setKeyBoardOpen]", [5843, 5878], "[current<PERSON><PERSON>, selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, selected<PERSON><PERSON>, setUserLabels]", [3450, 3463], [8686, 8761], "[currentUser, setChatsLoading, disable, chatCategory, chatFilterType.value, fetchStarredChats, fetchChats]", [2220, 2233], "[currentUser, fetchTemplate]", [770, 782], "[fetchSearch, inputValue]", [2221, 2267], "[fetchType, fetchAllParams, fetchMobileParams, currentUser.parent_id, currentUser.parent_token, selectedMobileNumber]", [22321, 22432], "[currentUser, selectedMobileNumber, allChats, dispatch, unReadChat, data, socket, chatFilterType, chatCategory, disable, selectedName, setAllChats, setUnreadCount]", [28281, 28319], "[currentUser, allChats, socket, chats, setAllChats, setChats, setSelectedMobileNumber, setSelectedUserDetails, setConvPage]", [29332, 29345], "[currentUser, fetchNumberVisibility]", [1291, 1304], "[current<PERSON><PERSON>, setWpProfile]", [4092, 4111], "[text, currentUser, showTypingStatus]", [1613, 1632], "[currentUser, phone, setActivities]", [1892, 1912], "[debouncedInputText, localSentences]", [13545, 13547], "[textareaRef]", [14366, 14380], "[text, socket, currentUser]", [1430, 1443]]