// Translation feature test utility for AuthKey API
import { TRANSLATION_CONFIG, getTranslationUrl } from '../config/translation';

/**
 * Test if the AuthKey translation API is accessible
 * @param {Object} currentUser - Current user object with parent_id and parent_token
 * @returns {Promise<boolean>} True if API is accessible
 */
export const testAuthKeyTranslationAPI = async (currentUser) => {
  if (!currentUser?.parent_id || !currentUser?.parent_token) {
    console.error('Missing user authentication data');
    return false;
  }

  try {
    const payload = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: "language_list"
    };
    
    const response = await fetch(getTranslationUrl(TRANSLATION_CONFIG.ENDPOINTS.GOOGLE), {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });
    
    const data = await response.json();
    console.log('AuthKey Translation API Test - Languages:', data);
    return data.success === true;
  } catch (error) {
    console.error('AuthKey translation API test failed:', error);
    return false;
  }
};

/**
 * Test translation functionality
 * @param {string} text - Text to translate
 * @param {string} targetLanguage - Target language code
 * @param {Object} currentUser - Current user object
 * @returns {Promise<string|null>} Translated text or null if failed
 */
export const testAuthKeyTranslation = async (text, targetLanguage, currentUser) => {
  if (!currentUser?.parent_id || !currentUser?.parent_token) {
    console.error('Missing user authentication data');
    return null;
  }

  try {
    const payload = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: "translate",
      target_lang: targetLanguage,
      text: text
    };
    
    const response = await fetch(getTranslationUrl(TRANSLATION_CONFIG.ENDPOINTS.GOOGLE), {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });
    
    const data = await response.json();
    console.log('AuthKey Translation Test Result:', data);
    
    if (data.success) {
      return data.translated_text;
    } else {
      console.error('Translation failed:', data.message);
      return null;
    }
  } catch (error) {
    console.error('Translation test failed:', error);
    return null;
  }
};

// Example usage:
// testAuthKeyTranslationAPI(currentUser).then(isWorking => console.log('API Working:', isWorking));
// testAuthKeyTranslation('hello', 'hi', currentUser).then(result => console.log('Translation:', result));
