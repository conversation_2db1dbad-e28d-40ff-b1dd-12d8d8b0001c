import styles from "./ActivityLog.module.css";
import { useState } from "react";
const ActivityLog = () => {
    const [logs] = useState([
        {
            id: 1,
            date: "April 10, 2025",
            title: "Agent Assigned",
            description: "<PERSON> was assigned to handle this customer.",
            dotColor: "primary",
        },
        {
            id: 2,
            date: "April 11, 2025",
            title: "Campaign Delivered",
            description: "Spring Promo 2025 campaign sent to the customer.",
            dotColor: "info",
        },
        {
            id: 3,
            date: "April 12, 2025",
            title: "Customer Responded",
            description: "Customer replied to the Spring Promo campaign.",
            dotColor: "success",
        },
        {
            id: 4,
            date: "April 13, 2025",
            title: "Label Applied: HOT",
            description: '"HOT" tag was assigned to this customer by the agent.',
            dotColor: "danger",
        },
        {
            id: 5,
            date: "April 14, 2025",
            title: "Bot Executed",
            description: "Qualification bot was triggered for this customer.",
            dotColor: "warning",
        },
    ]);


    return (
        <div className="container my-4">
            <div className="d-flex justify-content-between align-items-center mb-3">
                <h4 className="fw-bold">Recent Activity:</h4>
                <button className="btn btn-outline-primary btn-sm">View Alls</button>
            </div>
            <ul className={`list-unstyled ${styles.timeline}`}>
                {logs.map((log) => (
                    <li key={log.id} className={`d-flex justify-content-between align-items-start mb-4 ${styles.logItem}`}>
                        <div className="d-flex">
                            <div className={`bg-${log.dotColor} ${styles.dot} me-3 mt-1`}></div>
                            <div>
                                <small className="text-muted">
                                    {log.description} [{log.date}]
                                </small>
                                <br />
                                <span>
                                    {log.title}
                                    {log.linkText && <a href="#" className="ms-1">{log.linkText}</a>}
                                    {log.action && (
                                        <button className="btn btn-success btn-sm ms-2">
                                            {log.action}
                                        </button>
                                    )}
                                </span>
                            </div>
                        </div>
                        <div className={styles.icons}>
                            <i className="bi bi-check2-circle me-2"></i>
                            <i className="bi bi-eye me-2"></i>
                            <i className="bi bi-three-dots-vertical"></i>
                        </div>
                    </li>
                ))}
            </ul>
        </div>
    );
};

export default ActivityLog;
