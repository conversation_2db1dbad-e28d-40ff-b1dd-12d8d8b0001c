{"ast": null, "code": "var _jsxFileName = \"D:\\\\DataGen\\\\authkey-chat-new\\\\src\\\\components\\\\Input.jsx\",\n    _s = $RefreshSig$();\n\nimport React, { useContext, useEffect, useRef, useState } from \"react\";\nimport EmojiPicker from \"emoji-picker-react\";\nimport { AuthContext } from \"../context/AuthContext\";\nimport { BASE_URL, sendMessage } from \"../api/api\";\nimport { ChatContext } from \"../context/ChatContext\";\nimport { ChatState } from \"../context/AllProviders\";\nimport SendTemplate from \"./SendTemplate\";\nimport { useDebounce } from \"../customHooks/useDebounce\";\nimport { MdDelete } from \"react-icons/md\";\nimport { FaPlay, FaPause, FaRegStopCircle } from \"react-icons/fa\";\nimport { toast } from \"react-toastify\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport { FaPlus } from \"react-icons/fa6\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n\nconst Input = props => {\n  _s();\n\n  const {\n    saveSentences,\n    filteredSentences,\n    splitParagraphIntoSentences,\n    handleSuggestionClick,\n    textareaRef,\n    showPreview,\n    setShowPreview,\n    handleTranslationKeyDown,\n    handleTranslationInputChange\n  } = props;\n  const pickerRef = useRef(null); // const [text, setText] = useState(\"\");\n\n  const [showPrev, setShowPrev] = useState(false);\n  const [buttonLoader, setButtonLoader] = useState(false);\n  const [caption, setCaption] = useState(\"\");\n  const [previewUrl, setPreviewUrl] = useState();\n  const [file, setFile] = useState(null);\n  const [fileSize, setFileSize] = useState();\n  const [fileType, setFileType] = useState();\n  const [emojiStatus, setEmojiStatus] = useState(false);\n  const [sendButton, setSendButton] = useState(false);\n  const [isRecording, setIsRecording] = useState(false);\n  const [isPaused, setIsPaused] = useState(false);\n  const [recordingTime, setRecordingTime] = useState(0);\n  const [audioURL, setAudioURL] = useState(null);\n  const mediaRecorderRef = useRef(null);\n  const audioChunksRef = useRef([]);\n  const timerRef = useRef(null);\n  const [error, setError] = useState({\n    error: false,\n    errorMessage: \"\",\n    errorType: \"\"\n  });\n  const {\n    currentUser\n  } = useContext(AuthContext);\n  const {\n    dispatch\n  } = useContext(ChatContext);\n  const {\n    isViewerOpen,\n    setIsViewerOpen,\n    selectedImage,\n    setSelectedImage,\n    isOldMsg,\n    setIsOldMsg,\n    sendTemplatePopUp,\n    setSendTemplatePopUp,\n    selectedMobileNumber,\n    text,\n    setText,\n    socket,\n    setAllChats,\n    reply,\n    setReply\n  } = ChatState();\n  useEffect(() => {\n    setTimeout(() => {\n      setError({\n        error: false,\n        errorMessage: \"\",\n        errorType: \"\"\n      });\n    }, 5000);\n  }, [error]);\n  useEffect(() => {\n    return () => clearInterval(timerRef.current);\n  }, []);\n\n  const togglePopup = () => {\n    setShowPrev(!showPrev);\n    setFileSize();\n    setPreviewUrl();\n    setFile(null);\n    setPreviewUrl(\"\");\n    setFileType();\n  };\n\n  const resetState = () => {\n    setAudioURL(null);\n    setSendButton(false);\n    setIsRecording(false);\n    setPreviewUrl(\"\");\n    setFileSize(undefined);\n    setFileType(undefined);\n    setShowPrev(false);\n    setFile(null);\n    setButtonLoader(false);\n    setText(\"\");\n    setCaption(\"\");\n  };\n\n  const handleSend = async () => {\n    setEmojiStatus(false);\n    const uid = uuidv4();\n\n    if (file) {\n      setButtonLoader(true);\n\n      try {\n        let url = await uploadWhatsAppMedia();\n        const msg = {};\n        const {\n          data\n        } = await sendMessage({\n          agent_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.user_id,\n          agent_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.name,\n          manager_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.manager,\n          manager_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.manager_name,\n          team_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.team,\n          team_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.team_name,\n          token: currentUser.parent_token,\n          user_id: currentUser.parent_id,\n          method: \"media_reply\",\n          attachment_url: url,\n          caption: caption,\n          message_type: fileType,\n          brand_number: currentUser.brand_number,\n          mobile: props.selectedMobile,\n          content: text,\n          request_type: \"\"\n        });\n\n        if (data.success === true) {\n          let newdata = [...props.convData.conversion, data.response];\n          dispatch({\n            type: \"CHANGE_USER\",\n            payload: {\n              mobile: props.selectedMobile,\n              conversation: newdata,\n              name: props.convData.selectedName\n            }\n          });\n          resetState();\n        } else {\n          resetState();\n          toast.error(data.message);\n          setError({\n            error: true,\n            errorMessage: data.message,\n            errorType: \"alert-danger\"\n          });\n          setButtonLoader(false);\n        }\n      } catch (error) {\n        toast.error(error.message);\n        setButtonLoader(false);\n        resetState();\n      }\n    } else {\n      const date = new Date();\n      const msg = {\n        agent_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.user_id,\n        agent_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.name,\n        manager_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.manager,\n        manager_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.manager_name,\n        team_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.team,\n        team_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.team_name,\n        track_id: uid,\n        mobile: props.selectedMobile,\n        brand_number: currentUser.brand_number,\n        message_type: \"TEXT\",\n        req_from: \"AGENT_REPLY\",\n        file_url: \"\",\n        message_content: text,\n        image_caption: \"\",\n        resp_url: \"\",\n        request_type: \"\",\n        status: \"pending\",\n        created: date,\n        tag_id: reply ? JSON.stringify({\n          id: reply.id,\n          message_id: reply.messageid,\n          message_type: \"TEXT\",\n          req_from: reply.req_from,\n          message_content: reply.message_content,\n          file_url: \"\"\n        }) : \"\"\n      };\n      let newdata = [...props.convData.conversion, msg];\n      setReply(null);\n      dispatch({\n        type: \"CHANGE_USER\",\n        payload: {\n          mobile: props.selectedMobile,\n          conversation: newdata,\n          name: props.convData.selectedName\n        }\n      });\n      let data = {\n        agent_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.user_id,\n        agent_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.name,\n        manager_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.manager,\n        manager_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.manager_name,\n        team_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.team,\n        team_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.team_name,\n        track_id: uid,\n        token: currentUser.parent_token,\n        user_id: currentUser.parent_id,\n        method: \"reply\",\n        request_type: \"\",\n        brand_number: currentUser.brand_number,\n        mobile: props.selectedMobile,\n        content: text,\n        tag_id: reply ? JSON.stringify({\n          id: reply.id,\n          message_id: reply.messageid,\n          message_type: \"TEXT\",\n          req_from: reply.req_from,\n          message_content: reply.message_content,\n          file_url: \"\"\n        }) : \"\"\n      }; // if (text.trim()) {\n      //   const sentences = splitParagraphIntoSentences(text);\n      //   sentences.forEach((sentence) => {\n      //     saveSentences(sentence); // Save each sentence individually\n      //   });\n      // }\n\n      setAllChats(prevState => prevState.map(item => {\n        if (item.mobile === selectedMobileNumber) {\n          return { ...item,\n            req_from: \"AGENT_REPLY\",\n            content: text,\n            created: date\n          };\n        }\n\n        return item;\n      }));\n      setText(\"\"); // setShowPreview(false);\n\n      sendMessage(data).then(res => {\n        if (res.data.success === true) {\n          const updatedChat = newdata.map(chatdata => {\n            if (chatdata.track_id === res.data.track_id) {\n              return { ...chatdata,\n                status: \"Submitted\"\n              };\n            }\n\n            return chatdata;\n          });\n          dispatch({\n            type: \"CHANGE_USER\",\n            payload: {\n              mobile: props.selectedMobile,\n              conversation: updatedChat,\n              name: props.convData.selectedName\n            }\n          }); // setText(\"\");\n        } else {\n          if (res.data.message === \"You need to send fresh reply coz chat 24 hour old\") {\n            setIsOldMsg(true);\n          }\n\n          setError({\n            error: true,\n            errorMessage: res.data.message,\n            errorType: \"alert-danger\"\n          });\n        }\n      });\n    }\n  };\n\n  const fileHandler = e => {\n    setShowPrev(!showPrev);\n    const file = e.target.files[0];\n\n    if (e.target.files[0].type.startsWith(\"image\")) {\n      setFileType(\"image\");\n    } else if (e.target.files[0].type.startsWith(\"video\")) {\n      setFileType(\"video\");\n    } else {\n      setFileType(\"file\");\n    }\n\n    document.body.style.overflow = !showPrev ? \"hidden\" : \"auto\";\n\n    if (file) {\n      const fileSizeInBytes = file.size;\n      const fileSizeInMB = fileSizeInBytes / (1024 * 1024);\n      setFileSize(fileSizeInMB);\n\n      if (file && file.type.startsWith(\"video/\")) {\n        const objectURL = URL.createObjectURL(file);\n        setPreviewUrl(objectURL);\n        setFile(file);\n        return;\n      }\n\n      const reader = new FileReader();\n\n      reader.onloadend = () => {\n        setPreviewUrl(reader.result);\n      };\n\n      reader.readAsDataURL(file);\n      setFile(file);\n    } else {\n      setFile(null);\n      setPreviewUrl(\"\");\n    }\n  };\n\n  const uploadRecording = async rec => {\n    try {\n      var data = await new Promise((resolve, reject) => {\n        const data = new FormData();\n        data.append(\"amdfile\", rec.file, \"recording.mp3\");\n        data.append(\"doc_name\", \"test MK\");\n        data.append(\"doc_type\", rec.fileType);\n        data.append(\"input_file_type\", \"convert_recording\");\n        data.append(\"user_id\", currentUser.parent_id);\n        data.append(\"token\", currentUser.parent_token);\n        data.append(\"method\", \"send_recording\");\n        fetch(`${BASE_URL}/uploadFileWhatsapp.php`, {\n          method: \"POST\",\n          body: data\n        }).then(result => {\n          result.json().then(resp => {\n            resolve(resp.url);\n          });\n        });\n      }); // setFile(undefined);\n\n      return data;\n    } catch (error) {\n      console.log(\"error\");\n    }\n  };\n\n  const uploadWhatsAppMedia = async () => {\n    try {\n      var data = await new Promise((resolve, reject) => {\n        const data = new FormData();\n        data.append(\"amdfile\", file);\n        data.append(\"doc_name\", \"test MK\");\n        data.append(\"doc_type\", fileType);\n        data.append(\"user_id\", currentUser.parent_id);\n        data.append(\"token\", currentUser.parent_token);\n        data.append(\"method\", \"create\");\n        fetch(`${BASE_URL}/uploadFileWhatsapp.php`, {\n          method: \"POST\",\n          body: data\n        }).then(result => {\n          result.json().then(resp => {\n            resolve(resp.url);\n          });\n        });\n      }); // setFile(undefined);\n\n      return data;\n    } catch (error) {\n      console.log(\"error\");\n    }\n  };\n\n  const handleKeyPress = event => {\n    if (event.key === \"Enter\" && event.shiftKey) {// event.preventDefault();\n    } else if (event.key === \"Enter\") {\n      event.preventDefault();\n      handleSend();\n    }\n\n    if (event.key === \"Tab\" && filteredSentences.length > 0) {\n      event.preventDefault();\n      handleSuggestionClick(filteredSentences[0]);\n    } // Handle translation key events if function is provided\n\n\n    if (handleTranslationKeyDown) {\n      handleTranslationKeyDown(event);\n    }\n  };\n\n  const ImageViewer = _ref => {\n    let {\n      imageUrl,\n      onClose\n    } = _ref;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"popup\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"popupInner\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: imageUrl,\n          alt: \"Preview\",\n          className: \"popup-img\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"cancelButton\",\n          onClick: onClose,\n          children: \"x\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 7\n    }, this);\n  };\n\n  const closeImageViewer = () => {\n    setIsViewerOpen(false);\n    setSelectedImage(\"\");\n  };\n\n  useEffect(() => {\n    const handleResize = () => {\n      if (textareaRef.current) {\n        textareaRef.current.style.height = \"auto\";\n        const newHeight = Math.min(textareaRef.current.scrollHeight, 100);\n        textareaRef.current.style.height = `${newHeight}px`;\n        textareaRef.current.style.overflowY = textareaRef.current.scrollHeight > 100 ? \"auto\" : \"hidden\";\n      }\n    };\n\n    const textarea = textareaRef.current;\n    textarea === null || textarea === void 0 ? void 0 : textarea.addEventListener(\"input\", handleResize);\n    return () => {\n      textarea === null || textarea === void 0 ? void 0 : textarea.removeEventListener(\"input\", handleResize);\n    };\n  }, []);\n\n  const handleTextarea = e => {\n    setEmojiStatus(false);\n    setText(e.target.value); // setShowPreview(/(\\*.*?\\*)|(_.*?_)|(~.*?~)/.test(e.target.value));\n\n    if (e.target.value === \"\") {\n      setSendButton(false);\n    } else {\n      setSendButton(true);\n    } // Handle translation input change if function is provided\n\n\n    if (handleTranslationInputChange) {\n      handleTranslationInputChange(e.target.value);\n    }\n  };\n\n  useEffect(() => {\n    if (!socket) return;\n\n    if (text.length > 0) {\n      setSendButton(true);\n    } else {\n      setSendButton(false);\n    }\n\n    const timer1 = setTimeout(() => {\n      if (text.trim()) {\n        socket.emit(\"chat on\", currentUser);\n      }\n    }, 1000);\n    const timer = setTimeout(() => {\n      if (text.trim()) {\n        socket.emit(\"chat off\", currentUser);\n      }\n    }, 2 * 60 * 1000);\n    return () => {\n      clearTimeout(timer1);\n      clearTimeout(timer);\n    };\n  }, [text, socket]); // useEffect(() => {\n  //   let socket = io(SOCKET_URL);\n  //   if (currentUser.parent_id) {\n  //     socket.emit(\"setup\", currentUser);\n  //   }\n  //   const timer1 = setTimeout(() => {\n  //     if (text.trim()) {\n  //       socket.emit(\"chat on\", currentUser);\n  //     }\n  //   }, 1000);\n  //   const timer = setTimeout(() => {\n  //     if (text.trim()) {\n  //       socket.emit(\"chat off\", currentUser);\n  //     }\n  //   }, 2 * 60 * 1000);\n  //   return () => {\n  //     clearTimeout(timer, timer1);\n  //     socket.disconnect();\n  //   };\n  // }, [text]);\n  // useEffect(() => {\n  //   const handleClickOutside = (event) => {\n  //     if (pickerRef.current && !pickerRef.current.contains(event.target)) {\n  //       setEmojiStatus(false);\n  //     }\n  //   };\n  //   document.addEventListener(\"mousedown\", handleClickOutside);\n  //   return () => {\n  //     document.removeEventListener(\"mousedown\", handleClickOutside);\n  //   };\n  // }, []);\n\n  const toggleEmoji = () => {\n    const element = document.getElementById(\"chatinputmorecollapse\");\n\n    if (element) {\n      element.classList.remove(\"show\");\n    }\n\n    setEmojiStatus(!emojiStatus);\n  };\n\n  const emojiselect = emojidata => {\n    const {\n      current\n    } = textareaRef;\n    const {\n      selectionStart,\n      selectionEnd\n    } = current;\n    const newValue = text.substring(0, selectionStart) + emojidata.emoji + text.substring(selectionEnd);\n    setText(newValue);\n    current.focus();\n    current.setSelectionRange(selectionStart + emojidata.emoji.length, selectionStart + emojidata.emoji.length);\n  };\n\n  const formatTime = totalSeconds => {\n    const minutes = String(Math.floor(totalSeconds / 60)).padStart(2, \"0\");\n    const seconds = String(totalSeconds % 60).padStart(2, \"0\");\n    return `${minutes}:${seconds}`;\n  };\n\n  const startRecording = async () => {\n    setSendButton(true);\n    setIsRecording(true);\n    setIsPaused(false);\n    setRecordingTime(0);\n    const stream = await navigator.mediaDevices.getUserMedia({\n      audio: true\n    });\n    mediaRecorderRef.current = new MediaRecorder(stream);\n\n    mediaRecorderRef.current.ondataavailable = event => {\n      audioChunksRef.current.push(event.data);\n    };\n\n    mediaRecorderRef.current.onstop = async () => {\n      audioChunksRef.current = [];\n    };\n\n    mediaRecorderRef.current.start();\n    timerRef.current = setInterval(() => {\n      setRecordingTime(prevTime => prevTime + 1);\n    }, 1000);\n  };\n\n  const stopRecording = async () => {\n    clearInterval(timerRef.current);\n\n    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n      mediaRecorderRef.current.stop();\n      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());\n    }\n\n    audioChunksRef.current = [];\n    setAudioURL(null);\n    setSendButton(false);\n    setIsRecording(false);\n    setIsPaused(false);\n    setFileType(undefined);\n    setFile(null);\n    resetState();\n    await navigator.mediaDevices.getUserMedia({\n      audio: false\n    });\n  };\n\n  const prevRec = () => {\n    setIsPaused(false);\n    clearInterval(timerRef.current);\n\n    mediaRecorderRef.current.onstop = async () => {\n      const audioBlob = new Blob(audioChunksRef.current, {\n        type: \"audio/wav\"\n      });\n      audioChunksRef.current = [];\n      setAudioURL(URL.createObjectURL(audioBlob));\n      const type = audioBlob.type;\n      setFileType(type);\n      setFile(audioBlob);\n    };\n\n    mediaRecorderRef.current.stop();\n  };\n\n  const pauseRecording = () => {\n    setIsPaused(true);\n    clearInterval(timerRef.current);\n    mediaRecorderRef.current.pause();\n  };\n\n  const resumeRecording = () => {\n    setIsPaused(false);\n    timerRef.current = setInterval(() => {\n      setRecordingTime(prevTime => prevTime + 1);\n    }, 1000); // Restart the timer\n\n    mediaRecorderRef.current.resume();\n  };\n\n  const handleSendRec = async () => {\n    if (fileType && file) {\n      const recFile = {\n        file: file,\n        fileType: fileType\n      };\n      sendRecFunc(recFile, audioURL);\n      return;\n    }\n\n    let fileData, fileTp, localUrl;\n\n    mediaRecorderRef.current.onstop = async () => {\n      const audioBlob = new Blob(audioChunksRef.current, {\n        type: \"audio/mpeg\"\n      });\n      audioChunksRef.current = [];\n      localUrl = URL.createObjectURL(audioBlob);\n      fileTp = audioBlob.type;\n      fileData = audioBlob;\n      const recFile = {\n        file: fileData,\n        fileType: fileTp\n      };\n      sendRecFunc(recFile, localUrl);\n    };\n\n    setIsPaused(false);\n    clearInterval(timerRef.current);\n    mediaRecorderRef.current.stop();\n  };\n\n  const sendRecFunc = async (recFile, localUrl) => {\n    const uid = uuidv4();\n    const date = new Date();\n    const preData = {\n      track_id: uid,\n      agent_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.user_id,\n      agent_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.name,\n      manager_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.manager,\n      manager_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.manager_name,\n      team_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.team,\n      team_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.team_name,\n      req_from: \"AGENT_REPLY\",\n      method: \"media_reply\",\n      file_url: localUrl,\n      caption: \"\",\n      message_type: \"AUDIO\",\n      brand_number: currentUser.brand_number,\n      mobile: props.selectedMobile,\n      content: \"\",\n      status: \"pending\",\n      created: date,\n      message_content: \"\"\n    };\n    let newdata = [...props.convData.conversion, preData];\n    dispatch({\n      type: \"CHANGE_USER\",\n      payload: {\n        mobile: props.selectedMobile,\n        conversation: newdata,\n        name: props.convData.selectedName\n      }\n    });\n    resetState();\n\n    try {\n      const rec_url = await uploadRecording(recFile);\n      const {\n        data\n      } = await sendMessage({\n        track_id: uid,\n        agent_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.user_id,\n        agent_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.name,\n        manager_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.manager,\n        manager_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.manager_name,\n        team_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.team,\n        team_name: currentUser.user_type === \"admin\" ? \"admin\" : currentUser.team_name,\n        token: currentUser.parent_token,\n        user_id: currentUser.parent_id,\n        method: \"media_reply\",\n        attachment_url: rec_url,\n        caption: \"\",\n        message_type: \"AUDIO\",\n        brand_number: currentUser.brand_number,\n        mobile: props.selectedMobile,\n        content: \"\"\n      });\n\n      if (data.success === true) {\n        const updatedChat = newdata.map(chatdata => {\n          if (chatdata.track_id === data.track_id) {\n            return { ...chatdata,\n              status: \"Submitted\",\n              file_url: rec_url\n            };\n          }\n\n          return chatdata;\n        });\n        dispatch({\n          type: \"CHANGE_USER\",\n          payload: {\n            mobile: props.selectedMobile,\n            conversation: updatedChat,\n            name: props.convData.selectedName\n          }\n        });\n      } else {\n        resetState();\n        toast.error(data.message);\n      }\n    } catch (error) {\n      toast.error(error.message);\n      console.log(\"Error sending message:\", error);\n    }\n  };\n\n  const formatText = inputText => {\n    return inputText.replace(/([*~_])([^*~_]*[^*~_])\\1/g, (match, p1, p2) => {\n      if (p1 === \"*\") {\n        return `<b>${p2}</b>`;\n      } else if (p1 === \"_\") {\n        return `<i>${p2}</i>`;\n      } else if (p1 === \"~\") {\n        return `<s>${p2}</s>`;\n      }\n\n      return match; // Return the original text if no match\n    });\n  };\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: \"100%\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"emojiMobilecss\",\n      ref: pickerRef,\n      children: /*#__PURE__*/_jsxDEV(EmojiPicker, {\n        onEmojiClick: emojiselect,\n        open: emojiStatus,\n        height: 400,\n        width: \"auto\",\n        autoFocusSearch: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 755,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 754,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [isViewerOpen && /*#__PURE__*/_jsxDEV(ImageViewer, {\n        imageUrl: selectedImage,\n        onClose: closeImageViewer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 766,\n        columnNumber: 11\n      }, this), showPrev && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"popup\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"popupInner\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"cancelButton\",\n            onClick: togglePopup,\n            children: \"x\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 15\n          }, this), showPrev && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Preview:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"popupcontent\",\n              children: file.type.startsWith(\"video/\") ? /*#__PURE__*/_jsxDEV(\"video\", {\n                controls: true,\n                width: \"500\",\n                children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                  src: previewUrl,\n                  type: file.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 25\n                }, this), \"Your browser does not support the video tag.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 23\n              }, this) : file.type === \"application/pdf\" ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n                title: \"pdf\",\n                src: previewUrl,\n                style: {\n                  width: 200,\n                  height: 300\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n                src: previewUrl,\n                alt: \"Preview\",\n                style: {\n                  maxWidth: \"50%\",\n                  maxHeight: \"50%\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 19\n            }, this), fileSize > 10.0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-danger\",\n                children: \"*File size should be smaller than 10MB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 798,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                type: \"text\",\n                value: caption,\n                className: \"form-control mt-4\",\n                placeholder: \"Enter caption (optional)\",\n                onChange: e => setCaption(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 ms-2\",\n                children: buttonLoader === false ? /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: fileSize > 10.0 ? null : handleSend,\n                  className: \"btn btn-primary btn-lg chat-send waves-effect waves-light\",\n                  \"data-bs-toggle\": \"collapse\",\n                  \"data-bs-target\": \".chat-input-collapse1.show\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"bx bxs-send align-middle\",\n                    id: \"submit-btn\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 821,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-primary\",\n                  type: \"button\",\n                  disabled: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"spinner-border spinner-border-sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 832,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"sr-only\",\n                    children: \"Sending...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 827,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 775,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 770,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 769,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 764,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-input-section p-2 p-lg-2\",\n      id: \"keyboard\",\n      children: isOldMsg ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-center align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          style: {\n            color: \"red\"\n          },\n          className: \"me-4\",\n          children: \"This chat is older than 24h, Send template message\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"btn btn-success\",\n          onClick: () => setSendTemplatePopUp(true),\n          children: \"select template\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 855,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 851,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [error.error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-danger p-1 mb-2 ${error.errorType}`,\n          children: error.errorMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 866,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row g-0 align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"file_Upload\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 15\n          }, this), isRecording === false ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-auto\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"chat-input-links me-md-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"links-list-item\",\n                  \"data-bs-toggle\": \"tooltip\",\n                  \"data-bs-trigger\": \"hover\",\n                  \"data-bs-placement\": \"top\",\n                  title: \"file\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"btn btn-link text-decoration-none btn-lg waves-effect iconshovers\",\n                    \"data-bs-toggle\": \"collapse\",\n                    \"data-bs-target\": \"#chatinputmorecollapse\",\n                    \"aria-expanded\": \"false\",\n                    \"aria-controls\": \"chatinputmorecollapse\",\n                    onClick: () => setEmojiStatus(false),\n                    children: /*#__PURE__*/_jsxDEV(FaPlus, {\n                      className: \"align-middle iconsBgs\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 894,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 885,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 878,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"links-list-item\",\n                  \"data-bs-toggle\": \"tooltip\",\n                  \"data-bs-trigger\": \"hover\",\n                  \"data-bs-placement\": \"top\",\n                  title: \"Emoji\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"btn btn-link text-decoration-none btn-lg waves-effect emoji-btn iconshovers\",\n                    id: \"emoji-btn\",\n                    onClick: toggleEmoji,\n                    children: emojiStatus === false ? /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bx bx-smile iconsBgs align-middle\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 911,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"bx bx-x iconsBg align-middle\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 913,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 904,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 897,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"position-relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"chat-input-feedback\",\n                  children: \"Please Enter a Message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 922,\n                  columnNumber: 23\n                }, this), showPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preview\",\n                  dangerouslySetInnerHTML: {\n                    __html: formatText(text)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 937,\n                  columnNumber: 39\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  ref: textareaRef,\n                  onChange: handleTextarea,\n                  onKeyDown: e => handleKeyPress(e),\n                  className: \"form-control form-control-lg chat-input py-2\",\n                  rows: \"2\",\n                  value: text,\n                  id: \"chat-input\",\n                  placeholder: \"Type your message...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 921,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 920,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col audioContainer\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"audioBox\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"bx bxs-microphone align-middle mics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 955,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 954,\n              columnNumber: 19\n            }, this), !audioURL ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"audioTimer\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"audioplayPause\",\n                children: [\" \", /*#__PURE__*/_jsxDEV(FaRegStopCircle, {\n                  className: \"me-2\",\n                  onClick: prevRec\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 961,\n                  columnNumber: 25\n                }, this), isRecording && !isPaused && /*#__PURE__*/_jsxDEV(FaPause, {\n                  onClick: pauseRecording\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 27\n                }, this), isRecording && isPaused && /*#__PURE__*/_jsxDEV(FaPlay, {\n                  onClick: resumeRecording\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 966,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 959,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"audioFrequency\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"../icon-sound.gif\",\n                  alt: \"sound-graph\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 970,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"../icon-sound.gif\",\n                  className: \"ml-2\",\n                  alt: \"sound-graph\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 971,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"audiotimebox\",\n                children: [\" \", formatTime(recordingTime)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"audio\", {\n              controls: true,\n              src: audioURL\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 983,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"audioDelete\",\n              children: isRecording && /*#__PURE__*/_jsxDEV(MdDelete, {\n                onClick: stopRecording\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 986,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 985,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 953,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"chat-input-links ms-2 gap-md-1\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"links-list-item\",\n                children: sendButton ? /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: isRecording ? handleSendRec : handleSend,\n                  className: \"btn btn-primary btn-lg chat-send waves-effect waves-light\",\n                  \"data-bs-toggle\": \"collapse\",\n                  \"data-bs-target\": \".chat-input-collapse1.show\",\n                  title: \"Send message\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"bx bxs-send align-middle\",\n                    id: \"submit-btn\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1001,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 994,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: startRecording,\n                  className: \"btn btn-primary btn-lg chat-send waves-effect waves-light\",\n                  \"data-bs-toggle\": \"collapse\",\n                  \"data-bs-target\": \".chat-input-collapse1.show\",\n                  title: \"Send message\",\n                  children: /*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"bx bxs-microphone align-middle\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1014,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 992,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 991,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 990,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 871,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `chat-input-collapse chat-input-collapse1 collapse`,\n          id: \"chatinputmorecollapse\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card mb-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-body py-2 px-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"swiper chatinput-links\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"swiper-wrapper d-flex flex-column\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"swiper-slide swiperhover py-1 px-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center position-relative d-flex\",\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"file\",\n                        style: {\n                          display: \"none\"\n                        },\n                        id: \"attached-file\",\n                        onChange: fileHandler\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1033,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        htmlFor: \"attached-file\",\n                        className: \"attachFiles\",\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bx bx-paperclip iconsBgs bgBlue\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1039,\n                          columnNumber: 82\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"font-size-16 ms-2 text-body text-truncate\",\n                          children: \"Document\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1040,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1039,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1032,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1031,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"swiper-slide swiperhover py-1 px-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center position-relative d-flex\",\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        id: \"galleryfile-input\",\n                        type: \"file\",\n                        className: \"d-none\",\n                        onChange: fileHandler\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1060,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        htmlFor: \"galleryfile-input\",\n                        className: \"attachFiles\",\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bx bx-images iconsBgs bgOrange\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1067,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"font-size-16 ms-2 text-body text-truncate\",\n                          children: \"Image\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1068,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1066,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1059,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1058,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"swiper-slide swiperhover py-1 px-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center position-relative d-flex\",\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        id: \"audiofile-input\",\n                        type: \"file\",\n                        className: \"d-none\",\n                        onChange: fileHandler\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1075,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        htmlFor: \"audiofile-input\",\n                        className: \"attachFiles\",\n                        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"bx bx-video iconsBgs bgGreen\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1082,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"font-size-15 ms-2 text-body text-truncate\",\n                          children: \"Video\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1083,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1081,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1074,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1073,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1030,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1029,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1027,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1023,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 864,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 849,\n      columnNumber: 7\n    }, this), sendTemplatePopUp && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"popup-agent\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"assign-popup-content-agent\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            float: \"right\",\n            cursor: \"pointer\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"bx bx-x float-right\",\n            style: {\n              fontSize: \"26px\"\n            },\n            onClick: () => setSendTemplatePopUp(false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1171,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1170,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(SendTemplate, {\n            mobile: selectedMobileNumber\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1178,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1177,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1169,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1168,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 753,\n    columnNumber: 5\n  }, this);\n};\n\n_s(Input, \"xkUWtHfLncasgJkxSkTYvRPUzLw=\");\n\n_c = Input;\nexport default Input;\n\nvar _c;\n\n$RefreshReg$(_c, \"Input\");", "map": {"version": 3, "sources": ["D:/DataGen/authkey-chat-new/src/components/Input.jsx"], "names": ["React", "useContext", "useEffect", "useRef", "useState", "EmojiPicker", "AuthContext", "BASE_URL", "sendMessage", "ChatContext", "ChatState", "SendTemplate", "useDebounce", "MdDelete", "FaPlay", "FaPause", "FaRegStopCircle", "toast", "v4", "uuidv4", "FaPlus", "Input", "props", "saveSentences", "filteredSentences", "splitParagraphIntoSentences", "handleSuggestionClick", "textareaRef", "showPreview", "setShowPreview", "handleTranslationKeyDown", "handleTranslationInputChange", "pickerRef", "showPrev", "setShowPrev", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caption", "setCaption", "previewUrl", "setPreviewUrl", "file", "setFile", "fileSize", "setFileSize", "fileType", "setFileType", "emojiStatus", "setEmojiStatus", "sendButton", "setSendButton", "isRecording", "setIsRecording", "isPaused", "setIsPaused", "recordingTime", "setRecordingTime", "audioURL", "setAudioURL", "mediaRecorderRef", "audioChunksRef", "timerRef", "error", "setError", "errorMessage", "errorType", "currentUser", "dispatch", "isViewerOpen", "setIsViewerOpen", "selectedImage", "setSelectedImage", "isOldMsg", "setIsOldMsg", "sendTemplatePopUp", "setSendTemplatePopUp", "selectedMobileNumber", "text", "setText", "socket", "setAllChats", "reply", "setReply", "setTimeout", "clearInterval", "current", "togglePopup", "resetState", "undefined", "handleSend", "uid", "url", "uploadWhatsAppMedia", "msg", "data", "agent_id", "user_type", "user_id", "agent_name", "name", "manager_id", "manager", "manager_name", "team_id", "team", "team_name", "token", "parent_token", "parent_id", "method", "attachment_url", "message_type", "brand_number", "mobile", "selected<PERSON><PERSON><PERSON>", "content", "request_type", "success", "newdata", "convData", "conversion", "response", "type", "payload", "conversation", "<PERSON><PERSON><PERSON>", "message", "date", "Date", "track_id", "req_from", "file_url", "message_content", "image_caption", "resp_url", "status", "created", "tag_id", "JSON", "stringify", "id", "message_id", "messageid", "prevState", "map", "item", "then", "res", "updatedChat", "chatdata", "fileHandler", "e", "target", "files", "startsWith", "document", "body", "style", "overflow", "fileSizeInBytes", "size", "fileSizeInMB", "objectURL", "URL", "createObjectURL", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "uploadRecording", "rec", "Promise", "resolve", "reject", "FormData", "append", "fetch", "json", "resp", "console", "log", "handleKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "length", "ImageViewer", "imageUrl", "onClose", "closeImageViewer", "handleResize", "height", "newHeight", "Math", "min", "scrollHeight", "overflowY", "textarea", "addEventListener", "removeEventListener", "handleTextarea", "value", "timer1", "trim", "emit", "timer", "clearTimeout", "to<PERSON><PERSON><PERSON><PERSON>", "element", "getElementById", "classList", "remove", "emojiselect", "emojidata", "selectionStart", "selectionEnd", "newValue", "substring", "emoji", "focus", "setSelectionRange", "formatTime", "totalSeconds", "minutes", "String", "floor", "padStart", "seconds", "startRecording", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "MediaRecorder", "ondataavailable", "push", "onstop", "start", "setInterval", "prevTime", "stopRecording", "state", "stop", "getTracks", "for<PERSON>ach", "track", "prevRec", "audioBlob", "Blob", "pauseRecording", "pause", "resumeRecording", "resume", "handleSendRec", "recFile", "sendRecFunc", "fileData", "fileTp", "localUrl", "preData", "rec_url", "formatText", "inputText", "replace", "match", "p1", "p2", "width", "max<PERSON><PERSON><PERSON>", "maxHeight", "color", "__html", "display", "float", "cursor", "fontSize"], "mappings": ";;;AAAA,OAAOA,KAAP,IAAgBC,UAAhB,EAA4BC,SAA5B,EAAuCC,MAAvC,EAA+CC,QAA/C,QAA+D,OAA/D;AACA,OAAOC,WAAP,MAAwB,oBAAxB;AACA,SAASC,WAAT,QAA4B,wBAA5B;AACA,SAASC,QAAT,EAAmBC,WAAnB,QAAsC,YAAtC;AACA,SAASC,WAAT,QAA4B,wBAA5B;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,OAAOC,YAAP,MAAyB,gBAAzB;AACA,SAASC,WAAT,QAA4B,4BAA5B;AACA,SAASC,QAAT,QAAyB,gBAAzB;AACA,SAASC,MAAT,EAAiBC,OAAjB,EAA0BC,eAA1B,QAAiD,gBAAjD;AACA,SAASC,KAAT,QAAsB,gBAAtB;AACA,SAASC,EAAE,IAAIC,MAAf,QAA6B,MAA7B;AACA,SAASC,MAAT,QAAuB,iBAAvB;;;;AAEA,MAAMC,KAAK,GAAIC,KAAD,IAAW;AAAA;;AACvB,QAAM;AAAEC,IAAAA,aAAF;AAAiBC,IAAAA,iBAAjB;AAAoCC,IAAAA,2BAApC;AACJC,IAAAA,qBADI;AACmBC,IAAAA,WADnB;AACgCC,IAAAA,WADhC;AAC6CC,IAAAA,cAD7C;AAEJC,IAAAA,wBAFI;AAEsBC,IAAAA;AAFtB,MAEuDT,KAF7D;AAGA,QAAMU,SAAS,GAAG7B,MAAM,CAAC,IAAD,CAAxB,CAJuB,CAKvB;;AACA,QAAM,CAAC8B,QAAD,EAAWC,WAAX,IAA0B9B,QAAQ,CAAC,KAAD,CAAxC;AACA,QAAM,CAAC+B,YAAD,EAAeC,eAAf,IAAkChC,QAAQ,CAAC,KAAD,CAAhD;AACA,QAAM,CAACiC,OAAD,EAAUC,UAAV,IAAwBlC,QAAQ,CAAC,EAAD,CAAtC;AACA,QAAM,CAACmC,UAAD,EAAaC,aAAb,IAA8BpC,QAAQ,EAA5C;AACA,QAAM,CAACqC,IAAD,EAAOC,OAAP,IAAkBtC,QAAQ,CAAC,IAAD,CAAhC;AACA,QAAM,CAACuC,QAAD,EAAWC,WAAX,IAA0BxC,QAAQ,EAAxC;AACA,QAAM,CAACyC,QAAD,EAAWC,WAAX,IAA0B1C,QAAQ,EAAxC;AACA,QAAM,CAAC2C,WAAD,EAAcC,cAAd,IAAgC5C,QAAQ,CAAC,KAAD,CAA9C;AACA,QAAM,CAAC6C,UAAD,EAAaC,aAAb,IAA8B9C,QAAQ,CAAC,KAAD,CAA5C;AACA,QAAM,CAAC+C,WAAD,EAAcC,cAAd,IAAgChD,QAAQ,CAAC,KAAD,CAA9C;AACA,QAAM,CAACiD,QAAD,EAAWC,WAAX,IAA0BlD,QAAQ,CAAC,KAAD,CAAxC;AACA,QAAM,CAACmD,aAAD,EAAgBC,gBAAhB,IAAoCpD,QAAQ,CAAC,CAAD,CAAlD;AACA,QAAM,CAACqD,QAAD,EAAWC,WAAX,IAA0BtD,QAAQ,CAAC,IAAD,CAAxC;AACA,QAAMuD,gBAAgB,GAAGxD,MAAM,CAAC,IAAD,CAA/B;AACA,QAAMyD,cAAc,GAAGzD,MAAM,CAAC,EAAD,CAA7B;AACA,QAAM0D,QAAQ,GAAG1D,MAAM,CAAC,IAAD,CAAvB;AACA,QAAM,CAAC2D,KAAD,EAAQC,QAAR,IAAoB3D,QAAQ,CAAC;AACjC0D,IAAAA,KAAK,EAAE,KAD0B;AAEjCE,IAAAA,YAAY,EAAE,EAFmB;AAGjCC,IAAAA,SAAS,EAAE;AAHsB,GAAD,CAAlC;AAKA,QAAM;AAAEC,IAAAA;AAAF,MAAkBjE,UAAU,CAACK,WAAD,CAAlC;AACA,QAAM;AAAE6D,IAAAA;AAAF,MAAelE,UAAU,CAACQ,WAAD,CAA/B;AAEA,QAAM;AACJ2D,IAAAA,YADI;AAEJC,IAAAA,eAFI;AAGJC,IAAAA,aAHI;AAIJC,IAAAA,gBAJI;AAKJC,IAAAA,QALI;AAMJC,IAAAA,WANI;AAOJC,IAAAA,iBAPI;AAQJC,IAAAA,oBARI;AASJC,IAAAA,oBATI;AAUJC,IAAAA,IAVI;AAUEC,IAAAA,OAVF;AAWJC,IAAAA,MAXI;AAWIC,IAAAA,WAXJ;AAYJC,IAAAA,KAZI;AAYGC,IAAAA;AAZH,MAaFxE,SAAS,EAbb;AAcAR,EAAAA,SAAS,CAAC,MAAM;AACdiF,IAAAA,UAAU,CAAC,MAAM;AACfpB,MAAAA,QAAQ,CAAC;AACPD,QAAAA,KAAK,EAAE,KADA;AAEPE,QAAAA,YAAY,EAAE,EAFP;AAGPC,QAAAA,SAAS,EAAE;AAHJ,OAAD,CAAR;AAKD,KANS,EAMP,IANO,CAAV;AAOD,GARQ,EAQN,CAACH,KAAD,CARM,CAAT;AAWA5D,EAAAA,SAAS,CAAC,MAAM;AACd,WAAO,MAAMkF,aAAa,CAACvB,QAAQ,CAACwB,OAAV,CAA1B;AACD,GAFQ,EAEN,EAFM,CAAT;;AAGA,QAAMC,WAAW,GAAG,MAAM;AACxBpD,IAAAA,WAAW,CAAC,CAACD,QAAF,CAAX;AACAW,IAAAA,WAAW;AACXJ,IAAAA,aAAa;AACbE,IAAAA,OAAO,CAAC,IAAD,CAAP;AACAF,IAAAA,aAAa,CAAC,EAAD,CAAb;AACAM,IAAAA,WAAW;AACZ,GAPD;;AAQA,QAAMyC,UAAU,GAAG,MAAM;AACvB7B,IAAAA,WAAW,CAAC,IAAD,CAAX;AACAR,IAAAA,aAAa,CAAC,KAAD,CAAb;AACAE,IAAAA,cAAc,CAAC,KAAD,CAAd;AACAZ,IAAAA,aAAa,CAAC,EAAD,CAAb;AACAI,IAAAA,WAAW,CAAC4C,SAAD,CAAX;AACA1C,IAAAA,WAAW,CAAC0C,SAAD,CAAX;AACAtD,IAAAA,WAAW,CAAC,KAAD,CAAX;AACAQ,IAAAA,OAAO,CAAC,IAAD,CAAP;AACAN,IAAAA,eAAe,CAAC,KAAD,CAAf;AACA0C,IAAAA,OAAO,CAAC,EAAD,CAAP;AACAxC,IAAAA,UAAU,CAAC,EAAD,CAAV;AACD,GAZD;;AAaA,QAAMmD,UAAU,GAAG,YAAY;AAC7BzC,IAAAA,cAAc,CAAC,KAAD,CAAd;AACA,UAAM0C,GAAG,GAAGvE,MAAM,EAAlB;;AAEA,QAAIsB,IAAJ,EAAU;AACRL,MAAAA,eAAe,CAAC,IAAD,CAAf;;AACA,UAAI;AAGF,YAAIuD,GAAG,GAAG,MAAMC,mBAAmB,EAAnC;AAEA,cAAMC,GAAG,GAAG,EAAZ;AACA,cAAM;AAAEC,UAAAA;AAAF,YAAW,MAAMtF,WAAW,CAAC;AACjCuF,UAAAA,QAAQ,EAAE7B,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAAC+B,OAD/B;AAEjCC,UAAAA,UAAU,EACRhC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,OAApC,GAA8C9B,WAAW,CAACiC,IAH3B;AAIjCC,UAAAA,UAAU,EACRlC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAACmC,OALvB;AAMjCC,UAAAA,YAAY,EACVpC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GACI,OADJ,GAEI9B,WAAW,CAACoC,YATe;AAUjCC,UAAAA,OAAO,EAAErC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAACsC,IAV9B;AAWjCC,UAAAA,SAAS,EACPvC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,OAApC,GAA8C9B,WAAW,CAACuC,SAZ3B;AAajCC,UAAAA,KAAK,EAAExC,WAAW,CAACyC,YAbc;AAcjCV,UAAAA,OAAO,EAAE/B,WAAW,CAAC0C,SAdY;AAejCC,UAAAA,MAAM,EAAE,aAfyB;AAgBjCC,UAAAA,cAAc,EAAEnB,GAhBiB;AAiBjCtD,UAAAA,OAAO,EAAEA,OAjBwB;AAkBjC0E,UAAAA,YAAY,EAAElE,QAlBmB;AAmBjCmE,UAAAA,YAAY,EAAE9C,WAAW,CAAC8C,YAnBO;AAoBjCC,UAAAA,MAAM,EAAE3F,KAAK,CAAC4F,cApBmB;AAqBjCC,UAAAA,OAAO,EAAEtC,IArBwB;AAsBjCuC,UAAAA,YAAY,EAAE;AAtBmB,SAAD,CAAlC;;AAyBA,YAAItB,IAAI,CAACuB,OAAL,KAAiB,IAArB,EAA2B;AACzB,cAAIC,OAAO,GAAG,CAAC,GAAGhG,KAAK,CAACiG,QAAN,CAAeC,UAAnB,EAA+B1B,IAAI,CAAC2B,QAApC,CAAd;AAEAtD,UAAAA,QAAQ,CAAC;AACPuD,YAAAA,IAAI,EAAE,aADC;AAEPC,YAAAA,OAAO,EAAE;AACPV,cAAAA,MAAM,EAAE3F,KAAK,CAAC4F,cADP;AAEPU,cAAAA,YAAY,EAAEN,OAFP;AAGPnB,cAAAA,IAAI,EAAE7E,KAAK,CAACiG,QAAN,CAAeM;AAHd;AAFF,WAAD,CAAR;AAQAtC,UAAAA,UAAU;AACX,SAZD,MAYO;AACLA,UAAAA,UAAU;AACVtE,UAAAA,KAAK,CAAC6C,KAAN,CAAYgC,IAAI,CAACgC,OAAjB;AACA/D,UAAAA,QAAQ,CAAC;AACPD,YAAAA,KAAK,EAAE,IADA;AAEPE,YAAAA,YAAY,EAAE8B,IAAI,CAACgC,OAFZ;AAGP7D,YAAAA,SAAS,EAAE;AAHJ,WAAD,CAAR;AAKA7B,UAAAA,eAAe,CAAC,KAAD,CAAf;AACD;AACF,OArDD,CAqDE,OAAO0B,KAAP,EAAc;AACd7C,QAAAA,KAAK,CAAC6C,KAAN,CAAYA,KAAK,CAACgE,OAAlB;AACA1F,QAAAA,eAAe,CAAC,KAAD,CAAf;AACAmD,QAAAA,UAAU;AACX;AACF,KA5DD,MA4DO;AAGL,YAAMwC,IAAI,GAAG,IAAIC,IAAJ,EAAb;AACA,YAAMnC,GAAG,GAAG;AACVE,QAAAA,QAAQ,EAAE7B,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAAC+B,OADtD;AAEVC,QAAAA,UAAU,EACRhC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,OAApC,GAA8C9B,WAAW,CAACiC,IAHlD;AAIVC,QAAAA,UAAU,EACRlC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAACmC,OAL9C;AAMVC,QAAAA,YAAY,EACVpC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GACI,OADJ,GAEI9B,WAAW,CAACoC,YATR;AAUVC,QAAAA,OAAO,EAAErC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAACsC,IAVrD;AAWVC,QAAAA,SAAS,EACPvC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,OAApC,GAA8C9B,WAAW,CAACuC,SAZlD;AAaVwB,QAAAA,QAAQ,EAAEvC,GAbA;AAcVuB,QAAAA,MAAM,EAAE3F,KAAK,CAAC4F,cAdJ;AAeVF,QAAAA,YAAY,EAAE9C,WAAW,CAAC8C,YAfhB;AAgBVD,QAAAA,YAAY,EAAE,MAhBJ;AAiBVmB,QAAAA,QAAQ,EAAE,aAjBA;AAkBVC,QAAAA,QAAQ,EAAE,EAlBA;AAmBVC,QAAAA,eAAe,EAAEvD,IAnBP;AAoBVwD,QAAAA,aAAa,EAAE,EApBL;AAqBVC,QAAAA,QAAQ,EAAE,EArBA;AAsBVlB,QAAAA,YAAY,EAAE,EAtBJ;AAuBVmB,QAAAA,MAAM,EAAE,SAvBE;AAwBVC,QAAAA,OAAO,EAAET,IAxBC;AAyBVU,QAAAA,MAAM,EAAExD,KAAK,GACTyD,IAAI,CAACC,SAAL,CAAe;AACfC,UAAAA,EAAE,EAAE3D,KAAK,CAAC2D,EADK;AAEfC,UAAAA,UAAU,EAAE5D,KAAK,CAAC6D,SAFH;AAGf/B,UAAAA,YAAY,EAAE,MAHC;AAIfmB,UAAAA,QAAQ,EAAEjD,KAAK,CAACiD,QAJD;AAKfE,UAAAA,eAAe,EAAEnD,KAAK,CAACmD,eALR;AAMfD,UAAAA,QAAQ,EAAE;AANK,SAAf,CADS,GAST;AAlCM,OAAZ;AAwCA,UAAIb,OAAO,GAAG,CAAC,GAAGhG,KAAK,CAACiG,QAAN,CAAeC,UAAnB,EAA+B3B,GAA/B,CAAd;AACAX,MAAAA,QAAQ,CAAC,IAAD,CAAR;AAEAf,MAAAA,QAAQ,CAAC;AACPuD,QAAAA,IAAI,EAAE,aADC;AAEPC,QAAAA,OAAO,EAAE;AACPV,UAAAA,MAAM,EAAE3F,KAAK,CAAC4F,cADP;AAEPU,UAAAA,YAAY,EAAEN,OAFP;AAGPnB,UAAAA,IAAI,EAAE7E,KAAK,CAACiG,QAAN,CAAeM;AAHd;AAFF,OAAD,CAAR;AASA,UAAI/B,IAAI,GAAG;AACTC,QAAAA,QAAQ,EAAE7B,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAAC+B,OADvD;AAETC,QAAAA,UAAU,EACRhC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,OAApC,GAA8C9B,WAAW,CAACiC,IAHnD;AAITC,QAAAA,UAAU,EACRlC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAACmC,OAL/C;AAMTC,QAAAA,YAAY,EACVpC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GACI,OADJ,GAEI9B,WAAW,CAACoC,YATT;AAUTC,QAAAA,OAAO,EAAErC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAACsC,IAVtD;AAWTC,QAAAA,SAAS,EACPvC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,OAApC,GAA8C9B,WAAW,CAACuC,SAZnD;AAaTwB,QAAAA,QAAQ,EAAEvC,GAbD;AAcTgB,QAAAA,KAAK,EAAExC,WAAW,CAACyC,YAdV;AAeTV,QAAAA,OAAO,EAAE/B,WAAW,CAAC0C,SAfZ;AAgBTC,QAAAA,MAAM,EAAE,OAhBC;AAiBTO,QAAAA,YAAY,EAAE,EAjBL;AAkBTJ,QAAAA,YAAY,EAAE9C,WAAW,CAAC8C,YAlBjB;AAmBTC,QAAAA,MAAM,EAAE3F,KAAK,CAAC4F,cAnBL;AAoBTC,QAAAA,OAAO,EAAEtC,IApBA;AAqBT4D,QAAAA,MAAM,EAAExD,KAAK,GACXyD,IAAI,CAACC,SAAL,CAAe;AACfC,UAAAA,EAAE,EAAE3D,KAAK,CAAC2D,EADK;AAEfC,UAAAA,UAAU,EAAE5D,KAAK,CAAC6D,SAFH;AAGf/B,UAAAA,YAAY,EAAE,MAHC;AAIfmB,UAAAA,QAAQ,EAAEjD,KAAK,CAACiD,QAJD;AAKfE,UAAAA,eAAe,EAAEnD,KAAK,CAACmD,eALR;AAMfD,UAAAA,QAAQ,EAAE;AANK,SAAf,CADW,GASX;AA9BO,OAAX,CAxDK,CAyFL;AACA;AACA;AACA;AACA;AACA;;AAEAnD,MAAAA,WAAW,CAAE+D,SAAD,IACVA,SAAS,CAACC,GAAV,CAAeC,IAAD,IAAU;AACtB,YAAIA,IAAI,CAAChC,MAAL,KAAgBrC,oBAApB,EAA0C;AACxC,iBAAO,EAAE,GAAGqE,IAAL;AAAWf,YAAAA,QAAQ,EAAE,aAArB;AAAoCf,YAAAA,OAAO,EAAEtC,IAA7C;AAAmD2D,YAAAA,OAAO,EAAET;AAA5D,WAAP;AACD;;AACD,eAAOkB,IAAP;AACD,OALD,CADS,CAAX;AAQAnE,MAAAA,OAAO,CAAC,EAAD,CAAP,CAxGK,CA0GL;;AACAtE,MAAAA,WAAW,CAACsF,IAAD,CAAX,CAAkBoD,IAAlB,CAAwBC,GAAD,IAAS;AAC9B,YAAIA,GAAG,CAACrD,IAAJ,CAASuB,OAAT,KAAqB,IAAzB,EAA+B;AAC7B,gBAAM+B,WAAW,GAAG9B,OAAO,CAAC0B,GAAR,CAAaK,QAAD,IAAc;AAC5C,gBAAIA,QAAQ,CAACpB,QAAT,KAAsBkB,GAAG,CAACrD,IAAJ,CAASmC,QAAnC,EAA6C;AAC3C,qBAAO,EAAE,GAAGoB,QAAL;AAAed,gBAAAA,MAAM,EAAE;AAAvB,eAAP;AACD;;AACD,mBAAOc,QAAP;AACD,WALmB,CAApB;AAMAlF,UAAAA,QAAQ,CAAC;AACPuD,YAAAA,IAAI,EAAE,aADC;AAEPC,YAAAA,OAAO,EAAE;AACPV,cAAAA,MAAM,EAAE3F,KAAK,CAAC4F,cADP;AAEPU,cAAAA,YAAY,EAAEwB,WAFP;AAGPjD,cAAAA,IAAI,EAAE7E,KAAK,CAACiG,QAAN,CAAeM;AAHd;AAFF,WAAD,CAAR,CAP6B,CAe7B;AACD,SAhBD,MAgBO;AACL,cACEsB,GAAG,CAACrD,IAAJ,CAASgC,OAAT,KACA,mDAFF,EAGE;AACArD,YAAAA,WAAW,CAAC,IAAD,CAAX;AACD;;AAEDV,UAAAA,QAAQ,CAAC;AACPD,YAAAA,KAAK,EAAE,IADA;AAEPE,YAAAA,YAAY,EAAEmF,GAAG,CAACrD,IAAJ,CAASgC,OAFhB;AAGP7D,YAAAA,SAAS,EAAE;AAHJ,WAAD,CAAR;AAKD;AACF,OA/BD;AAgCD;AACF,GA5MD;;AAgNA,QAAMqF,WAAW,GAAIC,CAAD,IAAO;AACzBrH,IAAAA,WAAW,CAAC,CAACD,QAAF,CAAX;AACA,UAAMQ,IAAI,GAAG8G,CAAC,CAACC,MAAF,CAASC,KAAT,CAAe,CAAf,CAAb;;AACA,QAAIF,CAAC,CAACC,MAAF,CAASC,KAAT,CAAe,CAAf,EAAkB/B,IAAlB,CAAuBgC,UAAvB,CAAkC,OAAlC,CAAJ,EAAgD;AAC9C5G,MAAAA,WAAW,CAAC,OAAD,CAAX;AACD,KAFD,MAEO,IAAIyG,CAAC,CAACC,MAAF,CAASC,KAAT,CAAe,CAAf,EAAkB/B,IAAlB,CAAuBgC,UAAvB,CAAkC,OAAlC,CAAJ,EAAgD;AACrD5G,MAAAA,WAAW,CAAC,OAAD,CAAX;AACD,KAFM,MAEA;AACLA,MAAAA,WAAW,CAAC,MAAD,CAAX;AACD;;AAED6G,IAAAA,QAAQ,CAACC,IAAT,CAAcC,KAAd,CAAoBC,QAApB,GAA+B,CAAC7H,QAAD,GAAY,QAAZ,GAAuB,MAAtD;;AAEA,QAAIQ,IAAJ,EAAU;AACR,YAAMsH,eAAe,GAAGtH,IAAI,CAACuH,IAA7B;AACA,YAAMC,YAAY,GAAGF,eAAe,IAAI,OAAO,IAAX,CAApC;AACAnH,MAAAA,WAAW,CAACqH,YAAD,CAAX;;AACA,UAAIxH,IAAI,IAAIA,IAAI,CAACiF,IAAL,CAAUgC,UAAV,CAAqB,QAArB,CAAZ,EAA4C;AAC1C,cAAMQ,SAAS,GAAGC,GAAG,CAACC,eAAJ,CAAoB3H,IAApB,CAAlB;AACAD,QAAAA,aAAa,CAAC0H,SAAD,CAAb;AACAxH,QAAAA,OAAO,CAACD,IAAD,CAAP;AACA;AACD;;AACD,YAAM4H,MAAM,GAAG,IAAIC,UAAJ,EAAf;;AACAD,MAAAA,MAAM,CAACE,SAAP,GAAmB,MAAM;AACvB/H,QAAAA,aAAa,CAAC6H,MAAM,CAACG,MAAR,CAAb;AACD,OAFD;;AAGAH,MAAAA,MAAM,CAACI,aAAP,CAAqBhI,IAArB;AACAC,MAAAA,OAAO,CAACD,IAAD,CAAP;AACD,KAhBD,MAgBO;AACLC,MAAAA,OAAO,CAAC,IAAD,CAAP;AACAF,MAAAA,aAAa,CAAC,EAAD,CAAb;AACD;AACF,GAjCD;;AAkCA,QAAMkI,eAAe,GAAG,MAAOC,GAAP,IAAe;AACrC,QAAI;AACF,UAAI7E,IAAI,GAAG,MAAM,IAAI8E,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AAChD,cAAMhF,IAAI,GAAG,IAAIiF,QAAJ,EAAb;AACAjF,QAAAA,IAAI,CAACkF,MAAL,CAAY,SAAZ,EAAuBL,GAAG,CAAClI,IAA3B,EAAiC,eAAjC;AACAqD,QAAAA,IAAI,CAACkF,MAAL,CAAY,UAAZ,EAAwB,SAAxB;AACAlF,QAAAA,IAAI,CAACkF,MAAL,CAAY,UAAZ,EAAwBL,GAAG,CAAC9H,QAA5B;AACAiD,QAAAA,IAAI,CAACkF,MAAL,CAAY,iBAAZ,EAA+B,mBAA/B;AACAlF,QAAAA,IAAI,CAACkF,MAAL,CAAY,SAAZ,EAAuB9G,WAAW,CAAC0C,SAAnC;AACAd,QAAAA,IAAI,CAACkF,MAAL,CAAY,OAAZ,EAAqB9G,WAAW,CAACyC,YAAjC;AACAb,QAAAA,IAAI,CAACkF,MAAL,CAAY,QAAZ,EAAsB,gBAAtB;AAEAC,QAAAA,KAAK,CAAE,GAAE1K,QAAS,yBAAb,EAAuC;AAC1CsG,UAAAA,MAAM,EAAE,MADkC;AAE1C+C,UAAAA,IAAI,EAAE9D;AAFoC,SAAvC,CAAL,CAGGoD,IAHH,CAGSsB,MAAD,IAAY;AAClBA,UAAAA,MAAM,CAACU,IAAP,GAAchC,IAAd,CAAoBiC,IAAD,IAAU;AAC3BN,YAAAA,OAAO,CAACM,IAAI,CAACxF,GAAN,CAAP;AACD,WAFD;AAGD,SAPD;AAQD,OAlBgB,CAAjB,CADE,CAoBF;;AACA,aAAOG,IAAP;AACD,KAtBD,CAsBE,OAAOhC,KAAP,EAAc;AACdsH,MAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ;AACD;AACF,GA1BD;;AA2BA,QAAMzF,mBAAmB,GAAG,YAAY;AACtC,QAAI;AACF,UAAIE,IAAI,GAAG,MAAM,IAAI8E,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AAChD,cAAMhF,IAAI,GAAG,IAAIiF,QAAJ,EAAb;AACAjF,QAAAA,IAAI,CAACkF,MAAL,CAAY,SAAZ,EAAuBvI,IAAvB;AACAqD,QAAAA,IAAI,CAACkF,MAAL,CAAY,UAAZ,EAAwB,SAAxB;AACAlF,QAAAA,IAAI,CAACkF,MAAL,CAAY,UAAZ,EAAwBnI,QAAxB;AACAiD,QAAAA,IAAI,CAACkF,MAAL,CAAY,SAAZ,EAAuB9G,WAAW,CAAC0C,SAAnC;AACAd,QAAAA,IAAI,CAACkF,MAAL,CAAY,OAAZ,EAAqB9G,WAAW,CAACyC,YAAjC;AACAb,QAAAA,IAAI,CAACkF,MAAL,CAAY,QAAZ,EAAsB,QAAtB;AAEAC,QAAAA,KAAK,CAAE,GAAE1K,QAAS,yBAAb,EAAuC;AAC1CsG,UAAAA,MAAM,EAAE,MADkC;AAE1C+C,UAAAA,IAAI,EAAE9D;AAFoC,SAAvC,CAAL,CAGGoD,IAHH,CAGSsB,MAAD,IAAY;AAClBA,UAAAA,MAAM,CAACU,IAAP,GAAchC,IAAd,CAAoBiC,IAAD,IAAU;AAC3BN,YAAAA,OAAO,CAACM,IAAI,CAACxF,GAAN,CAAP;AACD,WAFD;AAGD,SAPD;AAQD,OAjBgB,CAAjB,CADE,CAmBF;;AACA,aAAOG,IAAP;AACD,KArBD,CAqBE,OAAOhC,KAAP,EAAc;AACdsH,MAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ;AACD;AACF,GAzBD;;AA2BA,QAAMC,cAAc,GAAIC,KAAD,IAAW;AAChC,QAAIA,KAAK,CAACC,GAAN,KAAc,OAAd,IAAyBD,KAAK,CAACE,QAAnC,EAA6C,CAC3C;AACD,KAFD,MAEO,IAAIF,KAAK,CAACC,GAAN,KAAc,OAAlB,EAA2B;AAChCD,MAAAA,KAAK,CAACG,cAAN;AACAjG,MAAAA,UAAU;AACX;;AACD,QAAI8F,KAAK,CAACC,GAAN,KAAc,KAAd,IAAuBhK,iBAAiB,CAACmK,MAAlB,GAA2B,CAAtD,EAAyD;AACvDJ,MAAAA,KAAK,CAACG,cAAN;AACAhK,MAAAA,qBAAqB,CAACF,iBAAiB,CAAC,CAAD,CAAlB,CAArB;AACD,KAV+B,CAYhC;;;AACA,QAAIM,wBAAJ,EAA8B;AAC5BA,MAAAA,wBAAwB,CAACyJ,KAAD,CAAxB;AACD;AACF,GAhBD;;AAkBA,QAAMK,WAAW,GAAG,QAA2B;AAAA,QAA1B;AAAEC,MAAAA,QAAF;AAAYC,MAAAA;AAAZ,KAA0B;AAC7C,wBACE;AAAK,MAAA,SAAS,EAAC,OAAf;AAAA,6BACE;AAAK,QAAA,SAAS,EAAC,YAAf;AAAA,gCACE;AAAK,UAAA,GAAG,EAAED,QAAV;AAAoB,UAAA,GAAG,EAAC,SAAxB;AAAkC,UAAA,SAAS,EAAC;AAA5C;AAAA;AAAA;AAAA;AAAA,gBADF,eAEE;AAAQ,UAAA,SAAS,EAAC,cAAlB;AAAiC,UAAA,OAAO,EAAEC,OAA1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAFF;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,YADF;AAUD,GAXD;;AAYA,QAAMC,gBAAgB,GAAG,MAAM;AAC7B1H,IAAAA,eAAe,CAAC,KAAD,CAAf;AACAE,IAAAA,gBAAgB,CAAC,EAAD,CAAhB;AACD,GAHD;;AAIArE,EAAAA,SAAS,CAAC,MAAM;AACd,UAAM8L,YAAY,GAAG,MAAM;AACzB,UAAIrK,WAAW,CAAC0D,OAAhB,EAAyB;AACvB1D,QAAAA,WAAW,CAAC0D,OAAZ,CAAoBwE,KAApB,CAA0BoC,MAA1B,GAAmC,MAAnC;AACA,cAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,CAASzK,WAAW,CAAC0D,OAAZ,CAAoBgH,YAA7B,EAA2C,GAA3C,CAAlB;AACA1K,QAAAA,WAAW,CAAC0D,OAAZ,CAAoBwE,KAApB,CAA0BoC,MAA1B,GAAoC,GAAEC,SAAU,IAAhD;AACAvK,QAAAA,WAAW,CAAC0D,OAAZ,CAAoBwE,KAApB,CAA0ByC,SAA1B,GACE3K,WAAW,CAAC0D,OAAZ,CAAoBgH,YAApB,GAAmC,GAAnC,GAAyC,MAAzC,GAAkD,QADpD;AAED;AACF,KARD;;AAUA,UAAME,QAAQ,GAAG5K,WAAW,CAAC0D,OAA7B;AACAkH,IAAAA,QAAQ,SAAR,IAAAA,QAAQ,WAAR,YAAAA,QAAQ,CAAEC,gBAAV,CAA2B,OAA3B,EAAoCR,YAApC;AAEA,WAAO,MAAM;AACXO,MAAAA,QAAQ,SAAR,IAAAA,QAAQ,WAAR,YAAAA,QAAQ,CAAEE,mBAAV,CAA8B,OAA9B,EAAuCT,YAAvC;AACD,KAFD;AAGD,GAjBQ,EAiBN,EAjBM,CAAT;;AAkBA,QAAMU,cAAc,GAAInD,CAAD,IAAO;AAC5BvG,IAAAA,cAAc,CAAC,KAAD,CAAd;AACA8B,IAAAA,OAAO,CAACyE,CAAC,CAACC,MAAF,CAASmD,KAAV,CAAP,CAF4B,CAG5B;;AACA,QAAIpD,CAAC,CAACC,MAAF,CAASmD,KAAT,KAAmB,EAAvB,EAA2B;AACzBzJ,MAAAA,aAAa,CAAC,KAAD,CAAb;AACD,KAFD,MAEO;AACLA,MAAAA,aAAa,CAAC,IAAD,CAAb;AACD,KAR2B,CAU5B;;;AACA,QAAInB,4BAAJ,EAAkC;AAChCA,MAAAA,4BAA4B,CAACwH,CAAC,CAACC,MAAF,CAASmD,KAAV,CAA5B;AACD;AACF,GAdD;;AAiBAzM,EAAAA,SAAS,CAAC,MAAM;AACd,QAAI,CAAC6E,MAAL,EAAa;;AACb,QAAIF,IAAI,CAAC8G,MAAL,GAAc,CAAlB,EAAqB;AACnBzI,MAAAA,aAAa,CAAC,IAAD,CAAb;AACD,KAFD,MAEO;AACLA,MAAAA,aAAa,CAAC,KAAD,CAAb;AACD;;AACD,UAAM0J,MAAM,GAAGzH,UAAU,CAAC,MAAM;AAC9B,UAAIN,IAAI,CAACgI,IAAL,EAAJ,EAAiB;AACf9H,QAAAA,MAAM,CAAC+H,IAAP,CAAY,SAAZ,EAAuB5I,WAAvB;AACD;AACF,KAJwB,EAItB,IAJsB,CAAzB;AAMA,UAAM6I,KAAK,GAAG5H,UAAU,CAAC,MAAM;AAC7B,UAAIN,IAAI,CAACgI,IAAL,EAAJ,EAAiB;AACf9H,QAAAA,MAAM,CAAC+H,IAAP,CAAY,UAAZ,EAAwB5I,WAAxB;AACD;AACF,KAJuB,EAIrB,IAAI,EAAJ,GAAS,IAJY,CAAxB;AAMA,WAAO,MAAM;AACX8I,MAAAA,YAAY,CAACJ,MAAD,CAAZ;AACAI,MAAAA,YAAY,CAACD,KAAD,CAAZ;AAED,KAJD;AAKD,GAxBQ,EAwBN,CAAClI,IAAD,EAAOE,MAAP,CAxBM,CAAT,CA5buB,CAsdvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;;AACA,QAAMkI,WAAW,GAAG,MAAM;AACxB,UAAMC,OAAO,GAAGvD,QAAQ,CAACwD,cAAT,CAAwB,uBAAxB,CAAhB;;AACA,QAAID,OAAJ,EAAa;AACXA,MAAAA,OAAO,CAACE,SAAR,CAAkBC,MAAlB,CAAyB,MAAzB;AACD;;AAEDrK,IAAAA,cAAc,CAAC,CAACD,WAAF,CAAd;AACD,GAPD;;AAQA,QAAMuK,WAAW,GAAIC,SAAD,IAAe;AACjC,UAAM;AAAElI,MAAAA;AAAF,QAAc1D,WAApB;AACA,UAAM;AAAE6L,MAAAA,cAAF;AAAkBC,MAAAA;AAAlB,QAAmCpI,OAAzC;AACA,UAAMqI,QAAQ,GACZ7I,IAAI,CAAC8I,SAAL,CAAe,CAAf,EAAkBH,cAAlB,IACAD,SAAS,CAACK,KADV,GAEA/I,IAAI,CAAC8I,SAAL,CAAeF,YAAf,CAHF;AAIA3I,IAAAA,OAAO,CAAC4I,QAAD,CAAP;AACArI,IAAAA,OAAO,CAACwI,KAAR;AACAxI,IAAAA,OAAO,CAACyI,iBAAR,CACEN,cAAc,GAAGD,SAAS,CAACK,KAAV,CAAgBjC,MADnC,EAEE6B,cAAc,GAAGD,SAAS,CAACK,KAAV,CAAgBjC,MAFnC;AAID,GAbD;;AAeA,QAAMoC,UAAU,GAAIC,YAAD,IAAkB;AACnC,UAAMC,OAAO,GAAGC,MAAM,CAAC/B,IAAI,CAACgC,KAAL,CAAWH,YAAY,GAAG,EAA1B,CAAD,CAAN,CAAsCI,QAAtC,CAA+C,CAA/C,EAAkD,GAAlD,CAAhB;AACA,UAAMC,OAAO,GAAGH,MAAM,CAACF,YAAY,GAAG,EAAhB,CAAN,CAA0BI,QAA1B,CAAmC,CAAnC,EAAsC,GAAtC,CAAhB;AACA,WAAQ,GAAEH,OAAQ,IAAGI,OAAQ,EAA7B;AACD,GAJD;;AAKA,QAAMC,cAAc,GAAG,YAAY;AACjCpL,IAAAA,aAAa,CAAC,IAAD,CAAb;AACAE,IAAAA,cAAc,CAAC,IAAD,CAAd;AACAE,IAAAA,WAAW,CAAC,KAAD,CAAX;AACAE,IAAAA,gBAAgB,CAAC,CAAD,CAAhB;AACA,UAAM+K,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAV,CAAuBC,YAAvB,CAAoC;AAAEC,MAAAA,KAAK,EAAE;AAAT,KAApC,CAArB;AAEAhL,IAAAA,gBAAgB,CAAC0B,OAAjB,GAA2B,IAAIuJ,aAAJ,CAAkBL,MAAlB,CAA3B;;AACA5K,IAAAA,gBAAgB,CAAC0B,OAAjB,CAAyBwJ,eAAzB,GAA4CtD,KAAD,IAAW;AACpD3H,MAAAA,cAAc,CAACyB,OAAf,CAAuByJ,IAAvB,CAA4BvD,KAAK,CAACzF,IAAlC;AACD,KAFD;;AAIAnC,IAAAA,gBAAgB,CAAC0B,OAAjB,CAAyB0J,MAAzB,GAAkC,YAAY;AAE5CnL,MAAAA,cAAc,CAACyB,OAAf,GAAyB,EAAzB;AACD,KAHD;;AAKA1B,IAAAA,gBAAgB,CAAC0B,OAAjB,CAAyB2J,KAAzB;AAEAnL,IAAAA,QAAQ,CAACwB,OAAT,GAAmB4J,WAAW,CAAC,MAAM;AACnCzL,MAAAA,gBAAgB,CAAE0L,QAAD,IAAcA,QAAQ,GAAG,CAA1B,CAAhB;AACD,KAF6B,EAE3B,IAF2B,CAA9B;AAGD,GAtBD;;AAuBA,QAAMC,aAAa,GAAG,YAAY;AAChC/J,IAAAA,aAAa,CAACvB,QAAQ,CAACwB,OAAV,CAAb;;AAGA,QAAI1B,gBAAgB,CAAC0B,OAAjB,IAA4B1B,gBAAgB,CAAC0B,OAAjB,CAAyB+J,KAAzB,KAAmC,UAAnE,EAA+E;AAC7EzL,MAAAA,gBAAgB,CAAC0B,OAAjB,CAAyBgK,IAAzB;AACA1L,MAAAA,gBAAgB,CAAC0B,OAAjB,CAAyBkJ,MAAzB,CAAgCe,SAAhC,GAA4CC,OAA5C,CAAoDC,KAAK,IAAIA,KAAK,CAACH,IAAN,EAA7D;AACD;;AACDzL,IAAAA,cAAc,CAACyB,OAAf,GAAyB,EAAzB;AACA3B,IAAAA,WAAW,CAAC,IAAD,CAAX;AACAR,IAAAA,aAAa,CAAC,KAAD,CAAb;AACAE,IAAAA,cAAc,CAAC,KAAD,CAAd;AACAE,IAAAA,WAAW,CAAC,KAAD,CAAX;AACAR,IAAAA,WAAW,CAAC0C,SAAD,CAAX;AACA9C,IAAAA,OAAO,CAAC,IAAD,CAAP;AACA6C,IAAAA,UAAU;AACV,UAAMiJ,SAAS,CAACC,YAAV,CAAuBC,YAAvB,CAAoC;AAAEC,MAAAA,KAAK,EAAE;AAAT,KAApC,CAAN;AACD,GAjBD;;AAkBA,QAAMc,OAAO,GAAG,MAAM;AACpBnM,IAAAA,WAAW,CAAC,KAAD,CAAX;AACA8B,IAAAA,aAAa,CAACvB,QAAQ,CAACwB,OAAV,CAAb;;AACA1B,IAAAA,gBAAgB,CAAC0B,OAAjB,CAAyB0J,MAAzB,GAAkC,YAAY;AAC5C,YAAMW,SAAS,GAAG,IAAIC,IAAJ,CAAS/L,cAAc,CAACyB,OAAxB,EAAiC;AAAEqC,QAAAA,IAAI,EAAE;AAAR,OAAjC,CAAlB;AACA9D,MAAAA,cAAc,CAACyB,OAAf,GAAyB,EAAzB;AACA3B,MAAAA,WAAW,CAACyG,GAAG,CAACC,eAAJ,CAAoBsF,SAApB,CAAD,CAAX;AACA,YAAMhI,IAAI,GAAGgI,SAAS,CAAChI,IAAvB;AACA5E,MAAAA,WAAW,CAAC4E,IAAD,CAAX;AACAhF,MAAAA,OAAO,CAACgN,SAAD,CAAP;AACD,KAPD;;AAQA/L,IAAAA,gBAAgB,CAAC0B,OAAjB,CAAyBgK,IAAzB;AACD,GAZD;;AAaA,QAAMO,cAAc,GAAG,MAAM;AAC3BtM,IAAAA,WAAW,CAAC,IAAD,CAAX;AACA8B,IAAAA,aAAa,CAACvB,QAAQ,CAACwB,OAAV,CAAb;AACA1B,IAAAA,gBAAgB,CAAC0B,OAAjB,CAAyBwK,KAAzB;AACD,GAJD;;AAKA,QAAMC,eAAe,GAAG,MAAM;AAC5BxM,IAAAA,WAAW,CAAC,KAAD,CAAX;AACAO,IAAAA,QAAQ,CAACwB,OAAT,GAAmB4J,WAAW,CAAC,MAAM;AACnCzL,MAAAA,gBAAgB,CAAE0L,QAAD,IAAcA,QAAQ,GAAG,CAA1B,CAAhB;AACD,KAF6B,EAE3B,IAF2B,CAA9B,CAF4B,CAIlB;;AACVvL,IAAAA,gBAAgB,CAAC0B,OAAjB,CAAyB0K,MAAzB;AACD,GAND;;AAOA,QAAMC,aAAa,GAAG,YAAY;AAChC,QAAInN,QAAQ,IAAIJ,IAAhB,EAAsB;AACpB,YAAMwN,OAAO,GAAG;AACdxN,QAAAA,IAAI,EAAEA,IADQ;AAEdI,QAAAA,QAAQ,EAAEA;AAFI,OAAhB;AAIAqN,MAAAA,WAAW,CAACD,OAAD,EAAUxM,QAAV,CAAX;AACA;AACD;;AACD,QAAI0M,QAAJ,EAAcC,MAAd,EAAsBC,QAAtB;;AAEA1M,IAAAA,gBAAgB,CAAC0B,OAAjB,CAAyB0J,MAAzB,GAAkC,YAAY;AAC5C,YAAMW,SAAS,GAAG,IAAIC,IAAJ,CAAS/L,cAAc,CAACyB,OAAxB,EAAiC;AACjDqC,QAAAA,IAAI,EAAE;AAD2C,OAAjC,CAAlB;AAGA9D,MAAAA,cAAc,CAACyB,OAAf,GAAyB,EAAzB;AACAgL,MAAAA,QAAQ,GAAGlG,GAAG,CAACC,eAAJ,CAAoBsF,SAApB,CAAX;AACAU,MAAAA,MAAM,GAAGV,SAAS,CAAChI,IAAnB;AACAyI,MAAAA,QAAQ,GAAGT,SAAX;AAEA,YAAMO,OAAO,GAAG;AACdxN,QAAAA,IAAI,EAAE0N,QADQ;AAEdtN,QAAAA,QAAQ,EAAEuN;AAFI,OAAhB;AAIAF,MAAAA,WAAW,CAACD,OAAD,EAAUI,QAAV,CAAX;AACD,KAdD;;AAgBA/M,IAAAA,WAAW,CAAC,KAAD,CAAX;AACA8B,IAAAA,aAAa,CAACvB,QAAQ,CAACwB,OAAV,CAAb;AACA1B,IAAAA,gBAAgB,CAAC0B,OAAjB,CAAyBgK,IAAzB;AACD,GA9BD;;AA+BA,QAAMa,WAAW,GAAG,OAAOD,OAAP,EAAgBI,QAAhB,KAA6B;AAC/C,UAAM3K,GAAG,GAAGvE,MAAM,EAAlB;AACA,UAAM4G,IAAI,GAAG,IAAIC,IAAJ,EAAb;AACA,UAAMsI,OAAO,GAAG;AACdrI,MAAAA,QAAQ,EAAEvC,GADI;AAEdK,MAAAA,QAAQ,EAAE7B,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAAC+B,OAFlD;AAGdC,MAAAA,UAAU,EACRhC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,OAApC,GAA8C9B,WAAW,CAACiC,IAJ9C;AAKdC,MAAAA,UAAU,EACRlC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAACmC,OAN1C;AAOdC,MAAAA,YAAY,EACVpC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GACI,OADJ,GAEI9B,WAAW,CAACoC,YAVJ;AAWdC,MAAAA,OAAO,EAAErC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAACsC,IAXjD;AAYdC,MAAAA,SAAS,EACPvC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,OAApC,GAA8C9B,WAAW,CAACuC,SAb9C;AAcdyB,MAAAA,QAAQ,EAAE,aAdI;AAedrB,MAAAA,MAAM,EAAE,aAfM;AAgBdsB,MAAAA,QAAQ,EAAEkI,QAhBI;AAiBdhO,MAAAA,OAAO,EAAE,EAjBK;AAkBd0E,MAAAA,YAAY,EAAE,OAlBA;AAmBdC,MAAAA,YAAY,EAAE9C,WAAW,CAAC8C,YAnBZ;AAoBdC,MAAAA,MAAM,EAAE3F,KAAK,CAAC4F,cApBA;AAqBdC,MAAAA,OAAO,EAAE,EArBK;AAsBdoB,MAAAA,MAAM,EAAE,SAtBM;AAuBdC,MAAAA,OAAO,EAAET,IAvBK;AAwBdK,MAAAA,eAAe,EAAE;AAxBH,KAAhB;AA0BA,QAAId,OAAO,GAAG,CAAC,GAAGhG,KAAK,CAACiG,QAAN,CAAeC,UAAnB,EAA+B8I,OAA/B,CAAd;AAEAnM,IAAAA,QAAQ,CAAC;AACPuD,MAAAA,IAAI,EAAE,aADC;AAEPC,MAAAA,OAAO,EAAE;AACPV,QAAAA,MAAM,EAAE3F,KAAK,CAAC4F,cADP;AAEPU,QAAAA,YAAY,EAAEN,OAFP;AAGPnB,QAAAA,IAAI,EAAE7E,KAAK,CAACiG,QAAN,CAAeM;AAHd;AAFF,KAAD,CAAR;AAQAtC,IAAAA,UAAU;;AACV,QAAI;AACF,YAAMgL,OAAO,GAAG,MAAM7F,eAAe,CAACuF,OAAD,CAArC;AAEA,YAAM;AAAEnK,QAAAA;AAAF,UAAW,MAAMtF,WAAW,CAAC;AACjCyH,QAAAA,QAAQ,EAAEvC,GADuB;AAEjCK,QAAAA,QAAQ,EACN7B,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAAC+B,OAHvB;AAIjCC,QAAAA,UAAU,EACRhC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,OAApC,GAA8C9B,WAAW,CAACiC,IAL3B;AAMjCC,QAAAA,UAAU,EACRlC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAACmC,OAPvB;AAQjCC,QAAAA,YAAY,EACVpC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GACI,OADJ,GAEI9B,WAAW,CAACoC,YAXe;AAYjCC,QAAAA,OAAO,EAAErC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,GAApC,GAA0C9B,WAAW,CAACsC,IAZ9B;AAajCC,QAAAA,SAAS,EACPvC,WAAW,CAAC8B,SAAZ,KAA0B,OAA1B,GAAoC,OAApC,GAA8C9B,WAAW,CAACuC,SAd3B;AAejCC,QAAAA,KAAK,EAAExC,WAAW,CAACyC,YAfc;AAgBjCV,QAAAA,OAAO,EAAE/B,WAAW,CAAC0C,SAhBY;AAiBjCC,QAAAA,MAAM,EAAE,aAjByB;AAkBjCC,QAAAA,cAAc,EAAEyJ,OAlBiB;AAmBjClO,QAAAA,OAAO,EAAE,EAnBwB;AAoBjC0E,QAAAA,YAAY,EAAE,OApBmB;AAqBjCC,QAAAA,YAAY,EAAE9C,WAAW,CAAC8C,YArBO;AAsBjCC,QAAAA,MAAM,EAAE3F,KAAK,CAAC4F,cAtBmB;AAuBjCC,QAAAA,OAAO,EAAE;AAvBwB,OAAD,CAAlC;;AA0BA,UAAIrB,IAAI,CAACuB,OAAL,KAAiB,IAArB,EAA2B;AACzB,cAAM+B,WAAW,GAAG9B,OAAO,CAAC0B,GAAR,CAAaK,QAAD,IAAc;AAC5C,cAAIA,QAAQ,CAACpB,QAAT,KAAsBnC,IAAI,CAACmC,QAA/B,EAAyC;AACvC,mBAAO,EAAE,GAAGoB,QAAL;AAAed,cAAAA,MAAM,EAAE,WAAvB;AAAoCJ,cAAAA,QAAQ,EAAEoI;AAA9C,aAAP;AACD;;AACD,iBAAOlH,QAAP;AACD,SALmB,CAApB;AAMAlF,QAAAA,QAAQ,CAAC;AACPuD,UAAAA,IAAI,EAAE,aADC;AAEPC,UAAAA,OAAO,EAAE;AACPV,YAAAA,MAAM,EAAE3F,KAAK,CAAC4F,cADP;AAEPU,YAAAA,YAAY,EAAEwB,WAFP;AAGPjD,YAAAA,IAAI,EAAE7E,KAAK,CAACiG,QAAN,CAAeM;AAHd;AAFF,SAAD,CAAR;AAQD,OAfD,MAeO;AACLtC,QAAAA,UAAU;AACVtE,QAAAA,KAAK,CAAC6C,KAAN,CAAYgC,IAAI,CAACgC,OAAjB;AACD;AACF,KAhDD,CAgDE,OAAOhE,KAAP,EAAc;AACd7C,MAAAA,KAAK,CAAC6C,KAAN,CAAYA,KAAK,CAACgE,OAAlB;AACAsD,MAAAA,OAAO,CAACC,GAAR,CAAY,wBAAZ,EAAsCvH,KAAtC;AACD;AACF,GA5FD;;AA6FA,QAAM0M,UAAU,GAAIC,SAAD,IAAe;AAChC,WAAOA,SAAS,CAACC,OAAV,CAAkB,2BAAlB,EAA+C,CAACC,KAAD,EAAQC,EAAR,EAAYC,EAAZ,KAAmB;AACvE,UAAID,EAAE,KAAK,GAAX,EAAgB;AACd,eAAQ,MAAKC,EAAG,MAAhB;AACD,OAFD,MAEO,IAAID,EAAE,KAAK,GAAX,EAAgB;AACrB,eAAQ,MAAKC,EAAG,MAAhB;AACD,OAFM,MAEA,IAAID,EAAE,KAAK,GAAX,EAAgB;AACrB,eAAQ,MAAKC,EAAG,MAAhB;AACD;;AACD,aAAOF,KAAP,CARuE,CAQzD;AACf,KATM,CAAP;AAUD,GAXD;;AAcA,sBACE;AAAK,IAAA,KAAK,EAAE;AAAEG,MAAAA,KAAK,EAAE;AAAT,KAAZ;AAAA,4BACE;AAAK,MAAA,SAAS,EAAC,gBAAf;AAAgC,MAAA,GAAG,EAAE9O,SAArC;AAAA,6BACE,QAAC,WAAD;AACE,QAAA,YAAY,EAAEsL,WADhB;AAEE,QAAA,IAAI,EAAEvK,WAFR;AAGE,QAAA,MAAM,EAAE,GAHV;AAIE,QAAA,KAAK,EAAE,MAJT;AAKE,QAAA,eAAe,EAAE;AALnB;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,YADF,eAWE;AAAA,iBACGqB,YAAY,iBACX,QAAC,WAAD;AAAa,QAAA,QAAQ,EAAEE,aAAvB;AAAsC,QAAA,OAAO,EAAEyH;AAA/C;AAAA;AAAA;AAAA;AAAA,cAFJ,EAIG9J,QAAQ,iBACP;AAAK,QAAA,SAAS,EAAC,OAAf;AAAA,+BACE;AAAK,UAAA,SAAS,EAAC,YAAf;AAAA,kCACE;AAAQ,YAAA,SAAS,EAAC,cAAlB;AAAiC,YAAA,OAAO,EAAEqD,WAA1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBADF,EAIGrD,QAAQ,iBACP;AAAA,oCACE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBADF,eAEE;AAAK,cAAA,SAAS,EAAC,cAAf;AAAA,wBACGQ,IAAI,CAACiF,IAAL,CAAUgC,UAAV,CAAqB,QAArB,iBACC;AAAO,gBAAA,QAAQ,MAAf;AAAgB,gBAAA,KAAK,EAAC,KAAtB;AAAA,wCACE;AAAQ,kBAAA,GAAG,EAAEnH,UAAb;AAAyB,kBAAA,IAAI,EAAEE,IAAI,CAACiF;AAApC;AAAA;AAAA;AAAA;AAAA,wBADF;AAAA;AAAA;AAAA;AAAA;AAAA,sBADD,GAKGjF,IAAI,CAACiF,IAAL,KAAc,iBAAd,gBACF;AACE,gBAAA,KAAK,EAAC,KADR;AAEE,gBAAA,GAAG,EAAEnF,UAFP;AAGE,gBAAA,KAAK,EAAE;AAAEuO,kBAAAA,KAAK,EAAE,GAAT;AAAc7E,kBAAAA,MAAM,EAAE;AAAtB;AAHT;AAAA;AAAA;AAAA;AAAA,sBADE,gBAOF;AACE,gBAAA,GAAG,EAAE1J,UADP;AAEE,gBAAA,GAAG,EAAC,SAFN;AAGE,gBAAA,KAAK,EAAE;AAAEwO,kBAAAA,QAAQ,EAAE,KAAZ;AAAmBC,kBAAAA,SAAS,EAAE;AAA9B;AAHT;AAAA;AAAA;AAAA;AAAA;AAbJ;AAAA;AAAA;AAAA;AAAA,oBAFF,EAsBGrO,QAAQ,GAAG,IAAX,iBACC;AAAA,qCACE;AAAO,gBAAA,SAAS,EAAC,aAAjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,oBAvBJ,eA6BE;AAAK,cAAA,SAAS,EAAC,+BAAf;AAAA,sCACE;AACE,gBAAA,IAAI,EAAC,MADP;AAEE,gBAAA,KAAK,EAAEN,OAFT;AAGE,gBAAA,SAAS,EAAC,mBAHZ;AAIE,gBAAA,WAAW,EAAC,0BAJd;AAKE,gBAAA,QAAQ,EAAGkH,CAAD,IAAOjH,UAAU,CAACiH,CAAC,CAACC,MAAF,CAASmD,KAAV;AAL7B;AAAA;AAAA;AAAA;AAAA,sBADF,eASE;AAAK,gBAAA,SAAS,EAAC,WAAf;AAAA,0BACGxK,YAAY,KAAK,KAAjB,gBACC;AACE,kBAAA,OAAO,EAAEQ,QAAQ,GAAG,IAAX,GAAkB,IAAlB,GAAyB8C,UADpC;AAEE,kBAAA,SAAS,EAAC,2DAFZ;AAGE,oCAAe,UAHjB;AAIE,oCAAe,4BAJjB;AAAA,yCAME;AACE,oBAAA,SAAS,EAAC,0BADZ;AAEE,oBAAA,EAAE,EAAC;AAFL;AAAA;AAAA;AAAA;AAAA;AANF;AAAA;AAAA;AAAA;AAAA,wBADD,gBAaC;AACE,kBAAA,SAAS,EAAC,iBADZ;AAEE,kBAAA,IAAI,EAAC,QAFP;AAGE,kBAAA,QAAQ,MAHV;AAAA,0CAKE;AACE,oBAAA,SAAS,EAAC,kCADZ;AAEE,oBAAA,IAAI,EAAC,QAFP;AAGE,mCAAY;AAHd;AAAA;AAAA;AAAA;AAAA,0BALF,eAUE;AAAM,oBAAA,SAAS,EAAC,SAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BAVF;AAAA;AAAA;AAAA;AAAA;AAAA;AAdJ;AAAA;AAAA;AAAA;AAAA,sBATF;AAAA;AAAA;AAAA;AAAA;AAAA,oBA7BF;AAAA;AAAA;AAAA;AAAA;AAAA,kBALJ;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,cALJ;AAAA;AAAA;AAAA;AAAA;AAAA,YAXF,eAgGE;AAAK,MAAA,SAAS,EAAC,+BAAf;AAA+C,MAAA,EAAE,EAAC,UAAlD;AAAA,gBACGjB,QAAQ,gBACP;AAAK,QAAA,SAAS,EAAC,kDAAf;AAAA,gCACE;AAAI,UAAA,KAAK,EAAE;AAAEyM,YAAAA,KAAK,EAAE;AAAT,WAAX;AAA6B,UAAA,SAAS,EAAC,MAAvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBADF,eAIE;AACE,UAAA,IAAI,EAAC,QADP;AAEE,UAAA,SAAS,EAAC,iBAFZ;AAGE,UAAA,OAAO,EAAE,MAAMtM,oBAAoB,CAAC,IAAD,CAHrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAJF;AAAA;AAAA;AAAA;AAAA;AAAA,cADO,gBAcP;AAAA,mBACGb,KAAK,CAACA,KAAN,iBACC;AAAK,UAAA,SAAS,EAAG,wBAAuBA,KAAK,CAACG,SAAU,EAAxD;AAAA,oBACGH,KAAK,CAACE;AADT;AAAA;AAAA;AAAA;AAAA,gBAFJ,eAOE;AAAK,UAAA,SAAS,EAAC,4BAAf;AAAA,kCACE;AAAK,YAAA,SAAS,EAAC;AAAf;AAAA;AAAA;AAAA;AAAA,kBADF,EAGGb,WAAW,KAAK,KAAhB,gBACC;AAAA,oCACE;AAAK,cAAA,SAAS,EAAC,UAAf;AAAA,qCACE;AAAK,gBAAA,SAAS,EAAC,0BAAf;AAAA,wCACE;AACE,kBAAA,SAAS,EAAC,iBADZ;AAEE,oCAAe,SAFjB;AAGE,qCAAgB,OAHlB;AAIE,uCAAkB,KAJpB;AAKE,kBAAA,KAAK,EAAC,MALR;AAAA,yCAOE;AACE,oBAAA,IAAI,EAAC,QADP;AAEE,oBAAA,SAAS,EAAC,mEAFZ;AAGE,sCAAe,UAHjB;AAIE,sCAAe,wBAJjB;AAKE,qCAAc,OALhB;AAME,qCAAc,uBANhB;AAOE,oBAAA,OAAO,EAAE,MAAMH,cAAc,CAAC,KAAD,CAP/B;AAAA,2CASE,QAAC,MAAD;AAAQ,sBAAA,SAAS,EAAC;AAAlB;AAAA;AAAA;AAAA;AAAA;AATF;AAAA;AAAA;AAAA;AAAA;AAPF;AAAA;AAAA;AAAA;AAAA,wBADF,eAoBE;AACE,kBAAA,SAAS,EAAC,iBADZ;AAEE,oCAAe,SAFjB;AAGE,qCAAgB,OAHlB;AAIE,uCAAkB,KAJpB;AAKE,kBAAA,KAAK,EAAC,OALR;AAAA,yCAOE;AACE,oBAAA,IAAI,EAAC,QADP;AAEE,oBAAA,SAAS,EAAC,6EAFZ;AAGE,oBAAA,EAAE,EAAC,WAHL;AAIE,oBAAA,OAAO,EAAEiK,WAJX;AAAA,8BAMGlK,WAAW,KAAK,KAAhB,gBACC;AAAG,sBAAA,SAAS,EAAC;AAAb;AAAA;AAAA;AAAA;AAAA,4BADD,gBAGC;AAAG,sBAAA,SAAS,EAAC;AAAb;AAAA;AAAA;AAAA;AAAA;AATJ;AAAA;AAAA;AAAA;AAAA;AAPF;AAAA;AAAA;AAAA;AAAA,wBApBF;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,oBADF,eA6CE;AAAK,cAAA,SAAS,EAAC,KAAf;AAAA,qCACE;AAAK,gBAAA,SAAS,EAAC,mBAAf;AAAA,wCACE;AAAK,kBAAA,SAAS,EAAC,qBAAf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBADF,EAgBGnB,WAAW,iBAAI;AAAK,kBAAA,SAAS,EAAC,SAAf;AACd,kBAAA,uBAAuB,EAAE;AAAEsP,oBAAAA,MAAM,EAAEV,UAAU,CAAC3L,IAAD;AAApB;AADX;AAAA;AAAA;AAAA;AAAA,wBAhBlB,eAkBE;AACE,kBAAA,GAAG,EAAElD,WADP;AAEE,kBAAA,QAAQ,EAAE+K,cAFZ;AAGE,kBAAA,SAAS,EAAGnD,CAAD,IAAO+B,cAAc,CAAC/B,CAAD,CAHlC;AAIE,kBAAA,SAAS,EAAC,8CAJZ;AAKE,kBAAA,IAAI,EAAC,GALP;AAME,kBAAA,KAAK,EAAE1E,IANT;AAOE,kBAAA,EAAE,EAAC,YAPL;AAQE,kBAAA,WAAW,EAAC;AARd;AAAA;AAAA;AAAA;AAAA,wBAlBF;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,oBA7CF;AAAA,0BADD,gBA+EC;AAAK,YAAA,SAAS,EAAC,oBAAf;AAAA,oCACE;AAAK,cAAA,SAAS,EAAC,UAAf;AAAA,qCACE;AAAG,gBAAA,SAAS,EAAC;AAAb;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,oBADF,EAIG,CAACpB,QAAD,gBACC;AAAK,cAAA,SAAS,EAAC,YAAf;AAAA,sCACE;AAAK,gBAAA,SAAS,EAAC,gBAAf;AAAA,2BACG,GADH,eAEE,QAAC,eAAD;AAAiB,kBAAA,SAAS,EAAC,MAA3B;AAAkC,kBAAA,OAAO,EAAEgM;AAA3C;AAAA;AAAA;AAAA;AAAA,wBAFF,EAGGtM,WAAW,IAAI,CAACE,QAAhB,iBACC,QAAC,OAAD;AAAS,kBAAA,OAAO,EAAEuM;AAAlB;AAAA;AAAA;AAAA;AAAA,wBAJJ,EAMGzM,WAAW,IAAIE,QAAf,iBACC,QAAC,MAAD;AAAQ,kBAAA,OAAO,EAAEyM;AAAjB;AAAA;AAAA;AAAA;AAAA,wBAPJ;AAAA;AAAA;AAAA;AAAA;AAAA,sBADF,eAWE;AAAK,gBAAA,SAAS,EAAC,gBAAf;AAAA,wCACE;AAAK,kBAAA,GAAG,EAAC,mBAAT;AAA6B,kBAAA,GAAG,EAAC;AAAjC;AAAA;AAAA;AAAA;AAAA,wBADF,eAEE;AACE,kBAAA,GAAG,EAAC,mBADN;AAEE,kBAAA,SAAS,EAAC,MAFZ;AAGE,kBAAA,GAAG,EAAC;AAHN;AAAA;AAAA;AAAA;AAAA,wBAFF;AAAA;AAAA;AAAA;AAAA;AAAA,sBAXF,eAmBE;AAAK,gBAAA,SAAS,EAAC,cAAf;AAAA,2BACG,GADH,EAEG/B,UAAU,CAACxK,aAAD,CAFb;AAAA;AAAA;AAAA;AAAA;AAAA,sBAnBF;AAAA;AAAA;AAAA;AAAA;AAAA,oBADD,gBA0BC;AAAO,cAAA,QAAQ,MAAf;AAAgB,cAAA,GAAG,EAAEE;AAArB;AAAA;AAAA;AAAA;AAAA,oBA9BJ,eAgCE;AAAK,cAAA,SAAS,EAAC,aAAf;AAAA,wBACGN,WAAW,iBAAI,QAAC,QAAD;AAAU,gBAAA,OAAO,EAAEgM;AAAnB;AAAA;AAAA;AAAA;AAAA;AADlB;AAAA;AAAA;AAAA;AAAA,oBAhCF;AAAA;AAAA;AAAA;AAAA;AAAA,kBAlFJ,eAuHE;AAAK,YAAA,SAAS,EAAC,UAAf;AAAA,mCACE;AAAK,cAAA,SAAS,EAAC,gCAAf;AAAA,qCACE;AAAK,gBAAA,SAAS,EAAC,iBAAf;AAAA,0BACGlM,UAAU,gBACT;AACE,kBAAA,OAAO,EAAEE,WAAW,GAAG6M,aAAH,GAAmBvK,UADzC;AAEE,kBAAA,SAAS,EAAC,2DAFZ;AAGE,oCAAe,UAHjB;AAIE,oCAAe,4BAJjB;AAKE,kBAAA,KAAK,EAAC,cALR;AAAA,yCAOE;AACE,oBAAA,SAAS,EAAC,0BADZ;AAEE,oBAAA,EAAE,EAAC;AAFL;AAAA;AAAA;AAAA;AAAA;AAPF;AAAA;AAAA;AAAA;AAAA,wBADS,gBAcT;AACE,kBAAA,OAAO,EAAE6I,cADX;AAEE,kBAAA,SAAS,EAAC,2DAFZ;AAGE,oCAAe,UAHjB;AAIE,oCAAe,4BAJjB;AAKE,kBAAA,KAAK,EAAC,cALR;AAAA,yCAOE;AAAG,oBAAA,SAAS,EAAC;AAAb;AAAA;AAAA;AAAA;AAAA;AAPF;AAAA;AAAA;AAAA;AAAA;AAfJ;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,kBAvHF;AAAA;AAAA;AAAA;AAAA;AAAA,gBAPF,eA+JE;AACE,UAAA,SAAS,EAAG,mDADd;AAEE,UAAA,EAAE,EAAC,uBAFL;AAAA,iCAIE;AAAK,YAAA,SAAS,EAAC,WAAf;AAAA,mCACE;AAAK,cAAA,SAAS,EAAC,qBAAf;AAAA,qCACE;AAAK,gBAAA,SAAS,EAAC,wBAAf;AAAA,uCACE;AAAK,kBAAA,SAAS,EAAC,mCAAf;AAAA,0CACE;AAAK,oBAAA,SAAS,EAAC,oCAAf;AAAA,2CACE;AAAK,sBAAA,SAAS,EAAC,sCAAf;AAAA,8CACE;AACE,wBAAA,IAAI,EAAC,MADP;AAEE,wBAAA,KAAK,EAAE;AAAE6C,0BAAAA,OAAO,EAAE;AAAX,yBAFT;AAGE,wBAAA,EAAE,EAAC,eAHL;AAIE,wBAAA,QAAQ,EAAE7H;AAJZ;AAAA;AAAA;AAAA;AAAA,8BADF,eAOE;AAAO,wBAAA,OAAO,EAAC,eAAf;AAA+B,wBAAA,SAAS,EAAC,aAAzC;AAAA,gDAAuD;AAAG,0BAAA,SAAS,EAAC;AAAb;AAAA;AAAA;AAAA;AAAA,gCAAvD,eACE;AAAK,0BAAA,SAAS,EAAC,2CAAf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCADF;AAAA;AAAA;AAAA;AAAA;AAAA,8BAPF;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,0BADF,eA4BE;AAAK,oBAAA,SAAS,EAAC,oCAAf;AAAA,2CACE;AAAK,sBAAA,SAAS,EAAC,sCAAf;AAAA,8CACE;AACE,wBAAA,EAAE,EAAC,mBADL;AAEE,wBAAA,IAAI,EAAC,MAFP;AAGE,wBAAA,SAAS,EAAC,QAHZ;AAIE,wBAAA,QAAQ,EAAEA;AAJZ;AAAA;AAAA;AAAA;AAAA,8BADF,eAOE;AAAO,wBAAA,OAAO,EAAC,mBAAf;AAAmC,wBAAA,SAAS,EAAC,aAA7C;AAAA,gDACE;AAAG,0BAAA,SAAS,EAAC;AAAb;AAAA;AAAA;AAAA;AAAA,gCADF,eAEE;AAAK,0BAAA,SAAS,EAAC,2CAAf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCAFF;AAAA;AAAA;AAAA;AAAA;AAAA,8BAPF;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,0BA5BF,eA2CE;AAAK,oBAAA,SAAS,EAAC,oCAAf;AAAA,2CACE;AAAK,sBAAA,SAAS,EAAC,sCAAf;AAAA,8CACE;AACE,wBAAA,EAAE,EAAC,iBADL;AAEE,wBAAA,IAAI,EAAC,MAFP;AAGE,wBAAA,SAAS,EAAC,QAHZ;AAIE,wBAAA,QAAQ,EAAEA;AAJZ;AAAA;AAAA;AAAA;AAAA,8BADF,eAOE;AAAO,wBAAA,OAAO,EAAC,iBAAf;AAAiC,wBAAA,SAAS,EAAC,aAA3C;AAAA,gDACE;AAAG,0BAAA,SAAS,EAAC;AAAb;AAAA;AAAA;AAAA;AAAA,gCADF,eAEE;AAAK,0BAAA,SAAS,EAAC,2CAAf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCAFF;AAAA;AAAA;AAAA;AAAA;AAAA,8BAPF;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,0BA3CF;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA;AAJF;AAAA;AAAA;AAAA;AAAA,gBA/JF;AAAA;AAAA;AAAA;AAAA;AAAA;AAfJ;AAAA;AAAA;AAAA;AAAA,YAhGF,EA8ZG5E,iBAAiB,iBAChB;AAAK,MAAA,SAAS,EAAC,aAAf;AAAA,6BACE;AAAK,QAAA,SAAS,EAAC,4BAAf;AAAA,gCACE;AAAK,UAAA,KAAK,EAAE;AAAE0M,YAAAA,KAAK,EAAE,OAAT;AAAkBC,YAAAA,MAAM,EAAE;AAA1B,WAAZ;AAAA,iCACE;AACE,YAAA,SAAS,EAAC,qBADZ;AAEE,YAAA,KAAK,EAAE;AAAEC,cAAAA,QAAQ,EAAE;AAAZ,aAFT;AAGE,YAAA,OAAO,EAAE,MAAM3M,oBAAoB,CAAC,KAAD;AAHrC;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,gBADF,eAQE;AAAA,iCACE,QAAC,YAAD;AAAc,YAAA,MAAM,EAAEC;AAAtB;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,gBARF;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,YA/ZJ;AAAA;AAAA;AAAA;AAAA;AAAA,UADF;AAibD,CAlpCD;;GAAMvD,K;;KAAAA,K;AAqpCN,eAAeA,KAAf", "sourcesContent": ["import React, { useContext, useEffect, useRef, useState } from \"react\";\r\nimport EmojiPicker from \"emoji-picker-react\";\r\nimport { AuthContext } from \"../context/AuthContext\";\r\nimport { BASE_URL, sendMessage } from \"../api/api\";\r\nimport { ChatContext } from \"../context/ChatContext\";\r\nimport { ChatState } from \"../context/AllProviders\";\r\nimport SendTemplate from \"./SendTemplate\";\r\nimport { useDebounce } from \"../customHooks/useDebounce\";\r\nimport { MdDelete } from \"react-icons/md\";\r\nimport { FaPlay, FaPause, FaRegStopCircle } from \"react-icons/fa\";\r\nimport { toast } from \"react-toastify\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\nimport { FaPlus } from \"react-icons/fa6\";\r\n\r\nconst Input = (props) => {\r\n  const { saveSentences, filteredSentences, splitParagraphIntoSentences,\r\n    handleSuggestionClick, textareaRef, showPreview, setShowPreview,\r\n    handleTranslationKeyDown, handleTranslationInputChange } = props;\r\n  const pickerRef = useRef(null);\r\n  // const [text, setText] = useState(\"\");\r\n  const [showPrev, setShowPrev] = useState(false);\r\n  const [buttonLoader, setButtonLoader] = useState(false);\r\n  const [caption, setCaption] = useState(\"\");\r\n  const [previewUrl, setPreviewUrl] = useState();\r\n  const [file, setFile] = useState(null);\r\n  const [fileSize, setFileSize] = useState();\r\n  const [fileType, setFileType] = useState();\r\n  const [emojiStatus, setEmojiStatus] = useState(false);\r\n  const [sendButton, setSendButton] = useState(false);\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [isPaused, setIsPaused] = useState(false);\r\n  const [recordingTime, setRecordingTime] = useState(0);\r\n  const [audioURL, setAudioURL] = useState(null);\r\n  const mediaRecorderRef = useRef(null);\r\n  const audioChunksRef = useRef([]);\r\n  const timerRef = useRef(null);\r\n  const [error, setError] = useState({\r\n    error: false,\r\n    errorMessage: \"\",\r\n    errorType: \"\",\r\n  });\r\n  const { currentUser } = useContext(AuthContext);\r\n  const { dispatch } = useContext(ChatContext);\r\n\r\n  const {\r\n    isViewerOpen,\r\n    setIsViewerOpen,\r\n    selectedImage,\r\n    setSelectedImage,\r\n    isOldMsg,\r\n    setIsOldMsg,\r\n    sendTemplatePopUp,\r\n    setSendTemplatePopUp,\r\n    selectedMobileNumber,\r\n    text, setText,\r\n    socket, setAllChats,\r\n    reply, setReply\r\n  } = ChatState();\r\n  useEffect(() => {\r\n    setTimeout(() => {\r\n      setError({\r\n        error: false,\r\n        errorMessage: \"\",\r\n        errorType: \"\",\r\n      });\r\n    }, 5000);\r\n  }, [error]);\r\n\r\n\r\n  useEffect(() => {\r\n    return () => clearInterval(timerRef.current);\r\n  }, []);\r\n  const togglePopup = () => {\r\n    setShowPrev(!showPrev);\r\n    setFileSize();\r\n    setPreviewUrl();\r\n    setFile(null);\r\n    setPreviewUrl(\"\");\r\n    setFileType();\r\n  };\r\n  const resetState = () => {\r\n    setAudioURL(null);\r\n    setSendButton(false);\r\n    setIsRecording(false);\r\n    setPreviewUrl(\"\");\r\n    setFileSize(undefined);\r\n    setFileType(undefined);\r\n    setShowPrev(false);\r\n    setFile(null);\r\n    setButtonLoader(false);\r\n    setText(\"\");\r\n    setCaption(\"\");\r\n  };\r\n  const handleSend = async () => {\r\n    setEmojiStatus(false);\r\n    const uid = uuidv4();\r\n\r\n    if (file) {\r\n      setButtonLoader(true);\r\n      try {\r\n\r\n\r\n        let url = await uploadWhatsAppMedia();\r\n\r\n        const msg = {};\r\n        const { data } = await sendMessage({\r\n          agent_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.user_id,\r\n          agent_name:\r\n            currentUser.user_type === \"admin\" ? \"admin\" : currentUser.name,\r\n          manager_id:\r\n            currentUser.user_type === \"admin\" ? \"1\" : currentUser.manager,\r\n          manager_name:\r\n            currentUser.user_type === \"admin\"\r\n              ? \"admin\"\r\n              : currentUser.manager_name,\r\n          team_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.team,\r\n          team_name:\r\n            currentUser.user_type === \"admin\" ? \"admin\" : currentUser.team_name,\r\n          token: currentUser.parent_token,\r\n          user_id: currentUser.parent_id,\r\n          method: \"media_reply\",\r\n          attachment_url: url,\r\n          caption: caption,\r\n          message_type: fileType,\r\n          brand_number: currentUser.brand_number,\r\n          mobile: props.selectedMobile,\r\n          content: text,\r\n          request_type: \"\",\r\n        });\r\n\r\n        if (data.success === true) {\r\n          let newdata = [...props.convData.conversion, data.response];\r\n\r\n          dispatch({\r\n            type: \"CHANGE_USER\",\r\n            payload: {\r\n              mobile: props.selectedMobile,\r\n              conversation: newdata,\r\n              name: props.convData.selectedName,\r\n            },\r\n          });\r\n          resetState();\r\n        } else {\r\n          resetState();\r\n          toast.error(data.message)\r\n          setError({\r\n            error: true,\r\n            errorMessage: data.message,\r\n            errorType: \"alert-danger\",\r\n          });\r\n          setButtonLoader(false);\r\n        }\r\n      } catch (error) {\r\n        toast.error(error.message)\r\n        setButtonLoader(false);\r\n        resetState();\r\n      }\r\n    } else {\r\n\r\n\r\n      const date = new Date();\r\n      const msg = {\r\n        agent_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.user_id,\r\n        agent_name:\r\n          currentUser.user_type === \"admin\" ? \"admin\" : currentUser.name,\r\n        manager_id:\r\n          currentUser.user_type === \"admin\" ? \"1\" : currentUser.manager,\r\n        manager_name:\r\n          currentUser.user_type === \"admin\"\r\n            ? \"admin\"\r\n            : currentUser.manager_name,\r\n        team_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.team,\r\n        team_name:\r\n          currentUser.user_type === \"admin\" ? \"admin\" : currentUser.team_name,\r\n        track_id: uid,\r\n        mobile: props.selectedMobile,\r\n        brand_number: currentUser.brand_number,\r\n        message_type: \"TEXT\",\r\n        req_from: \"AGENT_REPLY\",\r\n        file_url: \"\",\r\n        message_content: text,\r\n        image_caption: \"\",\r\n        resp_url: \"\",\r\n        request_type: \"\",\r\n        status: \"pending\",\r\n        created: date,\r\n        tag_id: reply\r\n          ? JSON.stringify({\r\n            id: reply.id,\r\n            message_id: reply.messageid,\r\n            message_type: \"TEXT\",\r\n            req_from: reply.req_from,\r\n            message_content: reply.message_content,\r\n            file_url: \"\"\r\n          })\r\n          : \"\"\r\n        ,\r\n      \r\n\r\n      };\r\n\r\n      let newdata = [...props.convData.conversion, msg];\r\n      setReply(null);\r\n\r\n      dispatch({\r\n        type: \"CHANGE_USER\",\r\n        payload: {\r\n          mobile: props.selectedMobile,\r\n          conversation: newdata,\r\n          name: props.convData.selectedName,\r\n        },\r\n      });\r\n\r\n      let data = {\r\n        agent_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.user_id,\r\n        agent_name:\r\n          currentUser.user_type === \"admin\" ? \"admin\" : currentUser.name,\r\n        manager_id:\r\n          currentUser.user_type === \"admin\" ? \"1\" : currentUser.manager,\r\n        manager_name:\r\n          currentUser.user_type === \"admin\"\r\n            ? \"admin\"\r\n            : currentUser.manager_name,\r\n        team_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.team,\r\n        team_name:\r\n          currentUser.user_type === \"admin\" ? \"admin\" : currentUser.team_name,\r\n        track_id: uid,\r\n        token: currentUser.parent_token,\r\n        user_id: currentUser.parent_id,\r\n        method: \"reply\",\r\n        request_type: \"\",\r\n        brand_number: currentUser.brand_number,\r\n        mobile: props.selectedMobile,\r\n        content: text,\r\n        tag_id: reply\r\n        ? JSON.stringify({\r\n          id: reply.id,\r\n          message_id: reply.messageid,\r\n          message_type: \"TEXT\",\r\n          req_from: reply.req_from,\r\n          message_content: reply.message_content,\r\n          file_url: \"\"\r\n        })\r\n        : \"\"\r\n      ,\r\n      };\r\n      // if (text.trim()) {\r\n      //   const sentences = splitParagraphIntoSentences(text);\r\n      //   sentences.forEach((sentence) => {\r\n      //     saveSentences(sentence); // Save each sentence individually\r\n      //   });\r\n      // }\r\n\r\n      setAllChats((prevState) =>\r\n        prevState.map((item) => {\r\n          if (item.mobile === selectedMobileNumber) {\r\n            return { ...item, req_from: \"AGENT_REPLY\", content: text, created: date }\r\n          }\r\n          return item;\r\n        })\r\n      );\r\n      setText(\"\");\r\n\r\n      // setShowPreview(false);\r\n      sendMessage(data).then((res) => {\r\n        if (res.data.success === true) {\r\n          const updatedChat = newdata.map((chatdata) => {\r\n            if (chatdata.track_id === res.data.track_id) {\r\n              return { ...chatdata, status: \"Submitted\" };\r\n            }\r\n            return chatdata;\r\n          });\r\n          dispatch({\r\n            type: \"CHANGE_USER\",\r\n            payload: {\r\n              mobile: props.selectedMobile,\r\n              conversation: updatedChat,\r\n              name: props.convData.selectedName,\r\n            },\r\n          });\r\n          // setText(\"\");\r\n        } else {\r\n          if (\r\n            res.data.message ===\r\n            \"You need to send fresh reply coz chat 24 hour old\"\r\n          ) {\r\n            setIsOldMsg(true);\r\n          }\r\n\r\n          setError({\r\n            error: true,\r\n            errorMessage: res.data.message,\r\n            errorType: \"alert-danger\",\r\n          });\r\n        }\r\n      });\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const fileHandler = (e) => {\r\n    setShowPrev(!showPrev);\r\n    const file = e.target.files[0];\r\n    if (e.target.files[0].type.startsWith(\"image\")) {\r\n      setFileType(\"image\");\r\n    } else if (e.target.files[0].type.startsWith(\"video\")) {\r\n      setFileType(\"video\");\r\n    } else {\r\n      setFileType(\"file\");\r\n    }\r\n\r\n    document.body.style.overflow = !showPrev ? \"hidden\" : \"auto\";\r\n\r\n    if (file) {\r\n      const fileSizeInBytes = file.size;\r\n      const fileSizeInMB = fileSizeInBytes / (1024 * 1024);\r\n      setFileSize(fileSizeInMB);\r\n      if (file && file.type.startsWith(\"video/\")) {\r\n        const objectURL = URL.createObjectURL(file);\r\n        setPreviewUrl(objectURL);\r\n        setFile(file);\r\n        return;\r\n      }\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => {\r\n        setPreviewUrl(reader.result);\r\n      };\r\n      reader.readAsDataURL(file);\r\n      setFile(file);\r\n    } else {\r\n      setFile(null);\r\n      setPreviewUrl(\"\");\r\n    }\r\n  };\r\n  const uploadRecording = async (rec) => {\r\n    try {\r\n      var data = await new Promise((resolve, reject) => {\r\n        const data = new FormData();\r\n        data.append(\"amdfile\", rec.file, \"recording.mp3\");\r\n        data.append(\"doc_name\", \"test MK\");\r\n        data.append(\"doc_type\", rec.fileType);\r\n        data.append(\"input_file_type\", \"convert_recording\");\r\n        data.append(\"user_id\", currentUser.parent_id);\r\n        data.append(\"token\", currentUser.parent_token);\r\n        data.append(\"method\", \"send_recording\");\r\n\r\n        fetch(`${BASE_URL}/uploadFileWhatsapp.php`, {\r\n          method: \"POST\",\r\n          body: data,\r\n        }).then((result) => {\r\n          result.json().then((resp) => {\r\n            resolve(resp.url);\r\n          });\r\n        });\r\n      });\r\n      // setFile(undefined);\r\n      return data;\r\n    } catch (error) {\r\n      console.log(\"error\");\r\n    }\r\n  };\r\n  const uploadWhatsAppMedia = async () => {\r\n    try {\r\n      var data = await new Promise((resolve, reject) => {\r\n        const data = new FormData();\r\n        data.append(\"amdfile\", file);\r\n        data.append(\"doc_name\", \"test MK\");\r\n        data.append(\"doc_type\", fileType);\r\n        data.append(\"user_id\", currentUser.parent_id);\r\n        data.append(\"token\", currentUser.parent_token);\r\n        data.append(\"method\", \"create\");\r\n\r\n        fetch(`${BASE_URL}/uploadFileWhatsapp.php`, {\r\n          method: \"POST\",\r\n          body: data,\r\n        }).then((result) => {\r\n          result.json().then((resp) => {\r\n            resolve(resp.url);\r\n          });\r\n        });\r\n      });\r\n      // setFile(undefined);\r\n      return data;\r\n    } catch (error) {\r\n      console.log(\"error\");\r\n    }\r\n  };\r\n\r\n  const handleKeyPress = (event) => {\r\n    if (event.key === \"Enter\" && event.shiftKey) {\r\n      // event.preventDefault();\r\n    } else if (event.key === \"Enter\") {\r\n      event.preventDefault();\r\n      handleSend();\r\n    }\r\n    if (event.key === \"Tab\" && filteredSentences.length > 0) {\r\n      event.preventDefault();\r\n      handleSuggestionClick(filteredSentences[0]);\r\n    }\r\n\r\n    // Handle translation key events if function is provided\r\n    if (handleTranslationKeyDown) {\r\n      handleTranslationKeyDown(event);\r\n    }\r\n  };\r\n\r\n  const ImageViewer = ({ imageUrl, onClose }) => {\r\n    return (\r\n      <div className=\"popup\">\r\n        <div className=\"popupInner\">\r\n          <img src={imageUrl} alt=\"Preview\" className=\"popup-img\" />\r\n          <button className=\"cancelButton\" onClick={onClose}>\r\n            x\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n  const closeImageViewer = () => {\r\n    setIsViewerOpen(false);\r\n    setSelectedImage(\"\");\r\n  };\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      if (textareaRef.current) {\r\n        textareaRef.current.style.height = \"auto\";\r\n        const newHeight = Math.min(textareaRef.current.scrollHeight, 100);\r\n        textareaRef.current.style.height = `${newHeight}px`;\r\n        textareaRef.current.style.overflowY =\r\n          textareaRef.current.scrollHeight > 100 ? \"auto\" : \"hidden\";\r\n      }\r\n    };\r\n\r\n    const textarea = textareaRef.current;\r\n    textarea?.addEventListener(\"input\", handleResize);\r\n\r\n    return () => {\r\n      textarea?.removeEventListener(\"input\", handleResize);\r\n    };\r\n  }, []);\r\n  const handleTextarea = (e) => {\r\n    setEmojiStatus(false);\r\n    setText(e.target.value);\r\n    // setShowPreview(/(\\*.*?\\*)|(_.*?_)|(~.*?~)/.test(e.target.value));\r\n    if (e.target.value === \"\") {\r\n      setSendButton(false);\r\n    } else {\r\n      setSendButton(true);\r\n    }\r\n\r\n    // Handle translation input change if function is provided\r\n    if (handleTranslationInputChange) {\r\n      handleTranslationInputChange(e.target.value);\r\n    }\r\n  };\r\n\r\n\r\n  useEffect(() => {\r\n    if (!socket) return;\r\n    if (text.length > 0) {\r\n      setSendButton(true)\r\n    } else {\r\n      setSendButton(false)\r\n    }\r\n    const timer1 = setTimeout(() => {\r\n      if (text.trim()) {\r\n        socket.emit(\"chat on\", currentUser);\r\n      }\r\n    }, 1000);\r\n\r\n    const timer = setTimeout(() => {\r\n      if (text.trim()) {\r\n        socket.emit(\"chat off\", currentUser);\r\n      }\r\n    }, 2 * 60 * 1000);\r\n\r\n    return () => {\r\n      clearTimeout(timer1);\r\n      clearTimeout(timer);\r\n\r\n    };\r\n  }, [text, socket]);\r\n\r\n  // useEffect(() => {\r\n  //   let socket = io(SOCKET_URL);\r\n  //   if (currentUser.parent_id) {\r\n  //     socket.emit(\"setup\", currentUser);\r\n  //   }\r\n  //   const timer1 = setTimeout(() => {\r\n  //     if (text.trim()) {\r\n  //       socket.emit(\"chat on\", currentUser);\r\n  //     }\r\n  //   }, 1000);\r\n  //   const timer = setTimeout(() => {\r\n  //     if (text.trim()) {\r\n  //       socket.emit(\"chat off\", currentUser);\r\n  //     }\r\n  //   }, 2 * 60 * 1000);\r\n\r\n  //   return () => {\r\n  //     clearTimeout(timer, timer1);\r\n  //     socket.disconnect();\r\n  //   };\r\n  // }, [text]);\r\n  // useEffect(() => {\r\n  //   const handleClickOutside = (event) => {\r\n  //     if (pickerRef.current && !pickerRef.current.contains(event.target)) {\r\n  //       setEmojiStatus(false);\r\n\r\n  //     }\r\n  //   };\r\n\r\n  //   document.addEventListener(\"mousedown\", handleClickOutside);\r\n\r\n  //   return () => {\r\n  //     document.removeEventListener(\"mousedown\", handleClickOutside);\r\n  //   };\r\n  // }, []);\r\n  const toggleEmoji = () => {\r\n    const element = document.getElementById(\"chatinputmorecollapse\");\r\n    if (element) {\r\n      element.classList.remove(\"show\");\r\n    }\r\n\r\n    setEmojiStatus(!emojiStatus);\r\n  };\r\n  const emojiselect = (emojidata) => {\r\n    const { current } = textareaRef;\r\n    const { selectionStart, selectionEnd } = current;\r\n    const newValue =\r\n      text.substring(0, selectionStart) +\r\n      emojidata.emoji +\r\n      text.substring(selectionEnd);\r\n    setText(newValue);\r\n    current.focus();\r\n    current.setSelectionRange(\r\n      selectionStart + emojidata.emoji.length,\r\n      selectionStart + emojidata.emoji.length\r\n    );\r\n  };\r\n\r\n  const formatTime = (totalSeconds) => {\r\n    const minutes = String(Math.floor(totalSeconds / 60)).padStart(2, \"0\");\r\n    const seconds = String(totalSeconds % 60).padStart(2, \"0\");\r\n    return `${minutes}:${seconds}`;\r\n  };\r\n  const startRecording = async () => {\r\n    setSendButton(true);\r\n    setIsRecording(true);\r\n    setIsPaused(false);\r\n    setRecordingTime(0);\r\n    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\r\n\r\n    mediaRecorderRef.current = new MediaRecorder(stream);\r\n    mediaRecorderRef.current.ondataavailable = (event) => {\r\n      audioChunksRef.current.push(event.data);\r\n    };\r\n\r\n    mediaRecorderRef.current.onstop = async () => {\r\n\r\n      audioChunksRef.current = []\r\n    };\r\n\r\n    mediaRecorderRef.current.start();\r\n\r\n    timerRef.current = setInterval(() => {\r\n      setRecordingTime((prevTime) => prevTime + 1);\r\n    }, 1000);\r\n  };\r\n  const stopRecording = async () => {\r\n    clearInterval(timerRef.current);\r\n\r\n\r\n    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\r\n      mediaRecorderRef.current.stop();\r\n      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());\r\n    }\r\n    audioChunksRef.current = [];\r\n    setAudioURL(null);\r\n    setSendButton(false);\r\n    setIsRecording(false);\r\n    setIsPaused(false);\r\n    setFileType(undefined);\r\n    setFile(null);\r\n    resetState();\r\n    await navigator.mediaDevices.getUserMedia({ audio: false });\r\n  };\r\n  const prevRec = () => {\r\n    setIsPaused(false);\r\n    clearInterval(timerRef.current);\r\n    mediaRecorderRef.current.onstop = async () => {\r\n      const audioBlob = new Blob(audioChunksRef.current, { type: \"audio/wav\" });\r\n      audioChunksRef.current = [];\r\n      setAudioURL(URL.createObjectURL(audioBlob));\r\n      const type = audioBlob.type;\r\n      setFileType(type);\r\n      setFile(audioBlob);\r\n    };\r\n    mediaRecorderRef.current.stop();\r\n  };\r\n  const pauseRecording = () => {\r\n    setIsPaused(true);\r\n    clearInterval(timerRef.current);\r\n    mediaRecorderRef.current.pause();\r\n  };\r\n  const resumeRecording = () => {\r\n    setIsPaused(false);\r\n    timerRef.current = setInterval(() => {\r\n      setRecordingTime((prevTime) => prevTime + 1);\r\n    }, 1000); // Restart the timer\r\n    mediaRecorderRef.current.resume();\r\n  };\r\n  const handleSendRec = async () => {\r\n    if (fileType && file) {\r\n      const recFile = {\r\n        file: file,\r\n        fileType: fileType\r\n      };\r\n      sendRecFunc(recFile, audioURL);\r\n      return;\r\n    }\r\n    let fileData, fileTp, localUrl;\r\n\r\n    mediaRecorderRef.current.onstop = async () => {\r\n      const audioBlob = new Blob(audioChunksRef.current, {\r\n        type: \"audio/mpeg\",\r\n      });\r\n      audioChunksRef.current = [];\r\n      localUrl = URL.createObjectURL(audioBlob);\r\n      fileTp = audioBlob.type;\r\n      fileData = audioBlob;\r\n\r\n      const recFile = {\r\n        file: fileData,\r\n        fileType: fileTp\r\n      };\r\n      sendRecFunc(recFile, localUrl);\r\n    };\r\n\r\n    setIsPaused(false);\r\n    clearInterval(timerRef.current);\r\n    mediaRecorderRef.current.stop();\r\n  };\r\n  const sendRecFunc = async (recFile, localUrl) => {\r\n    const uid = uuidv4();\r\n    const date = new Date();\r\n    const preData = {\r\n      track_id: uid,\r\n      agent_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.user_id,\r\n      agent_name:\r\n        currentUser.user_type === \"admin\" ? \"admin\" : currentUser.name,\r\n      manager_id:\r\n        currentUser.user_type === \"admin\" ? \"1\" : currentUser.manager,\r\n      manager_name:\r\n        currentUser.user_type === \"admin\"\r\n          ? \"admin\"\r\n          : currentUser.manager_name,\r\n      team_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.team,\r\n      team_name:\r\n        currentUser.user_type === \"admin\" ? \"admin\" : currentUser.team_name,\r\n      req_from: \"AGENT_REPLY\",\r\n      method: \"media_reply\",\r\n      file_url: localUrl,\r\n      caption: \"\",\r\n      message_type: \"AUDIO\",\r\n      brand_number: currentUser.brand_number,\r\n      mobile: props.selectedMobile,\r\n      content: \"\",\r\n      status: \"pending\",\r\n      created: date,\r\n      message_content: \"\",\r\n    };\r\n    let newdata = [...props.convData.conversion, preData];\r\n\r\n    dispatch({\r\n      type: \"CHANGE_USER\",\r\n      payload: {\r\n        mobile: props.selectedMobile,\r\n        conversation: newdata,\r\n        name: props.convData.selectedName,\r\n      },\r\n    });\r\n    resetState();\r\n    try {\r\n      const rec_url = await uploadRecording(recFile);\r\n\r\n      const { data } = await sendMessage({\r\n        track_id: uid,\r\n        agent_id:\r\n          currentUser.user_type === \"admin\" ? \"1\" : currentUser.user_id,\r\n        agent_name:\r\n          currentUser.user_type === \"admin\" ? \"admin\" : currentUser.name,\r\n        manager_id:\r\n          currentUser.user_type === \"admin\" ? \"1\" : currentUser.manager,\r\n        manager_name:\r\n          currentUser.user_type === \"admin\"\r\n            ? \"admin\"\r\n            : currentUser.manager_name,\r\n        team_id: currentUser.user_type === \"admin\" ? \"1\" : currentUser.team,\r\n        team_name:\r\n          currentUser.user_type === \"admin\" ? \"admin\" : currentUser.team_name,\r\n        token: currentUser.parent_token,\r\n        user_id: currentUser.parent_id,\r\n        method: \"media_reply\",\r\n        attachment_url: rec_url,\r\n        caption: \"\",\r\n        message_type: \"AUDIO\",\r\n        brand_number: currentUser.brand_number,\r\n        mobile: props.selectedMobile,\r\n        content: \"\",\r\n      });\r\n\r\n      if (data.success === true) {\r\n        const updatedChat = newdata.map((chatdata) => {\r\n          if (chatdata.track_id === data.track_id) {\r\n            return { ...chatdata, status: \"Submitted\", file_url: rec_url };\r\n          }\r\n          return chatdata;\r\n        });\r\n        dispatch({\r\n          type: \"CHANGE_USER\",\r\n          payload: {\r\n            mobile: props.selectedMobile,\r\n            conversation: updatedChat,\r\n            name: props.convData.selectedName,\r\n          },\r\n        });\r\n      } else {\r\n        resetState();\r\n        toast.error(data.message);\r\n      }\r\n    } catch (error) {\r\n      toast.error(error.message);\r\n      console.log(\"Error sending message:\", error);\r\n    }\r\n  }\r\n  const formatText = (inputText) => {\r\n    return inputText.replace(/([*~_])([^*~_]*[^*~_])\\1/g, (match, p1, p2) => {\r\n      if (p1 === \"*\") {\r\n        return `<b>${p2}</b>`;\r\n      } else if (p1 === \"_\") {\r\n        return `<i>${p2}</i>`;\r\n      } else if (p1 === \"~\") {\r\n        return `<s>${p2}</s>`;\r\n      }\r\n      return match; // Return the original text if no match\r\n    });\r\n  };\r\n\r\n\r\n  return (\r\n    <div style={{ width: \"100%\" }}>\r\n      <div className=\"emojiMobilecss\" ref={pickerRef}>\r\n        <EmojiPicker\r\n          onEmojiClick={emojiselect}\r\n          open={emojiStatus}\r\n          height={400}\r\n          width={\"auto\"}\r\n          autoFocusSearch={false}\r\n        />\r\n      </div>\r\n\r\n      <div>\r\n        {isViewerOpen && (\r\n          <ImageViewer imageUrl={selectedImage} onClose={closeImageViewer} />\r\n        )}\r\n        {showPrev && (\r\n          <div className=\"popup\">\r\n            <div className=\"popupInner\">\r\n              <button className=\"cancelButton\" onClick={togglePopup}>\r\n                x\r\n              </button>\r\n              {showPrev && (\r\n                <div>\r\n                  <h4>Preview:</h4>\r\n                  <div className=\"popupcontent\">\r\n                    {file.type.startsWith(\"video/\") ? (\r\n                      <video controls width=\"500\">\r\n                        <source src={previewUrl} type={file.type} />\r\n                        Your browser does not support the video tag.\r\n                      </video>\r\n                    ) : file.type === \"application/pdf\" ? (\r\n                      <iframe\r\n                        title=\"pdf\"\r\n                        src={previewUrl}\r\n                        style={{ width: 200, height: 300 }}\r\n                      ></iframe>\r\n                    ) : (\r\n                      <img\r\n                        src={previewUrl}\r\n                        alt=\"Preview\"\r\n                        style={{ maxWidth: \"50%\", maxHeight: \"50%\" }}\r\n                      />\r\n                    )}\r\n                  </div>\r\n                  {fileSize > 10.0 && (\r\n                    <div>\r\n                      <small className=\"text-danger\">\r\n                        *File size should be smaller than 10MB\r\n                      </small>\r\n                    </div>\r\n                  )}\r\n                  <div className=\"d-flex justify-content-center\">\r\n                    <textarea\r\n                      type=\"text\"\r\n                      value={caption}\r\n                      className=\"form-control mt-4\"\r\n                      placeholder=\"Enter caption (optional)\"\r\n                      onChange={(e) => setCaption(e.target.value)}\r\n                    ></textarea>\r\n\r\n                    <div className=\"mt-4 ms-2\">\r\n                      {buttonLoader === false ? (\r\n                        <button\r\n                          onClick={fileSize > 10.0 ? null : handleSend}\r\n                          className=\"btn btn-primary btn-lg chat-send waves-effect waves-light\"\r\n                          data-bs-toggle=\"collapse\"\r\n                          data-bs-target=\".chat-input-collapse1.show\"\r\n                        >\r\n                          <i\r\n                            className=\"bx bxs-send align-middle\"\r\n                            id=\"submit-btn\"\r\n                          />\r\n                        </button>\r\n                      ) : (\r\n                        <button\r\n                          className=\"btn btn-primary\"\r\n                          type=\"button\"\r\n                          disabled\r\n                        >\r\n                          <span\r\n                            className=\"spinner-border spinner-border-sm\"\r\n                            role=\"status\"\r\n                            aria-hidden=\"true\"\r\n                          ></span>\r\n                          <span className=\"sr-only\">Sending...</span>\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"chat-input-section p-2 p-lg-2\" id=\"keyboard\">\r\n        {isOldMsg ? (\r\n          <div className=\"d-flex justify-content-center align-items-center\">\r\n            <h6 style={{ color: \"red\" }} className=\"me-4\">\r\n              This chat is older than 24h, Send template message\r\n            </h6>\r\n            <button\r\n              type=\"button\"\r\n              className=\"btn btn-success\"\r\n              onClick={() => setSendTemplatePopUp(true)}\r\n            >\r\n              select template\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div>\r\n            {error.error && (\r\n              <div className={`text-danger p-1 mb-2 ${error.errorType}`}>\r\n                {error.errorMessage}\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"row g-0 align-items-center\">\r\n              <div className=\"file_Upload\" />\r\n\r\n              {isRecording === false ? (\r\n                <>\r\n                  <div className=\"col-auto\">\r\n                    <div className=\"chat-input-links me-md-2\">\r\n                      <div\r\n                        className=\"links-list-item\"\r\n                        data-bs-toggle=\"tooltip\"\r\n                        data-bs-trigger=\"hover\"\r\n                        data-bs-placement=\"top\"\r\n                        title=\"file\"\r\n                      >\r\n                        <button\r\n                          type=\"button\"\r\n                          className=\"btn btn-link text-decoration-none btn-lg waves-effect iconshovers\"\r\n                          data-bs-toggle=\"collapse\"\r\n                          data-bs-target=\"#chatinputmorecollapse\"\r\n                          aria-expanded=\"false\"\r\n                          aria-controls=\"chatinputmorecollapse\"\r\n                          onClick={() => setEmojiStatus(false)}\r\n                        >\r\n                          <FaPlus className=\"align-middle iconsBgs\" />\r\n                        </button>\r\n                      </div>\r\n                      <div\r\n                        className=\"links-list-item\"\r\n                        data-bs-toggle=\"tooltip\"\r\n                        data-bs-trigger=\"hover\"\r\n                        data-bs-placement=\"top\"\r\n                        title=\"Emoji\"\r\n                      >\r\n                        <button\r\n                          type=\"button\"\r\n                          className=\"btn btn-link text-decoration-none btn-lg waves-effect emoji-btn iconshovers\"\r\n                          id=\"emoji-btn\"\r\n                          onClick={toggleEmoji}\r\n                        >\r\n                          {emojiStatus === false ? (\r\n                            <i className=\"bx bx-smile iconsBgs align-middle\" />\r\n                          ) : (\r\n                            <i className=\"bx bx-x iconsBg align-middle\" />\r\n                          )}\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"col\">\r\n                    <div className=\"position-relative\">\r\n                      <div className=\"chat-input-feedback\">\r\n                        Please Enter a Message\r\n                      </div>\r\n                      {/* <input\r\n                autoComplete=\"off\"\r\n                type=\"text\"\r\n                onChange={(e) => setText(e.target.value)}\r\n                onKeyPress={(e) => handleKeyPress(e)}\r\n                value={text}\r\n                className=\"form-control form-control-lg chat-input\"\r\n                autoFocus\r\n                id=\"chat-input\"\r\n                placeholder=\"Type your message...\"\r\n              /> */}\r\n\r\n                      {showPreview && <div className=\"preview\"\r\n                        dangerouslySetInnerHTML={{ __html: formatText(text) }} />}\r\n                      <textarea\r\n                        ref={textareaRef}\r\n                        onChange={handleTextarea}\r\n                        onKeyDown={(e) => handleKeyPress(e)}\r\n                        className=\"form-control form-control-lg chat-input py-2\"\r\n                        rows=\"2\"\r\n                        value={text}\r\n                        id=\"chat-input\"\r\n                        placeholder=\"Type your message...\"\r\n                      ></textarea>\r\n                    </div>\r\n                  </div>\r\n                </>\r\n              ) : (\r\n                <div className=\"col audioContainer\">\r\n                  <div className=\"audioBox\">\r\n                    <i className=\"bx bxs-microphone align-middle mics\" />\r\n                  </div>\r\n                  {!audioURL ? (\r\n                    <div className=\"audioTimer\">\r\n                      <div className=\"audioplayPause\">\r\n                        {\" \"}\r\n                        <FaRegStopCircle className=\"me-2\" onClick={prevRec} />\r\n                        {isRecording && !isPaused && (\r\n                          <FaPause onClick={pauseRecording} />\r\n                        )}\r\n                        {isRecording && isPaused && (\r\n                          <FaPlay onClick={resumeRecording} />\r\n                        )}\r\n                      </div>\r\n                      <div className=\"audioFrequency\">\r\n                        <img src=\"../icon-sound.gif\" alt=\"sound-graph\" />\r\n                        <img\r\n                          src=\"../icon-sound.gif\"\r\n                          className=\"ml-2\"\r\n                          alt=\"sound-graph\"\r\n                        />\r\n                      </div>\r\n                      <div className=\"audiotimebox\">\r\n                        {\" \"}\r\n                        {formatTime(recordingTime)}\r\n                      </div>\r\n                    </div>\r\n                  ) : (\r\n                    <audio controls src={audioURL}></audio>\r\n                  )}\r\n                  <div className=\"audioDelete\">\r\n                    {isRecording && <MdDelete onClick={stopRecording} />}\r\n                  </div>\r\n                </div>\r\n              )}\r\n              <div className=\"col-auto\">\r\n                <div className=\"chat-input-links ms-2 gap-md-1\">\r\n                  <div className=\"links-list-item\">\r\n                    {sendButton ? (\r\n                      <button\r\n                        onClick={isRecording ? handleSendRec : handleSend}\r\n                        className=\"btn btn-primary btn-lg chat-send waves-effect waves-light\"\r\n                        data-bs-toggle=\"collapse\"\r\n                        data-bs-target=\".chat-input-collapse1.show\"\r\n                        title=\"Send message\"\r\n                      >\r\n                        <i\r\n                          className=\"bx bxs-send align-middle\"\r\n                          id=\"submit-btn\"\r\n                        />\r\n                      </button>\r\n                    ) : (\r\n                      <button\r\n                        onClick={startRecording}\r\n                        className=\"btn btn-primary btn-lg chat-send waves-effect waves-light\"\r\n                        data-bs-toggle=\"collapse\"\r\n                        data-bs-target=\".chat-input-collapse1.show\"\r\n                        title=\"Send message\"\r\n                      >\r\n                        <i className=\"bx bxs-microphone align-middle\" />\r\n                      </button>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Attachment Box */}\r\n            <div\r\n              className={`chat-input-collapse chat-input-collapse1 collapse`}\r\n              id=\"chatinputmorecollapse\"\r\n            >\r\n              <div className=\"card mb-0\">\r\n                <div className=\"card-body py-2 px-2\">\r\n                  <div className=\"swiper chatinput-links\">\r\n                    <div className=\"swiper-wrapper d-flex flex-column\">\r\n                      <div className=\"swiper-slide swiperhover py-1 px-2\">\r\n                        <div className=\"text-center position-relative d-flex\">\r\n                          <input\r\n                            type=\"file\"\r\n                            style={{ display: \"none\" }}\r\n                            id=\"attached-file\"\r\n                            onChange={fileHandler}\r\n                          />\r\n                          <label htmlFor=\"attached-file\" className=\"attachFiles\"><i className=\"bx bx-paperclip iconsBgs bgBlue\" />\r\n                            <div className=\"font-size-16 ms-2 text-body text-truncate\">Document</div>\r\n                          </label>\r\n                        </div>\r\n                      </div>\r\n                      {/* <div className=\"swiper-slide\">\r\n                    <div className=\"text-center px-2\">\r\n                      <div className=\"avatar-sm mx-auto\">\r\n                        <div className=\"avatar-title font-size-18 bg-soft-primary text-primary rounded-circle\">\r\n                          <i className=\"bx bxs-camera\" />\r\n                        </div>\r\n                      </div>\r\n                      <h5 className=\"font-size-11 text-uppercase text-truncate mt-3 mb-0\">\r\n                        <a href=\"#\" className=\"text-body stretched-link\">\r\n                          Camera\r\n                        </a>\r\n                      </h5>\r\n                    </div>\r\n                  </div> */}\r\n                      <div className=\"swiper-slide swiperhover py-1 px-2\">\r\n                        <div className=\"text-center position-relative d-flex\">\r\n                          <input\r\n                            id=\"galleryfile-input\"\r\n                            type=\"file\"\r\n                            className=\"d-none\"\r\n                            onChange={fileHandler}\r\n                          />\r\n                          <label htmlFor=\"galleryfile-input\" className=\"attachFiles\">\r\n                            <i className=\"bx bx-images iconsBgs bgOrange\" />\r\n                            <div className=\"font-size-16 ms-2 text-body text-truncate\">Image</div>\r\n                          </label>\r\n\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"swiper-slide swiperhover py-1 px-2\">\r\n                        <div className=\"text-center position-relative d-flex\">\r\n                          <input\r\n                            id=\"audiofile-input\"\r\n                            type=\"file\"\r\n                            className=\"d-none\"\r\n                            onChange={fileHandler}\r\n                          />\r\n                          <label htmlFor=\"audiofile-input\" className=\"attachFiles\">\r\n                            <i className=\"bx bx-video iconsBgs bgGreen\" />\r\n                            <div className=\"font-size-15 ms-2 text-body text-truncate\">Video</div>\r\n                          </label>\r\n\r\n                        </div>\r\n                      </div>\r\n                      {/* <div className=\"swiper-slide\">\r\n                    <div className=\"text-center px-2\">\r\n                      <div className=\"avatar-sm mx-auto\">\r\n                        <div className=\"avatar-title font-size-18 bg-soft-primary text-primary rounded-circle\">\r\n                          <i className=\"bx bx-current-location\" />\r\n                        </div>\r\n                      </div>\r\n                      <h5 className=\"font-size-11 text-uppercase text-truncate mt-3 mb-0\">\r\n                        <a href=\"#\" className=\"text-body stretched-link\">\r\n                          Location\r\n                        </a>\r\n                      </h5>\r\n                    </div>\r\n                  </div> */}\r\n                      {/* <div className=\"swiper-slide\">\r\n                    <div className=\"text-center px-2\">\r\n                      <div className=\"avatar-sm mx-auto\">\r\n                        <div className=\"avatar-title font-size-18 bg-soft-primary text-primary rounded-circle\">\r\n                          <i className=\"bx bxs-user-circle\" />\r\n                        </div>\r\n                      </div>\r\n                      <h5 className=\"font-size-11 text-uppercase text-truncate mt-3 mb-0\">\r\n                        <a\r\n                          href=\"#\"\r\n                          className=\"text-body stretched-link\"\r\n                          data-bs-toggle=\"modal\"\r\n                          data-bs-target=\".contactModal\"\r\n                        >\r\n                          Contacts\r\n                        </a>\r\n                      </h5>\r\n                    </div>\r\n                  </div> */}\r\n                      {/* <div className=\"swiper-slide d-block d-sm-none\">\r\n                    <div className=\"text-center px-2\">\r\n                      <div className=\"avatar-sm mx-auto\">\r\n                        <div className=\"avatar-title font-size-18 bg-soft-primary text-primary rounded-circle\">\r\n                          <i className=\"bx bx-microphone\" />\r\n                        </div>\r\n                      </div>\r\n                      <h5 className=\"font-size-11 text-uppercase text-truncate mt-3 mb-0\">\r\n                        <a href=\"#\" className=\"text-body stretched-link\">\r\n                          Audio\r\n                        </a>\r\n                      </h5>\r\n                    </div>\r\n                  </div> */}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Reply Box */}\r\n      {/* <div className=\"replyCard\">\r\n        <div className=\"card mb-0\">\r\n          <div className=\"card-body py-3\">\r\n            <div className=\"replymessage-block mb-0 d-flex align-items-start\">\r\n              <div className=\"flex-grow-1\">\r\n                <h5 className=\"conversation-name\" />\r\n                <p className=\"mb-0\" />\r\n              </div>\r\n              <div className=\"flex-shrink-0\">\r\n                <button\r\n                  type=\"button\"\r\n                  id=\"close_toggle\"\r\n                  className=\"btn btn-sm btn-link mt-n2 me-n3 font-size-18\"\r\n                >\r\n                  <i className=\"bx bx-x align-middle\" />\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div> */}\r\n\r\n      {sendTemplatePopUp && (\r\n        <div className=\"popup-agent\">\r\n          <div className=\"assign-popup-content-agent\">\r\n            <div style={{ float: \"right\", cursor: \"pointer\" }}>\r\n              <i\r\n                className=\"bx bx-x float-right\"\r\n                style={{ fontSize: \"26px\" }}\r\n                onClick={() => setSendTemplatePopUp(false)}\r\n              ></i>\r\n            </div>\r\n            <div>\r\n              <SendTemplate mobile={selectedMobileNumber} />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\n\r\nexport default Input;\r\n"]}, "metadata": {}, "sourceType": "module"}