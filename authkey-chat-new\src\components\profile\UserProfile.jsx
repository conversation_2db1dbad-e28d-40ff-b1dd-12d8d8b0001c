import { useContext, useEffect, useState } from "react";
import { BASE_URL, BASE_URL2 } from "../../api/api";
import axios from "axios";
import { AuthContext } from "../../context/AuthContext";
import { ChatState } from "../../context/AllProviders";
import { FormControlLabel } from "@mui/material";
import { styled } from "@mui/material/styles";
import Switch from "@mui/material/Switch";
import { toast } from "react-toastify";
import styles from './userProfile.module.css'
import AgentBreakModal from "../AgentBreakModal/AgentBreakModal";
const UserProfile = (props) => {
  const { currentUser, updateUser } = useContext(AuthContext);
  const { wpProfile, setWpProfile,breakInfo } = ChatState();
  const [activeStatus, setActiveStatus] = useState(
    currentUser.availability === 1 ? true : false
  );
  const [isBreakModalOpen, setBreakModalOpen] = useState(false);

  useEffect(() => {
    const wpProfile = async () => {
      if (currentUser.parent_id) {
        const { data } = await axios.get(
          `${BASE_URL}/wp_profile.php?user_id=${currentUser.parent_id}&method=retrieve&token=${currentUser.parent_token}`
        );
        if (data.success === true) {
          setWpProfile(data.data[0]);
        }
      }
    };
    wpProfile();
  }, [currentUser]);
  const IOSSwitch = styled((props) => (
    <Switch
      focusVisibleClassName=".Mui-focusVisible"
      disableRipple
      {...props}
    />
  ))(({ theme }) => ({
    width: 42,
    height: 26,
    padding: 0,
    "& .MuiSwitch-switchBase": {
      padding: 0,
      margin: 2,
      transitionDuration: "300ms",
      "&.Mui-checked": {
        transform: "translateX(16px)",
        color: "#fff",
        "& + .MuiSwitch-track": {
          backgroundColor: "#65C466",
          opacity: 1,
          border: 0,
          ...theme.applyStyles("dark", {
            backgroundColor: "#2ECA45",
          }),
        },
        "&.Mui-disabled + .MuiSwitch-track": {
          opacity: 0.5,
        },
      },
      "&.Mui-focusVisible .MuiSwitch-thumb": {
        color: "#33cf4d",
        border: "6px solid #fff",
      },
      "&.Mui-disabled .MuiSwitch-thumb": {
        color: theme.palette.grey[100],
        ...theme.applyStyles("dark", {
          color: theme.palette.grey[600],
        }),
      },
      "&.Mui-disabled + .MuiSwitch-track": {
        opacity: 0.7,
        ...theme.applyStyles("dark", {
          opacity: 0.3,
        }),
      },
    },
    "& .MuiSwitch-thumb": {
      boxSizing: "border-box",
      width: 22,
      height: 22,
    },
    "& .MuiSwitch-track": {
      borderRadius: 26 / 2,
      backgroundColor: "#E9E9EA",
      opacity: 1,
      transition: theme.transitions.create(["background-color"], {
        duration: 500,
      }),
      ...theme.applyStyles("dark", {
        backgroundColor: "#39393D",
      }),
    },
  }));
 

  const handleActiveStatus = async(e) => {
    setActiveStatus(e.target.checked);
    const dataforstatus = {
      token: currentUser.parent_token,
      user_id: currentUser.parent_id,
      method: "active_status",
      agent_id:currentUser.user_id,
      user_type:currentUser.user_type,
      status:e.target.checked
    };
    try {
      const {data} = await axios.post(`${BASE_URL2}/whatsapp_user`, dataforstatus);
      if(data.success===true){
        toast.success(data.message);
      }
      const updatedData={...currentUser, availability:data.data};
      
      
      updateUser(updatedData);
    } catch (error) {
      toast.error(error.message);
      console.log(error.message);
    }
  };
  return (
    <div className={`${styles.profileContainer}`}>
      {/* Profile Header */}
      <div className={`${styles.header} shadow-sm`}>
        <h4 className="fw-bold">Profile</h4>
      </div>

      {/* Profile Image - Fixed */}
      <div className={`${styles.profileImageContainer}`}>
        <img
          src={wpProfile.image_url || "/images/user.png"}
          className={`${styles.profileImage}`}
          alt="User Avatar"
        />
       {currentUser.user_type==="agent" && !breakInfo?.active && <button className={styles.breakButton} onClick={() => setBreakModalOpen(true)}>
          <i className="bx bx-coffee" /> Take a Break
        </button>}
      </div>

      {/* Scrollable Content */}
      <div className={styles.scrollableContent}>
        {/* About Section */}
        <Section title="About">
          <InfoItem icon="bx-user" text={currentUser.name} />
          <InfoItem icon="bx-message-rounded-dots" text={currentUser.mobile} />
        </Section>

        {/* Company Details */}
        <Section title="Company Details">
          <InfoItem icon="bx-building-house" text={wpProfile.comp_name} />
          <InfoItem icon="bx-phone" text={wpProfile.brand_number} />
          <InfoItem icon="bx-location-plus" text={wpProfile.address} />
        </Section>

        {/* Settings - Only for Agent */}
        {/* {currentUser.user_type === "agent" && (
          <Section title="Settings">
            <div className="d-flex justify-content-between align-items-center">
              <p className="mb-0 fw-medium">Active Status</p>
              <FormControlLabel
                control={
                  <IOSSwitch sx={{ m: 1 }} checked={activeStatus} onChange={handleActiveStatus} />
                }
              />
            </div>
          </Section>
        )} */}
      </div>
      {isBreakModalOpen && <AgentBreakModal onClose={() => setBreakModalOpen(false)} currentUser={currentUser} />}
    </div>
  );
};



const Section = ({ title, children }) => (
  <div className={styles.section}>
    <h6 className="fw-bold">{title}</h6>
    <div className={styles.sectionContent}>{children}</div>
  </div>
);

// Reusable Info Item Component
const InfoItem = ({ icon, text }) => (
  <div className={`${styles.infoItem}`}>
    <div className={styles.iconWrapper}>
      <i className={`bx ${icon} align-middle text-muted`} />
    </div>
    <p className="mb-0">{text}</p>
  </div>
);

export default UserProfile;
