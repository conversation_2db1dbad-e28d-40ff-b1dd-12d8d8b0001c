{"nested": {"google": {"nested": {"cloud": {"nested": {"translation": {"nested": {"v3": {"options": {"csharp_namespace": "Google.Cloud.Translate.V3", "go_package": "cloud.google.com/go/translate/apiv3/translatepb;translatepb", "java_multiple_files": true, "java_outer_classname": "TranslationServiceProto", "java_package": "com.google.cloud.translate.v3", "php_namespace": "Google\\Cloud\\Translate\\V3", "ruby_package": "Google::Cloud::Translate::V3", "objc_class_prefix": "CTRL3"}, "nested": {"AdaptiveMtDataset": {"options": {"(google.api.resource).type": "translate.googleapis.com/AdaptiveMtDataset", "(google.api.resource).pattern": "projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}"}, "fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/AdaptiveMtDataset"}}, "displayName": {"type": "string", "id": 2}, "sourceLanguageCode": {"type": "string", "id": 3}, "targetLanguageCode": {"type": "string", "id": 4}, "exampleCount": {"type": "int32", "id": 5}, "createTime": {"type": "google.protobuf.Timestamp", "id": 9, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 10, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "CreateAdaptiveMtDatasetRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "adaptiveMtDataset": {"type": "AdaptiveMtDataset", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "DeleteAdaptiveMtDatasetRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/AdaptiveMtDataset"}}}}, "GetAdaptiveMtDatasetRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/AdaptiveMtDataset"}}}}, "ListAdaptiveMtDatasetsRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "pageSize": {"type": "int32", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "pageToken": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "filter": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "ListAdaptiveMtDatasetsResponse": {"fields": {"adaptiveMtDatasets": {"rule": "repeated", "type": "AdaptiveMtDataset", "id": 1, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "nextPageToken": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "AdaptiveMtTranslateRequest": {"oneofs": {"_referenceSentenceConfig": {"oneof": ["referenceSentenceConfig"]}, "_glossaryConfig": {"oneof": ["glossaryConfig"]}}, "fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "dataset": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/AdaptiveMtDataset"}}, "content": {"rule": "repeated", "type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "referenceSentenceConfig": {"type": "ReferenceSentenceConfig", "id": 6, "options": {"proto3_optional": true}}, "glossaryConfig": {"type": "GlossaryConfig", "id": 7, "options": {"(google.api.field_behavior)": "OPTIONAL", "proto3_optional": true}}}, "nested": {"ReferenceSentencePair": {"fields": {"sourceSentence": {"type": "string", "id": 1}, "targetSentence": {"type": "string", "id": 2}}}, "ReferenceSentencePairList": {"fields": {"referenceSentencePairs": {"rule": "repeated", "type": "ReferenceSentencePair", "id": 1}}}, "ReferenceSentenceConfig": {"fields": {"referenceSentencePairLists": {"rule": "repeated", "type": "ReferenceSentencePairList", "id": 1}, "sourceLanguageCode": {"type": "string", "id": 2}, "targetLanguageCode": {"type": "string", "id": 3}}}, "GlossaryConfig": {"fields": {"glossary": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/Glossary"}}, "ignoreCase": {"type": "bool", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "contextualTranslationEnabled": {"type": "bool", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}}}, "AdaptiveMtTranslation": {"fields": {"translatedText": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "AdaptiveMtTranslateResponse": {"fields": {"translations": {"rule": "repeated", "type": "AdaptiveMtTranslation", "id": 1, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "languageCode": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "glossaryTranslations": {"rule": "repeated", "type": "AdaptiveMtTranslation", "id": 4}}}, "AdaptiveMtFile": {"options": {"(google.api.resource).type": "translate.googleapis.com/AdaptiveMtFile", "(google.api.resource).pattern": "projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}", "(google.api.resource).plural": "adaptiveMtFiles", "(google.api.resource).singular": "adaptiveMtFile"}, "fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/AdaptiveMtFile"}}, "displayName": {"type": "string", "id": 2}, "entryCount": {"type": "int32", "id": 3}, "createTime": {"type": "google.protobuf.Timestamp", "id": 4, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 5, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "GetAdaptiveMtFileRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/AdaptiveMtFile"}}}}, "DeleteAdaptiveMtFileRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/AdaptiveMtFile"}}}}, "ImportAdaptiveMtFileRequest": {"oneofs": {"source": {"oneof": ["fileInputSource", "gcsInputSource"]}}, "fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/AdaptiveMtDataset"}}, "fileInputSource": {"type": "FileInputSource", "id": 2}, "gcsInputSource": {"type": "GcsInputSource", "id": 3}}}, "ImportAdaptiveMtFileResponse": {"fields": {"adaptiveMtFile": {"type": "AdaptiveMtFile", "id": 1, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "ListAdaptiveMtFilesRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/AdaptiveMtDataset"}}, "pageSize": {"type": "int32", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "pageToken": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "ListAdaptiveMtFilesResponse": {"fields": {"adaptiveMtFiles": {"rule": "repeated", "type": "AdaptiveMtFile", "id": 1, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "nextPageToken": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "AdaptiveMtSentence": {"options": {"(google.api.resource).type": "translate.googleapis.com/AdaptiveMtSentence", "(google.api.resource).pattern": "projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}/adaptiveMtSentences/{sentence}", "(google.api.resource).plural": "adaptiveMtSentences", "(google.api.resource).singular": "adaptiveMtSentence"}, "fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/AdaptiveMtSentence"}}, "sourceSentence": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "targetSentence": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "createTime": {"type": "google.protobuf.Timestamp", "id": 4, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 5, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "ListAdaptiveMtSentencesRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/AdaptiveMtFile"}}, "pageSize": {"type": "int32", "id": 2}, "pageToken": {"type": "string", "id": 3}}}, "ListAdaptiveMtSentencesResponse": {"fields": {"adaptiveMtSentences": {"rule": "repeated", "type": "AdaptiveMtSentence", "id": 1, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "nextPageToken": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "GcsInputSource": {"fields": {"inputUri": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "FileInputSource": {"fields": {"mimeType": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "content": {"type": "bytes", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "displayName": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "GcsOutputDestination": {"fields": {"outputUriPrefix": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "OperationState": {"values": {"OPERATION_STATE_UNSPECIFIED": 0, "OPERATION_STATE_RUNNING": 1, "OPERATION_STATE_SUCCEEDED": 2, "OPERATION_STATE_FAILED": 3, "OPERATION_STATE_CANCELLING": 4, "OPERATION_STATE_CANCELLED": 5}}, "GlossaryEntry": {"options": {"(google.api.resource).type": "translate.googleapis.com/GlossaryEntry", "(google.api.resource).pattern": "projects/{project}/locations/{location}/glossaries/{glossary}/glossaryEntries/{glossary_entry}", "(google.api.resource).plural": "glossaryEntries", "(google.api.resource).singular": "glossaryEntry"}, "oneofs": {"data": {"oneof": ["termsPair", "termsSet"]}}, "fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "IDENTIFIER"}}, "termsPair": {"type": "GlossaryTermsPair", "id": 2}, "termsSet": {"type": "GlossaryTermsSet", "id": 3}, "description": {"type": "string", "id": 4}}, "nested": {"GlossaryTermsPair": {"fields": {"sourceTerm": {"type": "GlossaryTerm", "id": 1}, "targetTerm": {"type": "GlossaryTerm", "id": 2}}}, "GlossaryTermsSet": {"fields": {"terms": {"rule": "repeated", "type": "GlossaryTerm", "id": 1}}}}}, "GlossaryTerm": {"fields": {"languageCode": {"type": "string", "id": 1}, "text": {"type": "string", "id": 2}}}, "ImportDataRequest": {"fields": {"dataset": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "inputConfig": {"type": "DatasetInputConfig", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "DatasetInputConfig": {"fields": {"inputFiles": {"rule": "repeated", "type": "InputFile", "id": 1}}, "nested": {"InputFile": {"oneofs": {"source": {"oneof": ["gcsSource"]}}, "fields": {"usage": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "gcsSource": {"type": "GcsInputSource", "id": 3}}}}}, "ImportDataMetadata": {"fields": {"state": {"type": "OperationState", "id": 1}, "createTime": {"type": "google.protobuf.Timestamp", "id": 2}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 3}, "error": {"type": "google.rpc.Status", "id": 4}}}, "ExportDataRequest": {"fields": {"dataset": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "outputConfig": {"type": "DatasetOutputConfig", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "DatasetOutputConfig": {"oneofs": {"destination": {"oneof": ["gcsDestination"]}}, "fields": {"gcsDestination": {"type": "GcsOutputDestination", "id": 1}}}, "ExportDataMetadata": {"fields": {"state": {"type": "OperationState", "id": 1}, "createTime": {"type": "google.protobuf.Timestamp", "id": 2}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 3}, "error": {"type": "google.rpc.Status", "id": 4}}}, "DeleteDatasetRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/Dataset"}}}}, "DeleteDatasetMetadata": {"fields": {"state": {"type": "OperationState", "id": 1}, "createTime": {"type": "google.protobuf.Timestamp", "id": 2}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 3}, "error": {"type": "google.rpc.Status", "id": 4}}}, "GetDatasetRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/Dataset"}}}}, "ListDatasetsRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "pageSize": {"type": "int32", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "pageToken": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "ListDatasetsResponse": {"fields": {"datasets": {"rule": "repeated", "type": "Dataset", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "CreateDatasetRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "dataset": {"type": "Dataset", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "CreateDatasetMetadata": {"fields": {"state": {"type": "OperationState", "id": 1}, "createTime": {"type": "google.protobuf.Timestamp", "id": 2}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 3}, "error": {"type": "google.rpc.Status", "id": 4}}}, "ListExamplesRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/Dataset"}}, "filter": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "pageSize": {"type": "int32", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "pageToken": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "ListExamplesResponse": {"fields": {"examples": {"rule": "repeated", "type": "Example", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "Example": {"options": {"(google.api.resource).type": "translate.googleapis.com/Example", "(google.api.resource).pattern": "projects/{project}/locations/{location}/datasets/{dataset}/examples/{example}"}, "fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "sourceText": {"type": "string", "id": 2}, "targetText": {"type": "string", "id": 3}, "usage": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "BatchTransferResourcesResponse": {"fields": {"responses": {"rule": "repeated", "type": "TransferResourceResponse", "id": 1}}, "nested": {"TransferResourceResponse": {"fields": {"source": {"type": "string", "id": 1}, "target": {"type": "string", "id": 2}, "error": {"type": "google.rpc.Status", "id": 3}}}}}, "Dataset": {"options": {"(google.api.resource).type": "translate.googleapis.com/Dataset", "(google.api.resource).pattern": "projects/{project}/locations/{location}/datasets/{dataset}"}, "fields": {"name": {"type": "string", "id": 1}, "displayName": {"type": "string", "id": 2}, "sourceLanguageCode": {"type": "string", "id": 3}, "targetLanguageCode": {"type": "string", "id": 4}, "exampleCount": {"type": "int32", "id": 5, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "trainExampleCount": {"type": "int32", "id": 6, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "validateExampleCount": {"type": "int32", "id": 7, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "testExampleCount": {"type": "int32", "id": 8, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "createTime": {"type": "google.protobuf.Timestamp", "id": 9, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 10, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "CreateModelRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "model": {"type": "Model", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "CreateModelMetadata": {"fields": {"state": {"type": "OperationState", "id": 1}, "createTime": {"type": "google.protobuf.Timestamp", "id": 2}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 3}, "error": {"type": "google.rpc.Status", "id": 4}}}, "ListModelsRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "filter": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "pageSize": {"type": "int32", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "pageToken": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "ListModelsResponse": {"fields": {"models": {"rule": "repeated", "type": "Model", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "GetModelRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/Model"}}}}, "DeleteModelRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/Model"}}}}, "DeleteModelMetadata": {"fields": {"state": {"type": "OperationState", "id": 1}, "createTime": {"type": "google.protobuf.Timestamp", "id": 2}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 3}, "error": {"type": "google.rpc.Status", "id": 4}}}, "Model": {"options": {"(google.api.resource).type": "translate.googleapis.com/Model", "(google.api.resource).pattern": "projects/{project}/locations/{location}/models/{model}"}, "fields": {"name": {"type": "string", "id": 1}, "displayName": {"type": "string", "id": 2}, "dataset": {"type": "string", "id": 3}, "sourceLanguageCode": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "targetLanguageCode": {"type": "string", "id": 5, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "trainExampleCount": {"type": "int32", "id": 6, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "validateExampleCount": {"type": "int32", "id": 7, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "testExampleCount": {"type": "int32", "id": 12, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "createTime": {"type": "google.protobuf.Timestamp", "id": 8, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 10, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "TranslationService": {"options": {"(google.api.default_host)": "translate.googleapis.com", "(google.api.oauth_scopes)": "https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/cloud-translation"}, "methods": {"TranslateText": {"requestType": "TranslateTextRequest", "responseType": "TranslateTextResponse", "options": {"(google.api.http).post": "/v3/{parent=projects/*/locations/*}:translateText", "(google.api.http).body": "*", "(google.api.http).additional_bindings.post": "/v3/{parent=projects/*}:translateText", "(google.api.http).additional_bindings.body": "*", "(google.api.method_signature)": "parent,model,mime_type,source_language_code,target_language_code,contents"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{parent=projects/*/locations/*}:translateText", "body": "*", "additional_bindings": {"post": "/v3/{parent=projects/*}:translateText", "body": "*"}}}, {"(google.api.method_signature)": "parent,target_language_code,contents"}, {"(google.api.method_signature)": "parent,model,mime_type,source_language_code,target_language_code,contents"}]}, "RomanizeText": {"requestType": "RomanizeTextRequest", "responseType": "RomanizeTextResponse", "options": {"(google.api.http).post": "/v3/{parent=projects/*/locations/*}:romanizeText", "(google.api.http).body": "*", "(google.api.http).additional_bindings.post": "/v3/{parent=projects/*}:romanizeText", "(google.api.http).additional_bindings.body": "*", "(google.api.method_signature)": "parent,contents"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{parent=projects/*/locations/*}:romanizeText", "body": "*", "additional_bindings": {"post": "/v3/{parent=projects/*}:romanizeText", "body": "*"}}}, {"(google.api.method_signature)": "parent,contents"}]}, "DetectLanguage": {"requestType": "DetectLanguageRequest", "responseType": "DetectLanguageResponse", "options": {"(google.api.http).post": "/v3/{parent=projects/*/locations/*}:detectLanguage", "(google.api.http).body": "*", "(google.api.http).additional_bindings.post": "/v3/{parent=projects/*}:detectLanguage", "(google.api.http).additional_bindings.body": "*", "(google.api.method_signature)": "parent,model,mime_type,content"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{parent=projects/*/locations/*}:detectLanguage", "body": "*", "additional_bindings": {"post": "/v3/{parent=projects/*}:detectLanguage", "body": "*"}}}, {"(google.api.method_signature)": "parent,model,mime_type,content"}]}, "GetSupportedLanguages": {"requestType": "GetSupportedLanguagesRequest", "responseType": "SupportedLanguages", "options": {"(google.api.http).get": "/v3/{parent=projects/*/locations/*}/supportedLanguages", "(google.api.http).additional_bindings.get": "/v3/{parent=projects/*}/supportedLanguages", "(google.api.method_signature)": "parent,model,display_language_code"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{parent=projects/*/locations/*}/supportedLanguages", "additional_bindings": {"get": "/v3/{parent=projects/*}/supportedLanguages"}}}, {"(google.api.method_signature)": "parent,model,display_language_code"}]}, "TranslateDocument": {"requestType": "TranslateDocumentRequest", "responseType": "TranslateDocumentResponse", "options": {"(google.api.http).post": "/v3/{parent=projects/*/locations/*}:translateDocument", "(google.api.http).body": "*"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{parent=projects/*/locations/*}:translateDocument", "body": "*"}}]}, "BatchTranslateText": {"requestType": "BatchTranslateTextRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v3/{parent=projects/*/locations/*}:batchTranslateText", "(google.api.http).body": "*", "(google.longrunning.operation_info).response_type": "BatchTranslateResponse", "(google.longrunning.operation_info).metadata_type": "BatchTranslateMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{parent=projects/*/locations/*}:batchTranslateText", "body": "*"}}, {"(google.longrunning.operation_info)": {"response_type": "BatchTranslateResponse", "metadata_type": "BatchTranslateMetadata"}}]}, "BatchTranslateDocument": {"requestType": "BatchTranslateDocumentRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v3/{parent=projects/*/locations/*}:batchTranslateDocument", "(google.api.http).body": "*", "(google.api.method_signature)": "parent,source_language_code,target_language_codes,input_configs,output_config", "(google.longrunning.operation_info).response_type": "BatchTranslateDocumentResponse", "(google.longrunning.operation_info).metadata_type": "BatchTranslateDocumentMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{parent=projects/*/locations/*}:batchTranslateDocument", "body": "*"}}, {"(google.api.method_signature)": "parent,source_language_code,target_language_codes,input_configs,output_config"}, {"(google.longrunning.operation_info)": {"response_type": "BatchTranslateDocumentResponse", "metadata_type": "BatchTranslateDocumentMetadata"}}]}, "CreateGlossary": {"requestType": "CreateGlossaryRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v3/{parent=projects/*/locations/*}/glossaries", "(google.api.http).body": "glossary", "(google.api.method_signature)": "parent,glossary", "(google.longrunning.operation_info).response_type": "Glossary", "(google.longrunning.operation_info).metadata_type": "CreateGlossaryMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{parent=projects/*/locations/*}/glossaries", "body": "glossary"}}, {"(google.api.method_signature)": "parent,glossary"}, {"(google.longrunning.operation_info)": {"response_type": "Glossary", "metadata_type": "CreateGlossaryMetadata"}}]}, "UpdateGlossary": {"requestType": "UpdateGlossaryRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).patch": "/v3/{glossary.name=projects/*/locations/*/glossaries/*}", "(google.api.http).body": "glossary", "(google.api.method_signature)": "glossary,update_mask", "(google.longrunning.operation_info).response_type": "Glossary", "(google.longrunning.operation_info).metadata_type": "UpdateGlossaryMetadata"}, "parsedOptions": [{"(google.api.http)": {"patch": "/v3/{glossary.name=projects/*/locations/*/glossaries/*}", "body": "glossary"}}, {"(google.api.method_signature)": "glossary,update_mask"}, {"(google.longrunning.operation_info)": {"response_type": "Glossary", "metadata_type": "UpdateGlossaryMetadata"}}]}, "ListGlossaries": {"requestType": "ListGlossariesRequest", "responseType": "ListGlossariesResponse", "options": {"(google.api.http).get": "/v3/{parent=projects/*/locations/*}/glossaries", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{parent=projects/*/locations/*}/glossaries"}}, {"(google.api.method_signature)": "parent"}]}, "GetGlossary": {"requestType": "GetGlossaryRequest", "responseType": "Glossary", "options": {"(google.api.http).get": "/v3/{name=projects/*/locations/*/glossaries/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{name=projects/*/locations/*/glossaries/*}"}}, {"(google.api.method_signature)": "name"}]}, "DeleteGlossary": {"requestType": "DeleteGlossaryRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).delete": "/v3/{name=projects/*/locations/*/glossaries/*}", "(google.api.method_signature)": "name", "(google.longrunning.operation_info).response_type": "DeleteGlossaryResponse", "(google.longrunning.operation_info).metadata_type": "DeleteGlossaryMetadata"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v3/{name=projects/*/locations/*/glossaries/*}"}}, {"(google.api.method_signature)": "name"}, {"(google.longrunning.operation_info)": {"response_type": "DeleteGlossaryResponse", "metadata_type": "DeleteGlossaryMetadata"}}]}, "GetGlossaryEntry": {"requestType": "GetGlossaryEntryRequest", "responseType": "GlossaryEntry", "options": {"(google.api.http).get": "/v3/{name=projects/*/locations/*/glossaries/*/glossaryEntries/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{name=projects/*/locations/*/glossaries/*/glossaryEntries/*}"}}, {"(google.api.method_signature)": "name"}]}, "ListGlossaryEntries": {"requestType": "ListGlossaryEntriesRequest", "responseType": "ListGlossaryEntriesResponse", "options": {"(google.api.http).get": "/v3/{parent=projects/*/locations/*/glossaries/*}/glossaryEntries", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{parent=projects/*/locations/*/glossaries/*}/glossaryEntries"}}, {"(google.api.method_signature)": "parent"}]}, "CreateGlossaryEntry": {"requestType": "CreateGlossaryEntryRequest", "responseType": "GlossaryEntry", "options": {"(google.api.http).post": "/v3/{parent=projects/*/locations/*/glossaries/*}/glossaryEntries", "(google.api.http).body": "glossary_entry", "(google.api.method_signature)": "parent,glossary_entry"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{parent=projects/*/locations/*/glossaries/*}/glossaryEntries", "body": "glossary_entry"}}, {"(google.api.method_signature)": "parent,glossary_entry"}]}, "UpdateGlossaryEntry": {"requestType": "UpdateGlossaryEntryRequest", "responseType": "GlossaryEntry", "options": {"(google.api.http).patch": "/v3/{glossary_entry.name=projects/*/locations/*/glossaries/*/glossaryEntries/*}", "(google.api.http).body": "glossary_entry", "(google.api.method_signature)": "glossary_entry"}, "parsedOptions": [{"(google.api.http)": {"patch": "/v3/{glossary_entry.name=projects/*/locations/*/glossaries/*/glossaryEntries/*}", "body": "glossary_entry"}}, {"(google.api.method_signature)": "glossary_entry"}]}, "DeleteGlossaryEntry": {"requestType": "DeleteGlossaryEntryRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).delete": "/v3/{name=projects/*/locations/*/glossaries/*/glossaryEntries/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v3/{name=projects/*/locations/*/glossaries/*/glossaryEntries/*}"}}, {"(google.api.method_signature)": "name"}]}, "CreateDataset": {"requestType": "CreateDatasetRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v3/{parent=projects/*/locations/*}/datasets", "(google.api.http).body": "dataset", "(google.api.method_signature)": "parent,dataset", "(google.longrunning.operation_info).response_type": "Dataset", "(google.longrunning.operation_info).metadata_type": "CreateDatasetMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{parent=projects/*/locations/*}/datasets", "body": "dataset"}}, {"(google.api.method_signature)": "parent,dataset"}, {"(google.longrunning.operation_info)": {"response_type": "Dataset", "metadata_type": "CreateDatasetMetadata"}}]}, "GetDataset": {"requestType": "GetDatasetRequest", "responseType": "Dataset", "options": {"(google.api.http).get": "/v3/{name=projects/*/locations/*/datasets/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{name=projects/*/locations/*/datasets/*}"}}, {"(google.api.method_signature)": "name"}]}, "ListDatasets": {"requestType": "ListDatasetsRequest", "responseType": "ListDatasetsResponse", "options": {"(google.api.http).get": "/v3/{parent=projects/*/locations/*}/datasets", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{parent=projects/*/locations/*}/datasets"}}, {"(google.api.method_signature)": "parent"}]}, "DeleteDataset": {"requestType": "DeleteDatasetRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).delete": "/v3/{name=projects/*/locations/*/datasets/*}", "(google.api.method_signature)": "name", "(google.longrunning.operation_info).response_type": "google.protobuf.Empty", "(google.longrunning.operation_info).metadata_type": "DeleteDatasetMetadata"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v3/{name=projects/*/locations/*/datasets/*}"}}, {"(google.api.method_signature)": "name"}, {"(google.longrunning.operation_info)": {"response_type": "google.protobuf.Empty", "metadata_type": "DeleteDatasetMetadata"}}]}, "CreateAdaptiveMtDataset": {"requestType": "CreateAdaptiveMtDatasetRequest", "responseType": "AdaptiveMtDataset", "options": {"(google.api.http).post": "/v3/{parent=projects/*/locations/*}/adaptiveMtDatasets", "(google.api.http).body": "adaptive_mt_dataset", "(google.api.method_signature)": "parent,adaptive_mt_dataset"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{parent=projects/*/locations/*}/adaptiveMtDatasets", "body": "adaptive_mt_dataset"}}, {"(google.api.method_signature)": "parent,adaptive_mt_dataset"}]}, "DeleteAdaptiveMtDataset": {"requestType": "DeleteAdaptiveMtDatasetRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).delete": "/v3/{name=projects/*/locations/*/adaptiveMtDatasets/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v3/{name=projects/*/locations/*/adaptiveMtDatasets/*}"}}, {"(google.api.method_signature)": "name"}]}, "GetAdaptiveMtDataset": {"requestType": "GetAdaptiveMtDatasetRequest", "responseType": "AdaptiveMtDataset", "options": {"(google.api.http).get": "/v3/{name=projects/*/locations/*/adaptiveMtDatasets/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{name=projects/*/locations/*/adaptiveMtDatasets/*}"}}, {"(google.api.method_signature)": "name"}]}, "ListAdaptiveMtDatasets": {"requestType": "ListAdaptiveMtDatasetsRequest", "responseType": "ListAdaptiveMtDatasetsResponse", "options": {"(google.api.http).get": "/v3/{parent=projects/*/locations/*}/adaptiveMtDatasets", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{parent=projects/*/locations/*}/adaptiveMtDatasets"}}, {"(google.api.method_signature)": "parent"}]}, "AdaptiveMtTranslate": {"requestType": "AdaptiveMtTranslateRequest", "responseType": "AdaptiveMtTranslateResponse", "options": {"(google.api.http).post": "/v3/{parent=projects/*/locations/*}:adaptiveMtTranslate", "(google.api.http).body": "*", "(google.api.method_signature)": "parent,content"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{parent=projects/*/locations/*}:adaptiveMtTranslate", "body": "*"}}, {"(google.api.method_signature)": "parent,content"}]}, "GetAdaptiveMtFile": {"requestType": "GetAdaptiveMtFileRequest", "responseType": "AdaptiveMtFile", "options": {"(google.api.http).get": "/v3/{name=projects/*/locations/*/adaptiveMtDatasets/*/adaptiveMtFiles/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{name=projects/*/locations/*/adaptiveMtDatasets/*/adaptiveMtFiles/*}"}}, {"(google.api.method_signature)": "name"}]}, "DeleteAdaptiveMtFile": {"requestType": "DeleteAdaptiveMtFileRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).delete": "/v3/{name=projects/*/locations/*/adaptiveMtDatasets/*/adaptiveMtFiles/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v3/{name=projects/*/locations/*/adaptiveMtDatasets/*/adaptiveMtFiles/*}"}}, {"(google.api.method_signature)": "name"}]}, "ImportAdaptiveMtFile": {"requestType": "ImportAdaptiveMtFileRequest", "responseType": "ImportAdaptiveMtFileResponse", "options": {"(google.api.http).post": "/v3/{parent=projects/*/locations/*/adaptiveMtDatasets/*}:importAdaptiveMtFile", "(google.api.http).body": "*", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{parent=projects/*/locations/*/adaptiveMtDatasets/*}:importAdaptiveMtFile", "body": "*"}}, {"(google.api.method_signature)": "parent"}]}, "ListAdaptiveMtFiles": {"requestType": "ListAdaptiveMtFilesRequest", "responseType": "ListAdaptiveMtFilesResponse", "options": {"(google.api.http).get": "/v3/{parent=projects/*/locations/*/adaptiveMtDatasets/*}/adaptiveMtFiles", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{parent=projects/*/locations/*/adaptiveMtDatasets/*}/adaptiveMtFiles"}}, {"(google.api.method_signature)": "parent"}]}, "ListAdaptiveMtSentences": {"requestType": "ListAdaptiveMtSentencesRequest", "responseType": "ListAdaptiveMtSentencesResponse", "options": {"(google.api.http).get": "/v3/{parent=projects/*/locations/*/adaptiveMtDatasets/*/adaptiveMtFiles/*}/adaptiveMtSentences", "(google.api.http).additional_bindings.get": "/v3/{parent=projects/*/locations/*/adaptiveMtDatasets/*}/adaptiveMtSentences", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{parent=projects/*/locations/*/adaptiveMtDatasets/*/adaptiveMtFiles/*}/adaptiveMtSentences", "additional_bindings": {"get": "/v3/{parent=projects/*/locations/*/adaptiveMtDatasets/*}/adaptiveMtSentences"}}}, {"(google.api.method_signature)": "parent"}]}, "ImportData": {"requestType": "ImportDataRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v3/{dataset=projects/*/locations/*/datasets/*}:importData", "(google.api.http).body": "*", "(google.api.method_signature)": "dataset,input_config", "(google.longrunning.operation_info).response_type": "google.protobuf.Empty", "(google.longrunning.operation_info).metadata_type": "ImportDataMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{dataset=projects/*/locations/*/datasets/*}:importData", "body": "*"}}, {"(google.api.method_signature)": "dataset,input_config"}, {"(google.longrunning.operation_info)": {"response_type": "google.protobuf.Empty", "metadata_type": "ImportDataMetadata"}}]}, "ExportData": {"requestType": "ExportDataRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v3/{dataset=projects/*/locations/*/datasets/*}:exportData", "(google.api.http).body": "*", "(google.api.method_signature)": "dataset,output_config", "(google.longrunning.operation_info).response_type": "google.protobuf.Empty", "(google.longrunning.operation_info).metadata_type": "ExportDataMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{dataset=projects/*/locations/*/datasets/*}:exportData", "body": "*"}}, {"(google.api.method_signature)": "dataset,output_config"}, {"(google.longrunning.operation_info)": {"response_type": "google.protobuf.Empty", "metadata_type": "ExportDataMetadata"}}]}, "ListExamples": {"requestType": "ListExamplesRequest", "responseType": "ListExamplesResponse", "options": {"(google.api.http).get": "/v3/{parent=projects/*/locations/*/datasets/*}/examples", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{parent=projects/*/locations/*/datasets/*}/examples"}}, {"(google.api.method_signature)": "parent"}]}, "CreateModel": {"requestType": "CreateModelRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v3/{parent=projects/*/locations/*}/models", "(google.api.http).body": "model", "(google.api.method_signature)": "parent,model", "(google.longrunning.operation_info).response_type": "Model", "(google.longrunning.operation_info).metadata_type": "CreateModelMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3/{parent=projects/*/locations/*}/models", "body": "model"}}, {"(google.api.method_signature)": "parent,model"}, {"(google.longrunning.operation_info)": {"response_type": "Model", "metadata_type": "CreateModelMetadata"}}]}, "ListModels": {"requestType": "ListModelsRequest", "responseType": "ListModelsResponse", "options": {"(google.api.http).get": "/v3/{parent=projects/*/locations/*}/models", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{parent=projects/*/locations/*}/models"}}, {"(google.api.method_signature)": "parent"}]}, "GetModel": {"requestType": "GetModelRequest", "responseType": "Model", "options": {"(google.api.http).get": "/v3/{name=projects/*/locations/*/models/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3/{name=projects/*/locations/*/models/*}"}}, {"(google.api.method_signature)": "name"}]}, "DeleteModel": {"requestType": "DeleteModelRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).delete": "/v3/{name=projects/*/locations/*/models/*}", "(google.api.method_signature)": "name", "(google.longrunning.operation_info).response_type": "google.protobuf.Empty", "(google.longrunning.operation_info).metadata_type": "DeleteModelMetadata"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v3/{name=projects/*/locations/*/models/*}"}}, {"(google.api.method_signature)": "name"}, {"(google.longrunning.operation_info)": {"response_type": "google.protobuf.Empty", "metadata_type": "DeleteModelMetadata"}}]}}}, "TransliterationConfig": {"fields": {"enableTransliteration": {"type": "bool", "id": 1}}}, "TranslateTextRequest": {"fields": {"contents": {"rule": "repeated", "type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "mimeType": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "sourceLanguageCode": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "targetLanguageCode": {"type": "string", "id": 5, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "parent": {"type": "string", "id": 8, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "model": {"type": "string", "id": 6, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "glossaryConfig": {"type": "TranslateTextGlossaryConfig", "id": 7, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "transliterationConfig": {"type": "TransliterationConfig", "id": 13, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "labels": {"keyType": "string", "type": "string", "id": 10, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "TranslateTextResponse": {"fields": {"translations": {"rule": "repeated", "type": "Translation", "id": 1}, "glossaryTranslations": {"rule": "repeated", "type": "Translation", "id": 3}}}, "Translation": {"fields": {"translatedText": {"type": "string", "id": 1}, "model": {"type": "string", "id": 2}, "detectedLanguageCode": {"type": "string", "id": 4}, "glossaryConfig": {"type": "TranslateTextGlossaryConfig", "id": 3}}}, "RomanizeTextRequest": {"fields": {"parent": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "contents": {"rule": "repeated", "type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "sourceLanguageCode": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "Romanization": {"fields": {"romanizedText": {"type": "string", "id": 1}, "detectedLanguageCode": {"type": "string", "id": 2}}}, "RomanizeTextResponse": {"fields": {"romanizations": {"rule": "repeated", "type": "Romanization", "id": 1}}}, "DetectLanguageRequest": {"oneofs": {"source": {"oneof": ["content"]}}, "fields": {"parent": {"type": "string", "id": 5, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "model": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "content": {"type": "string", "id": 1}, "mimeType": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "labels": {"keyType": "string", "type": "string", "id": 6, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "DetectedLanguage": {"fields": {"languageCode": {"type": "string", "id": 1}, "confidence": {"type": "float", "id": 2}}}, "DetectLanguageResponse": {"fields": {"languages": {"rule": "repeated", "type": "DetectedLanguage", "id": 1}}}, "GetSupportedLanguagesRequest": {"fields": {"parent": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "displayLanguageCode": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "model": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "SupportedLanguages": {"fields": {"languages": {"rule": "repeated", "type": "SupportedLanguage", "id": 1}}}, "SupportedLanguage": {"fields": {"languageCode": {"type": "string", "id": 1}, "displayName": {"type": "string", "id": 2}, "supportSource": {"type": "bool", "id": 3}, "supportTarget": {"type": "bool", "id": 4}}}, "GcsSource": {"fields": {"inputUri": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "InputConfig": {"oneofs": {"source": {"oneof": ["gcsSource"]}}, "fields": {"mimeType": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "gcsSource": {"type": "GcsSource", "id": 2}}}, "GcsDestination": {"fields": {"outputUriPrefix": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "OutputConfig": {"oneofs": {"destination": {"oneof": ["gcsDestination"]}}, "fields": {"gcsDestination": {"type": "GcsDestination", "id": 1}}}, "DocumentInputConfig": {"oneofs": {"source": {"oneof": ["content", "gcsSource"]}}, "fields": {"content": {"type": "bytes", "id": 1}, "gcsSource": {"type": "GcsSource", "id": 2}, "mimeType": {"type": "string", "id": 4}}}, "DocumentOutputConfig": {"oneofs": {"destination": {"oneof": ["gcsDestination"]}}, "fields": {"gcsDestination": {"type": "GcsDestination", "id": 1, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "mimeType": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "TranslateDocumentRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "sourceLanguageCode": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "targetLanguageCode": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "documentInputConfig": {"type": "DocumentInputConfig", "id": 4, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "documentOutputConfig": {"type": "DocumentOutputConfig", "id": 5, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "model": {"type": "string", "id": 6, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "glossaryConfig": {"type": "TranslateTextGlossaryConfig", "id": 7, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "labels": {"keyType": "string", "type": "string", "id": 8, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "customizedAttribution": {"type": "string", "id": 10, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "isTranslateNativePdfOnly": {"type": "bool", "id": 11, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "enableShadowRemovalNativePdf": {"type": "bool", "id": 12, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "enableRotationCorrection": {"type": "bool", "id": 13, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "DocumentTranslation": {"fields": {"byteStreamOutputs": {"rule": "repeated", "type": "bytes", "id": 1}, "mimeType": {"type": "string", "id": 2}, "detectedLanguageCode": {"type": "string", "id": 3}}}, "TranslateDocumentResponse": {"fields": {"documentTranslation": {"type": "DocumentTranslation", "id": 1}, "glossaryDocumentTranslation": {"type": "DocumentTranslation", "id": 2}, "model": {"type": "string", "id": 3}, "glossaryConfig": {"type": "TranslateTextGlossaryConfig", "id": 4}}}, "BatchTranslateTextRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "sourceLanguageCode": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "targetLanguageCodes": {"rule": "repeated", "type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "models": {"keyType": "string", "type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "inputConfigs": {"rule": "repeated", "type": "InputConfig", "id": 5, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "outputConfig": {"type": "OutputConfig", "id": 6, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "glossaries": {"keyType": "string", "type": "TranslateTextGlossaryConfig", "id": 7, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "labels": {"keyType": "string", "type": "string", "id": 9, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "BatchTranslateMetadata": {"fields": {"state": {"type": "State", "id": 1}, "translatedCharacters": {"type": "int64", "id": 2}, "failedCharacters": {"type": "int64", "id": 3}, "totalCharacters": {"type": "int64", "id": 4}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 5}}, "nested": {"State": {"values": {"STATE_UNSPECIFIED": 0, "RUNNING": 1, "SUCCEEDED": 2, "FAILED": 3, "CANCELLING": 4, "CANCELLED": 5}}}}, "BatchTranslateResponse": {"fields": {"totalCharacters": {"type": "int64", "id": 1}, "translatedCharacters": {"type": "int64", "id": 2}, "failedCharacters": {"type": "int64", "id": 3}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 4}, "endTime": {"type": "google.protobuf.Timestamp", "id": 5}}}, "GlossaryInputConfig": {"oneofs": {"source": {"oneof": ["gcsSource"]}}, "fields": {"gcsSource": {"type": "GcsSource", "id": 1}}}, "Glossary": {"options": {"(google.api.resource).type": "translate.googleapis.com/Glossary", "(google.api.resource).pattern": "projects/{project}/locations/{location}/glossaries/{glossary}"}, "oneofs": {"languages": {"oneof": ["languagePair", "languageCodesSet"]}}, "fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "languagePair": {"type": "LanguageCodePair", "id": 3}, "languageCodesSet": {"type": "LanguageCodesSet", "id": 4}, "inputConfig": {"type": "GlossaryInputConfig", "id": 5}, "entryCount": {"type": "int32", "id": 6, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 7, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "endTime": {"type": "google.protobuf.Timestamp", "id": 8, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "displayName": {"type": "string", "id": 9, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}, "nested": {"LanguageCodePair": {"fields": {"sourceLanguageCode": {"type": "string", "id": 1}, "targetLanguageCode": {"type": "string", "id": 2}}}, "LanguageCodesSet": {"fields": {"languageCodes": {"rule": "repeated", "type": "string", "id": 1}}}}}, "CreateGlossaryRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "glossary": {"type": "Glossary", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "UpdateGlossaryRequest": {"fields": {"glossary": {"type": "Glossary", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "updateMask": {"type": "google.protobuf.FieldMask", "id": 2}}}, "GetGlossaryRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/Glossary"}}}}, "DeleteGlossaryRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/Glossary"}}}}, "ListGlossariesRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "pageSize": {"type": "int32", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "pageToken": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "filter": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "ListGlossariesResponse": {"fields": {"glossaries": {"rule": "repeated", "type": "Glossary", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "GetGlossaryEntryRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/GlossaryEntry"}}}}, "DeleteGlossaryEntryRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/GlossaryEntry"}}}}, "ListGlossaryEntriesRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/Glossary"}}, "pageSize": {"type": "int32", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "pageToken": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "ListGlossaryEntriesResponse": {"fields": {"glossaryEntries": {"rule": "repeated", "type": "GlossaryEntry", "id": 1, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "nextPageToken": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "CreateGlossaryEntryRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/Glossary"}}, "glossaryEntry": {"type": "GlossaryEntry", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "UpdateGlossaryEntryRequest": {"fields": {"glossaryEntry": {"type": "GlossaryEntry", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "CreateGlossaryMetadata": {"fields": {"name": {"type": "string", "id": 1}, "state": {"type": "State", "id": 2}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 3}}, "nested": {"State": {"values": {"STATE_UNSPECIFIED": 0, "RUNNING": 1, "SUCCEEDED": 2, "FAILED": 3, "CANCELLING": 4, "CANCELLED": 5}}}}, "UpdateGlossaryMetadata": {"fields": {"glossary": {"type": "Glossary", "id": 1}, "state": {"type": "State", "id": 2}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 3}}, "nested": {"State": {"values": {"STATE_UNSPECIFIED": 0, "RUNNING": 1, "SUCCEEDED": 2, "FAILED": 3, "CANCELLING": 4, "CANCELLED": 5}}}}, "DeleteGlossaryMetadata": {"fields": {"name": {"type": "string", "id": 1}, "state": {"type": "State", "id": 2}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 3}}, "nested": {"State": {"values": {"STATE_UNSPECIFIED": 0, "RUNNING": 1, "SUCCEEDED": 2, "FAILED": 3, "CANCELLING": 4, "CANCELLED": 5}}}}, "DeleteGlossaryResponse": {"fields": {"name": {"type": "string", "id": 1}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 2}, "endTime": {"type": "google.protobuf.Timestamp", "id": 3}}}, "BatchTranslateDocumentRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "sourceLanguageCode": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "targetLanguageCodes": {"rule": "repeated", "type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "inputConfigs": {"rule": "repeated", "type": "BatchDocumentInputConfig", "id": 4, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "outputConfig": {"type": "BatchDocumentOutputConfig", "id": 5, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "models": {"keyType": "string", "type": "string", "id": 6, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "glossaries": {"keyType": "string", "type": "TranslateTextGlossaryConfig", "id": 7, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "formatConversions": {"keyType": "string", "type": "string", "id": 8, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "customizedAttribution": {"type": "string", "id": 10, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "enableShadowRemovalNativePdf": {"type": "bool", "id": 11, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "enableRotationCorrection": {"type": "bool", "id": 12, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "BatchDocumentInputConfig": {"oneofs": {"source": {"oneof": ["gcsSource"]}}, "fields": {"gcsSource": {"type": "GcsSource", "id": 1}}}, "BatchDocumentOutputConfig": {"oneofs": {"destination": {"oneof": ["gcsDestination"]}}, "fields": {"gcsDestination": {"type": "GcsDestination", "id": 1}}}, "BatchTranslateDocumentResponse": {"fields": {"totalPages": {"type": "int64", "id": 1}, "translatedPages": {"type": "int64", "id": 2}, "failedPages": {"type": "int64", "id": 3}, "totalBillablePages": {"type": "int64", "id": 4}, "totalCharacters": {"type": "int64", "id": 5}, "translatedCharacters": {"type": "int64", "id": 6}, "failedCharacters": {"type": "int64", "id": 7}, "totalBillableCharacters": {"type": "int64", "id": 8}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 9}, "endTime": {"type": "google.protobuf.Timestamp", "id": 10}}}, "BatchTranslateDocumentMetadata": {"fields": {"state": {"type": "State", "id": 1}, "totalPages": {"type": "int64", "id": 2}, "translatedPages": {"type": "int64", "id": 3}, "failedPages": {"type": "int64", "id": 4}, "totalBillablePages": {"type": "int64", "id": 5}, "totalCharacters": {"type": "int64", "id": 6}, "translatedCharacters": {"type": "int64", "id": 7}, "failedCharacters": {"type": "int64", "id": 8}, "totalBillableCharacters": {"type": "int64", "id": 9}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 10}}, "nested": {"State": {"values": {"STATE_UNSPECIFIED": 0, "RUNNING": 1, "SUCCEEDED": 2, "FAILED": 3, "CANCELLING": 4, "CANCELLED": 5}}}}, "TranslateTextGlossaryConfig": {"fields": {"glossary": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/Glossary"}}, "ignoreCase": {"type": "bool", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "contextualTranslationEnabled": {"type": "bool", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}}}, "v3beta1": {"options": {"csharp_namespace": "Google.Cloud.Translate.V3Beta1", "go_package": "cloud.google.com/go/translation/apiv3beta1/translationpb;translationpb", "java_multiple_files": true, "java_outer_classname": "TranslationServiceProto", "java_package": "com.google.cloud.translate.v3beta1", "php_namespace": "Google\\Cloud\\Translate\\V3beta1", "ruby_package": "Google::Cloud::Translate::V3beta1"}, "nested": {"TranslationService": {"options": {"(google.api.default_host)": "translate.googleapis.com", "(google.api.oauth_scopes)": "https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/cloud-translation"}, "methods": {"TranslateText": {"requestType": "TranslateTextRequest", "responseType": "TranslateTextResponse", "options": {"(google.api.http).post": "/v3beta1/{parent=projects/*/locations/*}:translateText", "(google.api.http).body": "*", "(google.api.http).additional_bindings.post": "/v3beta1/{parent=projects/*}:translateText", "(google.api.http).additional_bindings.body": "*"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3beta1/{parent=projects/*/locations/*}:translateText", "body": "*", "additional_bindings": {"post": "/v3beta1/{parent=projects/*}:translateText", "body": "*"}}}]}, "DetectLanguage": {"requestType": "DetectLanguageRequest", "responseType": "DetectLanguageResponse", "options": {"(google.api.http).post": "/v3beta1/{parent=projects/*/locations/*}:detectLanguage", "(google.api.http).body": "*", "(google.api.http).additional_bindings.post": "/v3beta1/{parent=projects/*}:detectLanguage", "(google.api.http).additional_bindings.body": "*", "(google.api.method_signature)": "parent,model,mime_type"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3beta1/{parent=projects/*/locations/*}:detectLanguage", "body": "*", "additional_bindings": {"post": "/v3beta1/{parent=projects/*}:detectLanguage", "body": "*"}}}, {"(google.api.method_signature)": "parent,model,mime_type"}]}, "GetSupportedLanguages": {"requestType": "GetSupportedLanguagesRequest", "responseType": "SupportedLanguages", "options": {"(google.api.http).get": "/v3beta1/{parent=projects/*/locations/*}/supportedLanguages", "(google.api.http).additional_bindings.get": "/v3beta1/{parent=projects/*}/supportedLanguages", "(google.api.method_signature)": "parent,display_language_code,model"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3beta1/{parent=projects/*/locations/*}/supportedLanguages", "additional_bindings": {"get": "/v3beta1/{parent=projects/*}/supportedLanguages"}}}, {"(google.api.method_signature)": "parent,display_language_code,model"}]}, "TranslateDocument": {"requestType": "TranslateDocumentRequest", "responseType": "TranslateDocumentResponse", "options": {"(google.api.http).post": "/v3beta1/{parent=projects/*/locations/*}:translateDocument", "(google.api.http).body": "*"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3beta1/{parent=projects/*/locations/*}:translateDocument", "body": "*"}}]}, "BatchTranslateText": {"requestType": "BatchTranslateTextRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v3beta1/{parent=projects/*/locations/*}:batchTranslateText", "(google.api.http).body": "*", "(google.longrunning.operation_info).response_type": "BatchTranslateResponse", "(google.longrunning.operation_info).metadata_type": "BatchTranslateMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3beta1/{parent=projects/*/locations/*}:batchTranslateText", "body": "*"}}, {"(google.longrunning.operation_info)": {"response_type": "BatchTranslateResponse", "metadata_type": "BatchTranslateMetadata"}}]}, "BatchTranslateDocument": {"requestType": "BatchTranslateDocumentRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v3beta1/{parent=projects/*/locations/*}:batchTranslateDocument", "(google.api.http).body": "*", "(google.api.method_signature)": "parent,source_language_code,target_language_codes,input_configs,output_config", "(google.longrunning.operation_info).response_type": "BatchTranslateDocumentResponse", "(google.longrunning.operation_info).metadata_type": "BatchTranslateDocumentMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3beta1/{parent=projects/*/locations/*}:batchTranslateDocument", "body": "*"}}, {"(google.api.method_signature)": "parent,source_language_code,target_language_codes,input_configs,output_config"}, {"(google.longrunning.operation_info)": {"response_type": "BatchTranslateDocumentResponse", "metadata_type": "BatchTranslateDocumentMetadata"}}]}, "CreateGlossary": {"requestType": "CreateGlossaryRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v3beta1/{parent=projects/*/locations/*}/glossaries", "(google.api.http).body": "glossary", "(google.api.method_signature)": "parent,glossary", "(google.longrunning.operation_info).response_type": "Glossary", "(google.longrunning.operation_info).metadata_type": "CreateGlossaryMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v3beta1/{parent=projects/*/locations/*}/glossaries", "body": "glossary"}}, {"(google.api.method_signature)": "parent,glossary"}, {"(google.longrunning.operation_info)": {"response_type": "Glossary", "metadata_type": "CreateGlossaryMetadata"}}]}, "ListGlossaries": {"requestType": "ListGlossariesRequest", "responseType": "ListGlossariesResponse", "options": {"(google.api.http).get": "/v3beta1/{parent=projects/*/locations/*}/glossaries", "(google.api.method_signature)": "parent,filter"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3beta1/{parent=projects/*/locations/*}/glossaries"}}, {"(google.api.method_signature)": "parent,filter"}]}, "GetGlossary": {"requestType": "GetGlossaryRequest", "responseType": "Glossary", "options": {"(google.api.http).get": "/v3beta1/{name=projects/*/locations/*/glossaries/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v3beta1/{name=projects/*/locations/*/glossaries/*}"}}, {"(google.api.method_signature)": "name"}]}, "DeleteGlossary": {"requestType": "DeleteGlossaryRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).delete": "/v3beta1/{name=projects/*/locations/*/glossaries/*}", "(google.api.method_signature)": "name", "(google.longrunning.operation_info).response_type": "DeleteGlossaryResponse", "(google.longrunning.operation_info).metadata_type": "DeleteGlossaryMetadata"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v3beta1/{name=projects/*/locations/*/glossaries/*}"}}, {"(google.api.method_signature)": "name"}, {"(google.longrunning.operation_info)": {"response_type": "DeleteGlossaryResponse", "metadata_type": "DeleteGlossaryMetadata"}}]}}}, "TranslateTextGlossaryConfig": {"fields": {"glossary": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "ignoreCase": {"type": "bool", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "TranslateTextRequest": {"fields": {"contents": {"rule": "repeated", "type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "mimeType": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "sourceLanguageCode": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "targetLanguageCode": {"type": "string", "id": 5, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "parent": {"type": "string", "id": 8, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "model": {"type": "string", "id": 6, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "glossaryConfig": {"type": "TranslateTextGlossaryConfig", "id": 7, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "labels": {"keyType": "string", "type": "string", "id": 10, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "TranslateTextResponse": {"fields": {"translations": {"rule": "repeated", "type": "Translation", "id": 1}, "glossaryTranslations": {"rule": "repeated", "type": "Translation", "id": 3}}}, "Translation": {"fields": {"translatedText": {"type": "string", "id": 1}, "model": {"type": "string", "id": 2}, "detectedLanguageCode": {"type": "string", "id": 4}, "glossaryConfig": {"type": "TranslateTextGlossaryConfig", "id": 3}}}, "DetectLanguageRequest": {"oneofs": {"source": {"oneof": ["content"]}}, "fields": {"parent": {"type": "string", "id": 5, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "model": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "content": {"type": "string", "id": 1}, "mimeType": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "labels": {"keyType": "string", "type": "string", "id": 6, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "DetectedLanguage": {"fields": {"languageCode": {"type": "string", "id": 1}, "confidence": {"type": "float", "id": 2}}}, "DetectLanguageResponse": {"fields": {"languages": {"rule": "repeated", "type": "DetectedLanguage", "id": 1}}}, "GetSupportedLanguagesRequest": {"fields": {"parent": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "displayLanguageCode": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "model": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "SupportedLanguages": {"fields": {"languages": {"rule": "repeated", "type": "SupportedLanguage", "id": 1}}}, "SupportedLanguage": {"fields": {"languageCode": {"type": "string", "id": 1}, "displayName": {"type": "string", "id": 2}, "supportSource": {"type": "bool", "id": 3}, "supportTarget": {"type": "bool", "id": 4}}}, "GcsSource": {"fields": {"inputUri": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "InputConfig": {"oneofs": {"source": {"oneof": ["gcsSource"]}}, "fields": {"mimeType": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "gcsSource": {"type": "GcsSource", "id": 2}}}, "GcsDestination": {"fields": {"outputUriPrefix": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "OutputConfig": {"oneofs": {"destination": {"oneof": ["gcsDestination"]}}, "fields": {"gcsDestination": {"type": "GcsDestination", "id": 1}}}, "DocumentInputConfig": {"oneofs": {"source": {"oneof": ["content", "gcsSource"]}}, "fields": {"content": {"type": "bytes", "id": 1}, "gcsSource": {"type": "GcsSource", "id": 2}, "mimeType": {"type": "string", "id": 4}}}, "DocumentOutputConfig": {"oneofs": {"destination": {"oneof": ["gcsDestination"]}}, "fields": {"gcsDestination": {"type": "GcsDestination", "id": 1, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "mimeType": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "TranslateDocumentRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "sourceLanguageCode": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "targetLanguageCode": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "documentInputConfig": {"type": "DocumentInputConfig", "id": 4, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "documentOutputConfig": {"type": "DocumentOutputConfig", "id": 5, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "model": {"type": "string", "id": 6, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "glossaryConfig": {"type": "TranslateTextGlossaryConfig", "id": 7, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "labels": {"keyType": "string", "type": "string", "id": 8, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "customizedAttribution": {"type": "string", "id": 10, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "isTranslateNativePdfOnly": {"type": "bool", "id": 11, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "enableShadowRemovalNativePdf": {"type": "bool", "id": 12, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "enableRotationCorrection": {"type": "bool", "id": 13, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "DocumentTranslation": {"fields": {"byteStreamOutputs": {"rule": "repeated", "type": "bytes", "id": 1}, "mimeType": {"type": "string", "id": 2}, "detectedLanguageCode": {"type": "string", "id": 3}}}, "TranslateDocumentResponse": {"fields": {"documentTranslation": {"type": "DocumentTranslation", "id": 1}, "glossaryDocumentTranslation": {"type": "DocumentTranslation", "id": 2}, "model": {"type": "string", "id": 3}, "glossaryConfig": {"type": "TranslateTextGlossaryConfig", "id": 4}}}, "BatchTranslateTextRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "sourceLanguageCode": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "targetLanguageCodes": {"rule": "repeated", "type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "models": {"keyType": "string", "type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "inputConfigs": {"rule": "repeated", "type": "InputConfig", "id": 5, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "outputConfig": {"type": "OutputConfig", "id": 6, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "glossaries": {"keyType": "string", "type": "TranslateTextGlossaryConfig", "id": 7, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "labels": {"keyType": "string", "type": "string", "id": 9, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "BatchTranslateMetadata": {"fields": {"state": {"type": "State", "id": 1}, "translatedCharacters": {"type": "int64", "id": 2}, "failedCharacters": {"type": "int64", "id": 3}, "totalCharacters": {"type": "int64", "id": 4}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 5}}, "nested": {"State": {"values": {"STATE_UNSPECIFIED": 0, "RUNNING": 1, "SUCCEEDED": 2, "FAILED": 3, "CANCELLING": 4, "CANCELLED": 5}}}}, "BatchTranslateResponse": {"fields": {"totalCharacters": {"type": "int64", "id": 1}, "translatedCharacters": {"type": "int64", "id": 2}, "failedCharacters": {"type": "int64", "id": 3}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 4}, "endTime": {"type": "google.protobuf.Timestamp", "id": 5}}}, "GlossaryInputConfig": {"oneofs": {"source": {"oneof": ["gcsSource"]}}, "fields": {"gcsSource": {"type": "GcsSource", "id": 1}}}, "Glossary": {"options": {"(google.api.resource).type": "translate.googleapis.com/Glossary", "(google.api.resource).pattern": "projects/{project}/locations/{location}/glossaries/{glossary}"}, "oneofs": {"languages": {"oneof": ["languagePair", "languageCodesSet"]}}, "fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "languagePair": {"type": "LanguageCodePair", "id": 3}, "languageCodesSet": {"type": "LanguageCodesSet", "id": 4}, "inputConfig": {"type": "GlossaryInputConfig", "id": 5}, "entryCount": {"type": "int32", "id": 6, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 7, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "endTime": {"type": "google.protobuf.Timestamp", "id": 8, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}, "nested": {"LanguageCodePair": {"fields": {"sourceLanguageCode": {"type": "string", "id": 1}, "targetLanguageCode": {"type": "string", "id": 2}}}, "LanguageCodesSet": {"fields": {"languageCodes": {"rule": "repeated", "type": "string", "id": 1}}}}}, "CreateGlossaryRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "glossary": {"type": "Glossary", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "GetGlossaryRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/Glossary"}}}}, "DeleteGlossaryRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "translate.googleapis.com/Glossary"}}}}, "ListGlossariesRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "pageSize": {"type": "int32", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "pageToken": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "filter": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "ListGlossariesResponse": {"fields": {"glossaries": {"rule": "repeated", "type": "Glossary", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "CreateGlossaryMetadata": {"fields": {"name": {"type": "string", "id": 1}, "state": {"type": "State", "id": 2}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 3}}, "nested": {"State": {"values": {"STATE_UNSPECIFIED": 0, "RUNNING": 1, "SUCCEEDED": 2, "FAILED": 3, "CANCELLING": 4, "CANCELLED": 5}}}}, "DeleteGlossaryMetadata": {"fields": {"name": {"type": "string", "id": 1}, "state": {"type": "State", "id": 2}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 3}}, "nested": {"State": {"values": {"STATE_UNSPECIFIED": 0, "RUNNING": 1, "SUCCEEDED": 2, "FAILED": 3, "CANCELLING": 4, "CANCELLED": 5}}}}, "DeleteGlossaryResponse": {"fields": {"name": {"type": "string", "id": 1}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 2}, "endTime": {"type": "google.protobuf.Timestamp", "id": 3}}}, "BatchTranslateDocumentRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "sourceLanguageCode": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "targetLanguageCodes": {"rule": "repeated", "type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "inputConfigs": {"rule": "repeated", "type": "BatchDocumentInputConfig", "id": 4, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "outputConfig": {"type": "BatchDocumentOutputConfig", "id": 5, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "models": {"keyType": "string", "type": "string", "id": 6, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "glossaries": {"keyType": "string", "type": "TranslateTextGlossaryConfig", "id": 7, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "formatConversions": {"keyType": "string", "type": "string", "id": 8, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "customizedAttribution": {"type": "string", "id": 10, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "enableShadowRemovalNativePdf": {"type": "bool", "id": 11, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "enableRotationCorrection": {"type": "bool", "id": 12, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "BatchDocumentInputConfig": {"oneofs": {"source": {"oneof": ["gcsSource"]}}, "fields": {"gcsSource": {"type": "GcsSource", "id": 1}}}, "BatchDocumentOutputConfig": {"oneofs": {"destination": {"oneof": ["gcsDestination"]}}, "fields": {"gcsDestination": {"type": "GcsDestination", "id": 1}}}, "BatchTranslateDocumentResponse": {"fields": {"totalPages": {"type": "int64", "id": 1}, "translatedPages": {"type": "int64", "id": 2}, "failedPages": {"type": "int64", "id": 3}, "totalBillablePages": {"type": "int64", "id": 4}, "totalCharacters": {"type": "int64", "id": 5}, "translatedCharacters": {"type": "int64", "id": 6}, "failedCharacters": {"type": "int64", "id": 7}, "totalBillableCharacters": {"type": "int64", "id": 8}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 9}, "endTime": {"type": "google.protobuf.Timestamp", "id": 10}}}, "BatchTranslateDocumentMetadata": {"fields": {"state": {"type": "State", "id": 1}, "totalPages": {"type": "int64", "id": 2}, "translatedPages": {"type": "int64", "id": 3}, "failedPages": {"type": "int64", "id": 4}, "totalBillablePages": {"type": "int64", "id": 5}, "totalCharacters": {"type": "int64", "id": 6}, "translatedCharacters": {"type": "int64", "id": 7}, "failedCharacters": {"type": "int64", "id": 8}, "totalBillableCharacters": {"type": "int64", "id": 9}, "submitTime": {"type": "google.protobuf.Timestamp", "id": 10}}, "nested": {"State": {"values": {"STATE_UNSPECIFIED": 0, "RUNNING": 1, "SUCCEEDED": 2, "FAILED": 3, "CANCELLING": 4, "CANCELLED": 5}}}}}}}}}}, "api": {"options": {"go_package": "google.golang.org/genproto/googleapis/api;api", "java_multiple_files": true, "java_outer_classname": "LaunchStageProto", "java_package": "com.google.api", "objc_class_prefix": "GAPI", "cc_enable_arenas": true}, "nested": {"fieldBehavior": {"rule": "repeated", "type": "google.api.FieldBehavior", "id": 1052, "extend": "google.protobuf.FieldOptions", "options": {"packed": false}}, "FieldBehavior": {"values": {"FIELD_BEHAVIOR_UNSPECIFIED": 0, "OPTIONAL": 1, "REQUIRED": 2, "OUTPUT_ONLY": 3, "INPUT_ONLY": 4, "IMMUTABLE": 5, "UNORDERED_LIST": 6, "NON_EMPTY_DEFAULT": 7, "IDENTIFIER": 8}}, "resourceReference": {"type": "google.api.ResourceReference", "id": 1055, "extend": "google.protobuf.FieldOptions"}, "resourceDefinition": {"rule": "repeated", "type": "google.api.ResourceDescriptor", "id": 1053, "extend": "google.protobuf.FileOptions"}, "resource": {"type": "google.api.ResourceDescriptor", "id": 1053, "extend": "google.protobuf.MessageOptions"}, "ResourceDescriptor": {"fields": {"type": {"type": "string", "id": 1}, "pattern": {"rule": "repeated", "type": "string", "id": 2}, "nameField": {"type": "string", "id": 3}, "history": {"type": "History", "id": 4}, "plural": {"type": "string", "id": 5}, "singular": {"type": "string", "id": 6}, "style": {"rule": "repeated", "type": "Style", "id": 10}}, "nested": {"History": {"values": {"HISTORY_UNSPECIFIED": 0, "ORIGINALLY_SINGLE_PATTERN": 1, "FUTURE_MULTI_PATTERN": 2}}, "Style": {"values": {"STYLE_UNSPECIFIED": 0, "DECLARATIVE_FRIENDLY": 1}}}}, "ResourceReference": {"fields": {"type": {"type": "string", "id": 1}, "childType": {"type": "string", "id": 2}}}, "http": {"type": "HttpRule", "id": 72295728, "extend": "google.protobuf.MethodOptions"}, "Http": {"fields": {"rules": {"rule": "repeated", "type": "HttpRule", "id": 1}, "fullyDecodeReservedExpansion": {"type": "bool", "id": 2}}}, "HttpRule": {"oneofs": {"pattern": {"oneof": ["get", "put", "post", "delete", "patch", "custom"]}}, "fields": {"selector": {"type": "string", "id": 1}, "get": {"type": "string", "id": 2}, "put": {"type": "string", "id": 3}, "post": {"type": "string", "id": 4}, "delete": {"type": "string", "id": 5}, "patch": {"type": "string", "id": 6}, "custom": {"type": "CustomHttpPattern", "id": 8}, "body": {"type": "string", "id": 7}, "responseBody": {"type": "string", "id": 12}, "additionalBindings": {"rule": "repeated", "type": "HttpRule", "id": 11}}}, "CustomHttpPattern": {"fields": {"kind": {"type": "string", "id": 1}, "path": {"type": "string", "id": 2}}}, "methodSignature": {"rule": "repeated", "type": "string", "id": 1051, "extend": "google.protobuf.MethodOptions"}, "defaultHost": {"type": "string", "id": 1049, "extend": "google.protobuf.ServiceOptions"}, "oauthScopes": {"type": "string", "id": 1050, "extend": "google.protobuf.ServiceOptions"}, "apiVersion": {"type": "string", "id": 525000001, "extend": "google.protobuf.ServiceOptions"}, "CommonLanguageSettings": {"fields": {"referenceDocsUri": {"type": "string", "id": 1, "options": {"deprecated": true}}, "destinations": {"rule": "repeated", "type": "ClientLibraryDestination", "id": 2}}}, "ClientLibrarySettings": {"fields": {"version": {"type": "string", "id": 1}, "launchStage": {"type": "LaunchStage", "id": 2}, "restNumericEnums": {"type": "bool", "id": 3}, "javaSettings": {"type": "JavaSettings", "id": 21}, "cppSettings": {"type": "CppSettings", "id": 22}, "phpSettings": {"type": "PhpSettings", "id": 23}, "pythonSettings": {"type": "PythonSettings", "id": 24}, "nodeSettings": {"type": "NodeSettings", "id": 25}, "dotnetSettings": {"type": "DotnetSettings", "id": 26}, "rubySettings": {"type": "RubySettings", "id": 27}, "goSettings": {"type": "GoSettings", "id": 28}}}, "Publishing": {"fields": {"methodSettings": {"rule": "repeated", "type": "MethodSettings", "id": 2}, "newIssueUri": {"type": "string", "id": 101}, "documentationUri": {"type": "string", "id": 102}, "apiShortName": {"type": "string", "id": 103}, "githubLabel": {"type": "string", "id": 104}, "codeownerGithubTeams": {"rule": "repeated", "type": "string", "id": 105}, "docTagPrefix": {"type": "string", "id": 106}, "organization": {"type": "ClientLibraryOrganization", "id": 107}, "librarySettings": {"rule": "repeated", "type": "ClientLibrarySettings", "id": 109}, "protoReferenceDocumentationUri": {"type": "string", "id": 110}, "restReferenceDocumentationUri": {"type": "string", "id": 111}}}, "JavaSettings": {"fields": {"libraryPackage": {"type": "string", "id": 1}, "serviceClassNames": {"keyType": "string", "type": "string", "id": 2}, "common": {"type": "CommonLanguageSettings", "id": 3}}}, "CppSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "PhpSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "PythonSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "NodeSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "DotnetSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}, "renamedServices": {"keyType": "string", "type": "string", "id": 2}, "renamedResources": {"keyType": "string", "type": "string", "id": 3}, "ignoredResources": {"rule": "repeated", "type": "string", "id": 4}, "forcedNamespaceAliases": {"rule": "repeated", "type": "string", "id": 5}, "handwrittenSignatures": {"rule": "repeated", "type": "string", "id": 6}}}, "RubySettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "GoSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "MethodSettings": {"fields": {"selector": {"type": "string", "id": 1}, "longRunning": {"type": "<PERSON><PERSON><PERSON>ning", "id": 2}, "autoPopulatedFields": {"rule": "repeated", "type": "string", "id": 3}}, "nested": {"LongRunning": {"fields": {"initialPollDelay": {"type": "google.protobuf.Duration", "id": 1}, "pollDelayMultiplier": {"type": "float", "id": 2}, "maxPollDelay": {"type": "google.protobuf.Duration", "id": 3}, "totalPollTimeout": {"type": "google.protobuf.Duration", "id": 4}}}}}, "ClientLibraryOrganization": {"values": {"CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED": 0, "CLOUD": 1, "ADS": 2, "PHOTOS": 3, "STREET_VIEW": 4, "SHOPPING": 5, "GEO": 6, "GENERATIVE_AI": 7}}, "ClientLibraryDestination": {"values": {"CLIENT_LIBRARY_DESTINATION_UNSPECIFIED": 0, "GITHUB": 10, "PACKAGE_MANAGER": 20}}, "LaunchStage": {"values": {"LAUNCH_STAGE_UNSPECIFIED": 0, "UNIMPLEMENTED": 6, "PRELAUNCH": 7, "EARLY_ACCESS": 1, "ALPHA": 2, "BETA": 3, "GA": 4, "DEPRECATED": 5}}}}, "protobuf": {"options": {"go_package": "google.golang.org/protobuf/types/descriptorpb", "java_package": "com.google.protobuf", "java_outer_classname": "DescriptorProtos", "csharp_namespace": "Google.Protobuf.Reflection", "objc_class_prefix": "GPB", "cc_enable_arenas": true, "optimize_for": "SPEED"}, "nested": {"FileDescriptorSet": {"edition": "proto2", "fields": {"file": {"rule": "repeated", "type": "FileDescriptorProto", "id": 1}}}, "Edition": {"edition": "proto2", "values": {"EDITION_UNKNOWN": 0, "EDITION_PROTO2": 998, "EDITION_PROTO3": 999, "EDITION_2023": 1000, "EDITION_2024": 1001, "EDITION_1_TEST_ONLY": 1, "EDITION_2_TEST_ONLY": 2, "EDITION_99997_TEST_ONLY": 99997, "EDITION_99998_TEST_ONLY": 99998, "EDITION_99999_TEST_ONLY": 99999, "EDITION_MAX": 2147483647}}, "FileDescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "package": {"type": "string", "id": 2}, "dependency": {"rule": "repeated", "type": "string", "id": 3}, "publicDependency": {"rule": "repeated", "type": "int32", "id": 10}, "weakDependency": {"rule": "repeated", "type": "int32", "id": 11}, "messageType": {"rule": "repeated", "type": "DescriptorProto", "id": 4}, "enumType": {"rule": "repeated", "type": "EnumDescriptorProto", "id": 5}, "service": {"rule": "repeated", "type": "ServiceDescriptorProto", "id": 6}, "extension": {"rule": "repeated", "type": "FieldDescriptorProto", "id": 7}, "options": {"type": "FileOptions", "id": 8}, "sourceCodeInfo": {"type": "SourceCodeInfo", "id": 9}, "syntax": {"type": "string", "id": 12}, "edition": {"type": "Edition", "id": 14}}}, "DescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "field": {"rule": "repeated", "type": "FieldDescriptorProto", "id": 2}, "extension": {"rule": "repeated", "type": "FieldDescriptorProto", "id": 6}, "nestedType": {"rule": "repeated", "type": "DescriptorProto", "id": 3}, "enumType": {"rule": "repeated", "type": "EnumDescriptorProto", "id": 4}, "extensionRange": {"rule": "repeated", "type": "ExtensionRange", "id": 5}, "oneofDecl": {"rule": "repeated", "type": "OneofDescriptorProto", "id": 8}, "options": {"type": "MessageOptions", "id": 7}, "reservedRange": {"rule": "repeated", "type": "ReservedRange", "id": 9}, "reservedName": {"rule": "repeated", "type": "string", "id": 10}}, "nested": {"ExtensionRange": {"fields": {"start": {"type": "int32", "id": 1}, "end": {"type": "int32", "id": 2}, "options": {"type": "ExtensionRangeOptions", "id": 3}}}, "ReservedRange": {"fields": {"start": {"type": "int32", "id": 1}, "end": {"type": "int32", "id": 2}}}}}, "ExtensionRangeOptions": {"edition": "proto2", "fields": {"uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}, "declaration": {"rule": "repeated", "type": "Declaration", "id": 2, "options": {"retention": "RETENTION_SOURCE"}}, "features": {"type": "FeatureSet", "id": 50}, "verification": {"type": "VerificationState", "id": 3, "options": {"default": "UNVERIFIED", "retention": "RETENTION_SOURCE"}}}, "extensions": [[1000, 536870911]], "nested": {"Declaration": {"fields": {"number": {"type": "int32", "id": 1}, "fullName": {"type": "string", "id": 2}, "type": {"type": "string", "id": 3}, "reserved": {"type": "bool", "id": 5}, "repeated": {"type": "bool", "id": 6}}, "reserved": [[4, 4]]}, "VerificationState": {"values": {"DECLARATION": 0, "UNVERIFIED": 1}}}}, "FieldDescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "number": {"type": "int32", "id": 3}, "label": {"type": "Label", "id": 4}, "type": {"type": "Type", "id": 5}, "typeName": {"type": "string", "id": 6}, "extendee": {"type": "string", "id": 2}, "defaultValue": {"type": "string", "id": 7}, "oneofIndex": {"type": "int32", "id": 9}, "jsonName": {"type": "string", "id": 10}, "options": {"type": "FieldOptions", "id": 8}, "proto3Optional": {"type": "bool", "id": 17}}, "nested": {"Type": {"values": {"TYPE_DOUBLE": 1, "TYPE_FLOAT": 2, "TYPE_INT64": 3, "TYPE_UINT64": 4, "TYPE_INT32": 5, "TYPE_FIXED64": 6, "TYPE_FIXED32": 7, "TYPE_BOOL": 8, "TYPE_STRING": 9, "TYPE_GROUP": 10, "TYPE_MESSAGE": 11, "TYPE_BYTES": 12, "TYPE_UINT32": 13, "TYPE_ENUM": 14, "TYPE_SFIXED32": 15, "TYPE_SFIXED64": 16, "TYPE_SINT32": 17, "TYPE_SINT64": 18}}, "Label": {"values": {"LABEL_OPTIONAL": 1, "LABEL_REPEATED": 3, "LABEL_REQUIRED": 2}}}}, "OneofDescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "options": {"type": "OneofOptions", "id": 2}}}, "EnumDescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "value": {"rule": "repeated", "type": "EnumValueDescriptorProto", "id": 2}, "options": {"type": "EnumOptions", "id": 3}, "reservedRange": {"rule": "repeated", "type": "EnumReservedRange", "id": 4}, "reservedName": {"rule": "repeated", "type": "string", "id": 5}}, "nested": {"EnumReservedRange": {"fields": {"start": {"type": "int32", "id": 1}, "end": {"type": "int32", "id": 2}}}}}, "EnumValueDescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "number": {"type": "int32", "id": 2}, "options": {"type": "EnumValueOptions", "id": 3}}}, "ServiceDescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "method": {"rule": "repeated", "type": "MethodDescriptorProto", "id": 2}, "options": {"type": "ServiceOptions", "id": 3}}}, "MethodDescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "inputType": {"type": "string", "id": 2}, "outputType": {"type": "string", "id": 3}, "options": {"type": "MethodOptions", "id": 4}, "clientStreaming": {"type": "bool", "id": 5, "options": {"default": false}}, "serverStreaming": {"type": "bool", "id": 6, "options": {"default": false}}}}, "FileOptions": {"edition": "proto2", "fields": {"javaPackage": {"type": "string", "id": 1}, "javaOuterClassname": {"type": "string", "id": 8}, "javaMultipleFiles": {"type": "bool", "id": 10, "options": {"default": false}}, "javaGenerateEqualsAndHash": {"type": "bool", "id": 20, "options": {"deprecated": true}}, "javaStringCheckUtf8": {"type": "bool", "id": 27, "options": {"default": false}}, "optimizeFor": {"type": "OptimizeMode", "id": 9, "options": {"default": "SPEED"}}, "goPackage": {"type": "string", "id": 11}, "ccGenericServices": {"type": "bool", "id": 16, "options": {"default": false}}, "javaGenericServices": {"type": "bool", "id": 17, "options": {"default": false}}, "pyGenericServices": {"type": "bool", "id": 18, "options": {"default": false}}, "deprecated": {"type": "bool", "id": 23, "options": {"default": false}}, "ccEnableArenas": {"type": "bool", "id": 31, "options": {"default": true}}, "objcClassPrefix": {"type": "string", "id": 36}, "csharpNamespace": {"type": "string", "id": 37}, "swiftPrefix": {"type": "string", "id": 39}, "phpClassPrefix": {"type": "string", "id": 40}, "phpNamespace": {"type": "string", "id": 41}, "phpMetadataNamespace": {"type": "string", "id": 44}, "rubyPackage": {"type": "string", "id": 45}, "features": {"type": "FeatureSet", "id": 50}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]], "reserved": [[42, 42], [38, 38]], "nested": {"OptimizeMode": {"values": {"SPEED": 1, "CODE_SIZE": 2, "LITE_RUNTIME": 3}}}}, "MessageOptions": {"edition": "proto2", "fields": {"messageSetWireFormat": {"type": "bool", "id": 1, "options": {"default": false}}, "noStandardDescriptorAccessor": {"type": "bool", "id": 2, "options": {"default": false}}, "deprecated": {"type": "bool", "id": 3, "options": {"default": false}}, "mapEntry": {"type": "bool", "id": 7}, "deprecatedLegacyJsonFieldConflicts": {"type": "bool", "id": 11, "options": {"deprecated": true}}, "features": {"type": "FeatureSet", "id": 12}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]], "reserved": [[4, 4], [5, 5], [6, 6], [8, 8], [9, 9]]}, "FieldOptions": {"edition": "proto2", "fields": {"ctype": {"type": "CType", "id": 1, "options": {"default": "STRING"}}, "packed": {"type": "bool", "id": 2}, "jstype": {"type": "JSType", "id": 6, "options": {"default": "JS_NORMAL"}}, "lazy": {"type": "bool", "id": 5, "options": {"default": false}}, "unverifiedLazy": {"type": "bool", "id": 15, "options": {"default": false}}, "deprecated": {"type": "bool", "id": 3, "options": {"default": false}}, "weak": {"type": "bool", "id": 10, "options": {"default": false}}, "debugRedact": {"type": "bool", "id": 16, "options": {"default": false}}, "retention": {"type": "OptionRetention", "id": 17}, "targets": {"rule": "repeated", "type": "OptionTargetType", "id": 19}, "editionDefaults": {"rule": "repeated", "type": "EditionDefault", "id": 20}, "features": {"type": "FeatureSet", "id": 21}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]], "reserved": [[4, 4], [18, 18]], "nested": {"CType": {"values": {"STRING": 0, "CORD": 1, "STRING_PIECE": 2}}, "JSType": {"values": {"JS_NORMAL": 0, "JS_STRING": 1, "JS_NUMBER": 2}}, "OptionRetention": {"values": {"RETENTION_UNKNOWN": 0, "RETENTION_RUNTIME": 1, "RETENTION_SOURCE": 2}}, "OptionTargetType": {"values": {"TARGET_TYPE_UNKNOWN": 0, "TARGET_TYPE_FILE": 1, "TARGET_TYPE_EXTENSION_RANGE": 2, "TARGET_TYPE_MESSAGE": 3, "TARGET_TYPE_FIELD": 4, "TARGET_TYPE_ONEOF": 5, "TARGET_TYPE_ENUM": 6, "TARGET_TYPE_ENUM_ENTRY": 7, "TARGET_TYPE_SERVICE": 8, "TARGET_TYPE_METHOD": 9}}, "EditionDefault": {"fields": {"edition": {"type": "Edition", "id": 3}, "value": {"type": "string", "id": 2}}}}}, "OneofOptions": {"edition": "proto2", "fields": {"features": {"type": "FeatureSet", "id": 1}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]]}, "EnumOptions": {"edition": "proto2", "fields": {"allowAlias": {"type": "bool", "id": 2}, "deprecated": {"type": "bool", "id": 3, "options": {"default": false}}, "deprecatedLegacyJsonFieldConflicts": {"type": "bool", "id": 6, "options": {"deprecated": true}}, "features": {"type": "FeatureSet", "id": 7}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]], "reserved": [[5, 5]]}, "EnumValueOptions": {"edition": "proto2", "fields": {"deprecated": {"type": "bool", "id": 1, "options": {"default": false}}, "features": {"type": "FeatureSet", "id": 2}, "debugRedact": {"type": "bool", "id": 3, "options": {"default": false}}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]]}, "ServiceOptions": {"edition": "proto2", "fields": {"features": {"type": "FeatureSet", "id": 34}, "deprecated": {"type": "bool", "id": 33, "options": {"default": false}}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]]}, "MethodOptions": {"edition": "proto2", "fields": {"deprecated": {"type": "bool", "id": 33, "options": {"default": false}}, "idempotencyLevel": {"type": "IdempotencyLevel", "id": 34, "options": {"default": "IDEMPOTENCY_UNKNOWN"}}, "features": {"type": "FeatureSet", "id": 35}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]], "nested": {"IdempotencyLevel": {"values": {"IDEMPOTENCY_UNKNOWN": 0, "NO_SIDE_EFFECTS": 1, "IDEMPOTENT": 2}}}}, "UninterpretedOption": {"edition": "proto2", "fields": {"name": {"rule": "repeated", "type": "NamePart", "id": 2}, "identifierValue": {"type": "string", "id": 3}, "positiveIntValue": {"type": "uint64", "id": 4}, "negativeIntValue": {"type": "int64", "id": 5}, "doubleValue": {"type": "double", "id": 6}, "stringValue": {"type": "bytes", "id": 7}, "aggregateValue": {"type": "string", "id": 8}}, "nested": {"NamePart": {"fields": {"namePart": {"rule": "required", "type": "string", "id": 1}, "isExtension": {"rule": "required", "type": "bool", "id": 2}}}}}, "FeatureSet": {"edition": "proto2", "fields": {"fieldPresence": {"type": "FieldPresence", "id": 1, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_2023", "edition_defaults.value": "EXPLICIT"}}, "enumType": {"type": "EnumType", "id": 2, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO3", "edition_defaults.value": "OPEN"}}, "repeatedFieldEncoding": {"type": "RepeatedFieldEncoding", "id": 3, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO3", "edition_defaults.value": "PACKED"}}, "utf8Validation": {"type": "Utf8Validation", "id": 4, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO3", "edition_defaults.value": "VERIFY"}}, "messageEncoding": {"type": "MessageEncoding", "id": 5, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO2", "edition_defaults.value": "LENGTH_PREFIXED"}}, "jsonFormat": {"type": "JsonFormat", "id": 6, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO3", "edition_defaults.value": "ALLOW"}}}, "extensions": [[1000, 1000], [1001, 1001], [1002, 1002], [9990, 9990], [9995, 9999], [10000, 10000]], "reserved": [[999, 999]], "nested": {"FieldPresence": {"values": {"FIELD_PRESENCE_UNKNOWN": 0, "EXPLICIT": 1, "IMPLICIT": 2, "LEGACY_REQUIRED": 3}}, "EnumType": {"values": {"ENUM_TYPE_UNKNOWN": 0, "OPEN": 1, "CLOSED": 2}}, "RepeatedFieldEncoding": {"values": {"REPEATED_FIELD_ENCODING_UNKNOWN": 0, "PACKED": 1, "EXPANDED": 2}}, "Utf8Validation": {"values": {"UTF8_VALIDATION_UNKNOWN": 0, "VERIFY": 2, "NONE": 3}}, "MessageEncoding": {"values": {"MESSAGE_ENCODING_UNKNOWN": 0, "LENGTH_PREFIXED": 1, "DELIMITED": 2}}, "JsonFormat": {"values": {"JSON_FORMAT_UNKNOWN": 0, "ALLOW": 1, "LEGACY_BEST_EFFORT": 2}}}}, "FeatureSetDefaults": {"edition": "proto2", "fields": {"defaults": {"rule": "repeated", "type": "FeatureSetEditionDefault", "id": 1}, "minimumEdition": {"type": "Edition", "id": 4}, "maximumEdition": {"type": "Edition", "id": 5}}, "nested": {"FeatureSetEditionDefault": {"fields": {"edition": {"type": "Edition", "id": 3}, "features": {"type": "FeatureSet", "id": 2}}}}}, "SourceCodeInfo": {"edition": "proto2", "fields": {"location": {"rule": "repeated", "type": "Location", "id": 1}}, "nested": {"Location": {"fields": {"path": {"rule": "repeated", "type": "int32", "id": 1, "options": {"packed": true}}, "span": {"rule": "repeated", "type": "int32", "id": 2, "options": {"packed": true}}, "leadingComments": {"type": "string", "id": 3}, "trailingComments": {"type": "string", "id": 4}, "leadingDetachedComments": {"rule": "repeated", "type": "string", "id": 6}}}}}, "GeneratedCodeInfo": {"edition": "proto2", "fields": {"annotation": {"rule": "repeated", "type": "Annotation", "id": 1}}, "nested": {"Annotation": {"fields": {"path": {"rule": "repeated", "type": "int32", "id": 1, "options": {"packed": true}}, "sourceFile": {"type": "string", "id": 2}, "begin": {"type": "int32", "id": 3}, "end": {"type": "int32", "id": 4}, "semantic": {"type": "Semantic", "id": 5}}, "nested": {"Semantic": {"values": {"NONE": 0, "SET": 1, "ALIAS": 2}}}}}}, "Timestamp": {"fields": {"seconds": {"type": "int64", "id": 1}, "nanos": {"type": "int32", "id": 2}}}, "Any": {"fields": {"type_url": {"type": "string", "id": 1}, "value": {"type": "bytes", "id": 2}}}, "Duration": {"fields": {"seconds": {"type": "int64", "id": 1}, "nanos": {"type": "int32", "id": 2}}}, "Empty": {"fields": {}}, "FieldMask": {"fields": {"paths": {"rule": "repeated", "type": "string", "id": 1}}}}}, "rpc": {"options": {"cc_enable_arenas": true, "go_package": "google.golang.org/genproto/googleapis/rpc/status;status", "java_multiple_files": true, "java_outer_classname": "StatusProto", "java_package": "com.google.rpc", "objc_class_prefix": "RPC"}, "nested": {"Status": {"fields": {"code": {"type": "int32", "id": 1}, "message": {"type": "string", "id": 2}, "details": {"rule": "repeated", "type": "google.protobuf.Any", "id": 3}}}}}, "longrunning": {"options": {"cc_enable_arenas": true, "csharp_namespace": "Google.LongRunning", "go_package": "cloud.google.com/go/longrunning/autogen/longrunningpb;longrunningpb", "java_multiple_files": true, "java_outer_classname": "OperationsProto", "java_package": "com.google.longrunning", "php_namespace": "Google\\LongRunning"}, "nested": {"operationInfo": {"type": "google.longrunning.OperationInfo", "id": 1049, "extend": "google.protobuf.MethodOptions"}, "Operations": {"options": {"(google.api.default_host)": "longrunning.googleapis.com"}, "methods": {"ListOperations": {"requestType": "ListOperationsRequest", "responseType": "ListOperationsResponse", "options": {"(google.api.http).get": "/v1/{name=operations}", "(google.api.method_signature)": "name,filter"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{name=operations}"}}, {"(google.api.method_signature)": "name,filter"}]}, "GetOperation": {"requestType": "GetOperationRequest", "responseType": "Operation", "options": {"(google.api.http).get": "/v1/{name=operations/**}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{name=operations/**}"}}, {"(google.api.method_signature)": "name"}]}, "DeleteOperation": {"requestType": "DeleteOperationRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).delete": "/v1/{name=operations/**}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v1/{name=operations/**}"}}, {"(google.api.method_signature)": "name"}]}, "CancelOperation": {"requestType": "CancelOperationRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).post": "/v1/{name=operations/**}:cancel", "(google.api.http).body": "*", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1/{name=operations/**}:cancel", "body": "*"}}, {"(google.api.method_signature)": "name"}]}, "WaitOperation": {"requestType": "WaitOperationRequest", "responseType": "Operation"}}}, "Operation": {"oneofs": {"result": {"oneof": ["error", "response"]}}, "fields": {"name": {"type": "string", "id": 1}, "metadata": {"type": "google.protobuf.Any", "id": 2}, "done": {"type": "bool", "id": 3}, "error": {"type": "google.rpc.Status", "id": 4}, "response": {"type": "google.protobuf.Any", "id": 5}}}, "GetOperationRequest": {"fields": {"name": {"type": "string", "id": 1}}}, "ListOperationsRequest": {"fields": {"name": {"type": "string", "id": 4}, "filter": {"type": "string", "id": 1}, "pageSize": {"type": "int32", "id": 2}, "pageToken": {"type": "string", "id": 3}}}, "ListOperationsResponse": {"fields": {"operations": {"rule": "repeated", "type": "Operation", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "CancelOperationRequest": {"fields": {"name": {"type": "string", "id": 1}}}, "DeleteOperationRequest": {"fields": {"name": {"type": "string", "id": 1}}}, "WaitOperationRequest": {"fields": {"name": {"type": "string", "id": 1}, "timeout": {"type": "google.protobuf.Duration", "id": 2}}}, "OperationInfo": {"fields": {"responseType": {"type": "string", "id": 1}, "metadataType": {"type": "string", "id": 2}}}}}}}}}