

.pageContainer {
    background-color: #f9f9f9;
    width: 100%;
    display: flex;
    justify-content: center;
    overflow-y: auto;
}

.main {
    width: 90%;
    padding: 20px;
    /* Add padding for spacing inside the container */
    background-color: #ffffff;
    /* Set a light background color */
    border-radius: 8px;
    /* Rounded corners */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    /* Soft shadow effect */
    border: 1px solid #e0e0e0;
    /* Optional: Add a light border */
    height: 100vh;

}

.columnsContainer {
    display: flex;
    overflow-x: auto;
    height: 100%;
}

/* Make the scrollbar thin */
::-webkit-scrollbar {
    width: 6px;       /* Vertical scrollbar width */
    height: 6px;      /* Horizontal scrollbar height */
  }
  
  /* Track (the background of the scrollbar) */
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  
  /* Thumb (the draggable part of the scrollbar) */
  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3); /* semi-transparent thumb */
    border-radius: 10px;
  }
  
  /* Optional: On hover */
  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }
  

@media (max-width:993px) {
    .pageContainer {
        flex-direction: column;
    }

    
    .main {
        width: 100%;
        margin: 0 auto;
        /* margin-top: .5rem; */
        border-radius: 0;
        height: calc(100vh - 65px);
    }
}

.pipelineDropdownWrapper {
  position: relative;

}
.dropdown-item.active {
  font-weight: bold;
  background-color: #f0f0f0;
}

.stageWrapper {
  position: relative;
}

.addButton {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.stageWrapper:hover .addButton {
  opacity: 1;
}
.separator {
  border: 0;
  border-top: 1px solid #5f5e5e; 
  margin: 5px 0;  
  width: 100%;
}
