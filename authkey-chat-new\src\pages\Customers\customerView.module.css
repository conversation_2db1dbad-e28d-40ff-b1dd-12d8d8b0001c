.pageContainer {
    width: 100%;
    display: flex;
    justify-content: center;
    height: 100vh;
    background-color: whitesmoke;
    overflow-x: hidden;
    overflow-y: auto;

}

.main {
    flex-grow: 1;
    height: 100%;
    padding: 1rem;
}

.card {
    padding: .8rem;
    background-color: white;
    border-radius: .5rem;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);
    height: 100%;

}

.profileCard {
    width: 30%;


}

.detailCard {
    width: 70%;
   

}

@media (max-width:993px) {
    .pageContainer {
        height: calc(100vh - 65px);
        width: 100vw;
        display: block;
    }
    
    .main {
        height: auto;
        width: 100vw;
        margin-left: 0;
       
    }

    .profileCard {
        width: 100%;
        
    }

    .detailCard {
        width: 100%;
      

    }
    .card{
        height: auto;
    }
}

.tabsContainer {
    display: flex;
    border-bottom: 2px solid #ddd;
    gap: 10px;
}

.navLink {
    background: none;
    border: none;
    padding: 10px 15px;
    font-size: 16px;
    cursor: pointer;
    color: #333;
    transition: color 0.3s, border-bottom 0.3s;
}

.navLink:hover {
    color: #04aa6d;
}

.active {
    font-weight: bold;
    color: #04aa6d !important;
    border-bottom: 2px solid #04aa6d;
}

.tabContent {
    margin-top: 15px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.stats {
    display: flex;
    justify-content: space-between;
    width: 100%;
    text-align: center;
}

.stats div {
    flex: 1;
}

.contactInfo {
    font-size: 0.95rem;
    color: #555;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.contactInfo>div {
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.contactInfoSection {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
    width: 100%;
    max-width: 400px;
}

.agentSection {
    .list-group-item {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        margin-bottom: 6px;
        padding: 10px 15px;
        font-size: 0.95rem;
    }
}