// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.translation.v3;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/translate/v3/common.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.Translate.V3";
option go_package = "cloud.google.com/go/translate/apiv3/translatepb;translatepb";
option java_multiple_files = true;
option java_outer_classname = "AutoMLTranslationProto";
option java_package = "com.google.cloud.translate.v3";
option php_namespace = "Google\\Cloud\\Translate\\V3";
option ruby_package = "Google::Cloud::Translate::V3";

// Request message for ImportData.
message ImportDataRequest {
  // Required. Name of the dataset. In form of
  // `projects/{project-number-or-id}/locations/{location-id}/datasets/{dataset-id}`
  string dataset = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The config for the input content.
  DatasetInputConfig input_config = 2 [(google.api.field_behavior) = REQUIRED];
}

// Input configuration for datasets.
message DatasetInputConfig {
  // An input file.
  message InputFile {
    // Optional. Usage of the file contents. Options are TRAIN|VALIDATION|TEST,
    // or UNASSIGNED (by default) for auto split.
    string usage = 2 [(google.api.field_behavior) = OPTIONAL];

    // Source of the file containing sentence pairs.
    // Supported formats are tab-separated values (.tsv) and Translation Memory
    // eXchange (.tmx) .
    oneof source {
      // Google Cloud Storage file source.
      GcsInputSource gcs_source = 3;
    }
  }

  // Files containing the sentence pairs to be imported to the dataset.
  repeated InputFile input_files = 1;
}

// Metadata of import data operation.
message ImportDataMetadata {
  // The current state of the operation.
  OperationState state = 1;

  // The creation time of the operation.
  google.protobuf.Timestamp create_time = 2;

  // The last update time of the operation.
  google.protobuf.Timestamp update_time = 3;

  // Only populated when operation doesn't succeed.
  google.rpc.Status error = 4;
}

// Request message for ExportData.
message ExportDataRequest {
  // Required. Name of the dataset. In form of
  // `projects/{project-number-or-id}/locations/{location-id}/datasets/{dataset-id}`
  string dataset = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The config for the output content.
  DatasetOutputConfig output_config = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Output configuration for datasets.
message DatasetOutputConfig {
  // Required. Specify the output.
  oneof destination {
    // Google Cloud Storage destination to write the output.
    GcsOutputDestination gcs_destination = 1;
  }
}

// Metadata of export data operation.
message ExportDataMetadata {
  // The current state of the operation.
  OperationState state = 1;

  // The creation time of the operation.
  google.protobuf.Timestamp create_time = 2;

  // The last update time of the operation.
  google.protobuf.Timestamp update_time = 3;

  // Only populated when operation doesn't succeed.
  google.rpc.Status error = 4;
}

// Request message for DeleteDataset.
message DeleteDatasetRequest {
  // Required. The name of the dataset to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "translate.googleapis.com/Dataset"
    }
  ];
}

// Metadata of delete dataset operation.
message DeleteDatasetMetadata {
  // The current state of the operation.
  OperationState state = 1;

  // The creation time of the operation.
  google.protobuf.Timestamp create_time = 2;

  // The last update time of the operation.
  google.protobuf.Timestamp update_time = 3;

  // Only populated when operation doesn't succeed.
  google.rpc.Status error = 4;
}

// Request message for GetDataset.
message GetDatasetRequest {
  // Required. The resource name of the dataset to retrieve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "translate.googleapis.com/Dataset"
    }
  ];
}

// Request message for ListDatasets.
message ListDatasetsRequest {
  // Required. Name of the parent project. In form of
  // `projects/{project-number-or-id}/locations/{location-id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Requested page size. The server can return fewer results than
  // requested.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results for the server to return.
  // Typically obtained from next_page_token field in the response of a
  // ListDatasets call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListDatasets.
message ListDatasetsResponse {
  // The datasets read.
  repeated Dataset datasets = 1;

  // A token to retrieve next page of results.
  // Pass this token to the page_token field in the ListDatasetsRequest to
  // obtain the corresponding page.
  string next_page_token = 2;
}

// Request message for CreateDataset.
message CreateDatasetRequest {
  // Required. The project name.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The Dataset to create.
  Dataset dataset = 2 [(google.api.field_behavior) = REQUIRED];
}

// Metadata of create dataset operation.
message CreateDatasetMetadata {
  // The current state of the operation.
  OperationState state = 1;

  // The creation time of the operation.
  google.protobuf.Timestamp create_time = 2;

  // The last update time of the operation.
  google.protobuf.Timestamp update_time = 3;

  // Only populated when operation doesn't succeed.
  google.rpc.Status error = 4;
}

// Request message for ListExamples.
message ListExamplesRequest {
  // Required. Name of the parent dataset. In form of
  // `projects/{project-number-or-id}/locations/{location-id}/datasets/{dataset-id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "translate.googleapis.com/Dataset"
    }
  ];

  // Optional. An expression for filtering the examples that will be returned.
  // Example filter:
  // * `usage=TRAIN`
  string filter = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Requested page size. The server can return fewer results than
  // requested.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results for the server to return.
  // Typically obtained from next_page_token field in the response of a
  // ListExamples call.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListExamples.
message ListExamplesResponse {
  // The sentence pairs.
  repeated Example examples = 1;

  // A token to retrieve next page of results.
  // Pass this token to the page_token field in the ListExamplesRequest to
  // obtain the corresponding page.
  string next_page_token = 2;
}

// A sentence pair.
message Example {
  option (google.api.resource) = {
    type: "translate.googleapis.com/Example"
    pattern: "projects/{project}/locations/{location}/datasets/{dataset}/examples/{example}"
  };

  // Output only. The resource name of the example, in form of
  // `projects/{project-number-or-id}/locations/{location_id}/datasets/{dataset_id}/examples/{example_id}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Sentence in source language.
  string source_text = 2;

  // Sentence in target language.
  string target_text = 3;

  // Output only. Usage of the sentence pair. Options are TRAIN|VALIDATION|TEST.
  string usage = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Response message for BatchTransferResources.
message BatchTransferResourcesResponse {
  // Transfer response for a single resource.
  message TransferResourceResponse {
    // Full name of the resource to transfer as specified in the request.
    string source = 1;

    // Full name of the new resource successfully transferred from the source
    // hosted by Translation API. Target will be empty if the transfer failed.
    string target = 2;

    // The error result in case of failure.
    google.rpc.Status error = 3;
  }

  // Responses of the transfer for individual resources.
  repeated TransferResourceResponse responses = 1;
}

// A dataset that hosts the examples (sentence pairs) used for translation
// models.
message Dataset {
  option (google.api.resource) = {
    type: "translate.googleapis.com/Dataset"
    pattern: "projects/{project}/locations/{location}/datasets/{dataset}"
  };

  // The resource name of the dataset, in form of
  // `projects/{project-number-or-id}/locations/{location_id}/datasets/{dataset_id}`
  string name = 1;

  // The name of the dataset to show in the interface. The name can be
  // up to 32 characters long and can consist only of ASCII Latin letters A-Z
  // and a-z, underscores (_), and ASCII digits 0-9.
  string display_name = 2;

  // The BCP-47 language code of the source language.
  string source_language_code = 3;

  // The BCP-47 language code of the target language.
  string target_language_code = 4;

  // Output only. The number of examples in the dataset.
  int32 example_count = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of training examples (sentence pairs).
  int32 train_example_count = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of validation examples (sentence pairs).
  int32 validate_example_count = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of test examples (sentence pairs).
  int32 test_example_count = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this dataset was created.
  google.protobuf.Timestamp create_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this dataset was last updated.
  google.protobuf.Timestamp update_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for CreateModel.
message CreateModelRequest {
  // Required. The project name, in form of
  // `projects/{project}/locations/{location}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The Model to create.
  Model model = 2 [(google.api.field_behavior) = REQUIRED];
}

// Metadata of create model operation.
message CreateModelMetadata {
  // The current state of the operation.
  OperationState state = 1;

  // The creation time of the operation.
  google.protobuf.Timestamp create_time = 2;

  // The last update time of the operation.
  google.protobuf.Timestamp update_time = 3;

  // Only populated when operation doesn't succeed.
  google.rpc.Status error = 4;
}

// Request message for ListModels.
message ListModelsRequest {
  // Required. Name of the parent project. In form of
  // `projects/{project-number-or-id}/locations/{location-id}`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. An expression for filtering the models that will be returned.
  // Supported filter:
  // `dataset_id=${dataset_id}`
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Requested page size. The server can return fewer results than
  // requested.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results for the server to return.
  // Typically obtained from next_page_token field in the response of a
  // ListModels call.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for ListModels.
message ListModelsResponse {
  // The models read.
  repeated Model models = 1;

  // A token to retrieve next page of results.
  // Pass this token to the page_token field in the ListModelsRequest to
  // obtain the corresponding page.
  string next_page_token = 2;
}

// Request message for GetModel.
message GetModelRequest {
  // Required. The resource name of the model to retrieve.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "translate.googleapis.com/Model" }
  ];
}

// Request message for DeleteModel.
message DeleteModelRequest {
  // Required. The name of the model to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "translate.googleapis.com/Model" }
  ];
}

// Metadata of delete model operation.
message DeleteModelMetadata {
  // The current state of the operation.
  OperationState state = 1;

  // The creation time of the operation.
  google.protobuf.Timestamp create_time = 2;

  // The last update time of the operation.
  google.protobuf.Timestamp update_time = 3;

  // Only populated when operation doesn't succeed.
  google.rpc.Status error = 4;
}

// A trained translation model.
message Model {
  option (google.api.resource) = {
    type: "translate.googleapis.com/Model"
    pattern: "projects/{project}/locations/{location}/models/{model}"
  };

  // The resource name of the model, in form of
  // `projects/{project-number-or-id}/locations/{location_id}/models/{model_id}`
  string name = 1;

  // The name of the model to show in the interface. The name can be
  // up to 32 characters long and can consist only of ASCII Latin letters A-Z
  // and a-z, underscores (_), and ASCII digits 0-9.
  string display_name = 2;

  // The dataset from which the model is trained, in form of
  // `projects/{project-number-or-id}/locations/{location_id}/datasets/{dataset_id}`
  string dataset = 3;

  // Output only. The BCP-47 language code of the source language.
  string source_language_code = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The BCP-47 language code of the target language.
  string target_language_code = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of examples (sentence pairs) used to train the model.
  int32 train_example_count = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of examples (sentence pairs) used to validate the
  // model.
  int32 validate_example_count = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Number of examples (sentence pairs) used to test the model.
  int32 test_example_count = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the model resource was created, which is also
  // when the training started.
  google.protobuf.Timestamp create_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when this model was last updated.
  google.protobuf.Timestamp update_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
