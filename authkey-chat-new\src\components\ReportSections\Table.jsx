import React, { useState, useContext } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Box, CircularProgress } from "@mui/material";
import {
    DataGrid, GridToolbarContainer,
    GridToolbarExport, GridToolbarFilterButton,
    GridToolbarQuickFilter
} from "@mui/x-data-grid";
import axios from "axios";
import { ChatState } from "../../context/AllProviders";
import { AuthContext } from "../../context/AuthContext";
import { BASE_URL2 } from "../../api/api";

const Table = ({ data, onLoadMore, setCustomerData, setModalOpen }) => {
    const navigate = useNavigate();
    const { currentUser } = useContext(AuthContext);
    const { handleChatOpen } = ChatState();
    const [chatLoading, setChatLoading] = useState(false);
    const [paginationModel, setPaginationModel] = useState({ page: 0, pageSize: 5 });

    if (!data || data.length === 0) return <p>No data available</p>;

    const handleCustomerClick = async (mobileNumber, name) => {
        try {
            setChatLoading(true);
            const response = await axios.post(`${BASE_URL2}/whatsapp_report`, {
                user_id: currentUser.parent_id,
                token: currentUser.parent_token,
                method: "customer_history",
                mobile: mobileNumber,
                brand_number: currentUser.brand_number,
            });
            if (response.data.success) {
                setCustomerData(response.data.data);
                setModalOpen(true);
                navigate(`/dashboard?mob=${btoa(mobileNumber)}&name=${name}`);
            }
        } catch (error) {
            console.error(error);
        } finally {
            setChatLoading(false);
        }
    };
    // const handleCustomerClick = async (mobileNumber, name) => {


    //     navigate(`/customer-details/${btoa(mobileNumber)}`);


    // };

    const handleClick = async (chat) => {
        setChatLoading(true);
        await handleChatOpen(chat);
        setChatLoading(false);
        navigate(`/home/<USER>
    };

    const columns = Object.keys(data[0])
        .map((key) => {
            if (key === "Last Message Csv") return null;
            return {
                field: key,
                headerName: key,
                flex: 1,
                renderCell: (params) => {
                    if (key === "Customer Number" || key === "Customer Name") {
                        return (
                            <span
                                style={{ color: "#007bff", cursor: "pointer", fontWeight: 500 }}
                                onClick={() => handleCustomerClick(params.row["Customer Number"], params.row["Customer Name"])}
                            >
                                {params.value}
                            </span>
                        );
                    }
                    return key === "History" ? (
                        <Link to={params.value} style={{ textDecoration: "none", color: "#007bff", fontWeight: 500 }}>
                            View Report
                        </Link>
                    ) : key === "View Chat" ? (
                        <span
                            onClick={() => handleClick(params.value)}
                            style={{ textDecoration: "underline", color: "#007bff", cursor: "pointer", fontWeight: 500 }}
                        >
                            View
                        </span>
                    ) : key === "Status" ? (
                        <span style={{ color: params.value ? "red" : "green", fontWeight: 600 }}>
                            {params.value ? "Inactive" : "Active"}
                        </span>
                    ) : key === "Chat Status" ? (
                        <span style={{ color: params.value === "Active" ? "green" : params.value === "Waiting" ? "red" : "" }}>
                            {params.value}
                        </span>
                    ) : (
                        params.value
                    );
                },
            };
        })
        .filter((column) => column !== null);

    const rows = data.map((row, index) => ({ id: index, ...row }));

    const handlePaginationChange = (model) => {
        const totalPages = Math.ceil(data.length / model.pageSize);
        setPaginationModel(model);
        if (model.page + 1 === totalPages && onLoadMore) onLoadMore();
    };

    const CustomToolbar = () => (
        <GridToolbarContainer sx={{ padding: "8px", background: "#f0f2f5", borderRadius: "10px 10px 0 0" }}>
            <GridToolbarExport
                csvOptions={{
                    fileName: "Chat_Report",
                    delimiter: ",",
                    utf8WithBom: true,
                }}
            />
            <GridToolbarFilterButton />
            <GridToolbarQuickFilter debounceMs={500} />
        </GridToolbarContainer>
    );

    return (
        <>
            {chatLoading && (
                <div
                    style={{
                        position: "fixed",
                        zIndex: 100,
                        top: 0,
                        left: 0,
                        width: "100vw",
                        height: "100vh",
                        backgroundColor: "rgba(0, 0, 0, 0.1)",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        backdropFilter: "blur(3px)",
                    }}
                >
                    <CircularProgress />
                </div>
            )}

            <Box sx={{
                width: "100%",
                backgroundColor: "white",
                borderRadius: "10px",
                boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
                overflow: "hidden",
                "& .MuiDataGrid-root": {
                    border: "none",
                },
                "& .MuiDataGrid-columnHeaders": {
                    backgroundColor: "#1976D2",
                    color: "black",
                    fontSize: "14px",
                    fontWeight: "bold",
                },
                "& .MuiDataGrid-row:nth-of-type(even)": {
                    backgroundColor: "#f9f9f9",
                },
                "& .MuiDataGrid-row:hover": {
                    backgroundColor: "#e3f2fd",
                    transition: "background 0.3s ease-in-out",
                },
                "& .MuiPaginationItem-root": {
                    color: "#1976D2",
                    fontWeight: "bold",
                },
                "& .MuiButtonBase-root": {
                    fontWeight: "bold",
                    textTransform: "none",
                },
            }}>
                <DataGrid
                    rows={rows}
                    columns={columns}
                    initialState={{ pagination: { paginationModel } }}
                    pageSizeOptions={[5, 10, 20]}
                    paginationModel={paginationModel}
                    onPaginationModelChange={handlePaginationChange}
                    autoHeight
                    disableSelectionOnClick
                    slots={{ toolbar: CustomToolbar }}
                />
            </Box>
        </>
    );
};

export default Table;
