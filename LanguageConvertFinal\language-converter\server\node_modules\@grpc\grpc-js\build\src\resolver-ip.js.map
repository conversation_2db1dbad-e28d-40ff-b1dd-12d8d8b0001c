{"version": 3, "file": "resolver-ip.js", "sourceRoot": "", "sources": ["../../src/resolver-ip.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;AAsGH,sBAGC;AAvGD,6BAAqC;AAGrC,2CAAmD;AACnD,yCAAsC;AACtC,yCAA0E;AAE1E,6CAAmE;AACnE,qCAAqC;AAErC,MAAM,WAAW,GAAG,aAAa,CAAC;AAElC,SAAS,KAAK,CAAC,IAAY;IACzB,OAAO,CAAC,KAAK,CAAC,wBAAY,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AACvD,CAAC;AAED,MAAM,WAAW,GAAG,MAAM,CAAC;AAC3B,MAAM,WAAW,GAAG,MAAM,CAAC;AAE3B;;GAEG;AACH,MAAM,YAAY,GAAG,GAAG,CAAC;AAEzB,MAAM,UAAU;IAId,YACE,MAAe,EACP,QAA0B,EAClC,cAA8B;;QADtB,aAAQ,GAAR,QAAQ,CAAkB;QAL5B,cAAS,GAAe,EAAE,CAAC;QAC3B,UAAK,GAAwB,IAAI,CAAC;QAClC,sBAAiB,GAAG,KAAK,CAAC;QAMhC,KAAK,CAAC,kCAAkC,GAAG,IAAA,wBAAW,EAAC,MAAM,CAAC,CAAC,CAAC;QAChE,MAAM,SAAS,GAAwB,EAAE,CAAC;QAC1C,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,CAAC,EAAE,CAAC;YACtE,IAAI,CAAC,KAAK,GAAG;gBACX,IAAI,EAAE,kBAAM,CAAC,WAAW;gBACxB,OAAO,EAAE,uBAAuB,MAAM,CAAC,MAAM,iBAAiB;gBAC9D,QAAQ,EAAE,IAAI,mBAAQ,EAAE;aACzB,CAAC;YACF,OAAO;QACT,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxC,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,IAAA,0BAAa,EAAC,IAAI,CAAC,CAAC;YACrC,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBACtB,IAAI,CAAC,KAAK,GAAG;oBACX,IAAI,EAAE,kBAAM,CAAC,WAAW;oBACxB,OAAO,EAAE,mBAAmB,MAAM,CAAC,MAAM,YAAY,IAAI,EAAE;oBAC3D,QAAQ,EAAE,IAAI,mBAAQ,EAAE;iBACzB,CAAC;gBACF,OAAO;YACT,CAAC;YACD,IACE,CAAC,MAAM,CAAC,MAAM,KAAK,WAAW,IAAI,CAAC,IAAA,YAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACzD,CAAC,MAAM,CAAC,MAAM,KAAK,WAAW,IAAI,CAAC,IAAA,YAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EACzD,CAAC;gBACD,IAAI,CAAC,KAAK,GAAG;oBACX,IAAI,EAAE,kBAAM,CAAC,WAAW;oBACxB,OAAO,EAAE,mBAAmB,MAAM,CAAC,MAAM,YAAY,IAAI,EAAE;oBAC3D,QAAQ,EAAE,IAAI,mBAAQ,EAAE;iBACzB,CAAC;gBACF,OAAO;YACT,CAAC;YACD,SAAS,CAAC,IAAI,CAAC;gBACb,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,IAAI,EAAE,MAAA,QAAQ,CAAC,IAAI,mCAAI,YAAY;aACpC,CAAC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QACtE,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,GAAG,gBAAgB,GAAG,SAAS,CAAC,CAAC;IAClE,CAAC;IACD,gBAAgB;QACd,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;gBACpB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpC,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAClC,IAAI,CAAC,SAAS,EACd,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,EAAE,CACH,CAAC;gBACJ,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IACD,OAAO;QACL,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACjC,CAAC;IAED,MAAM,CAAC,mBAAmB,CAAC,MAAe;QACxC,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;CACF;AAED,SAAgB,KAAK;IACnB,IAAA,2BAAgB,EAAC,WAAW,EAAE,UAAU,CAAC,CAAC;IAC1C,IAAA,2BAAgB,EAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AAC5C,CAAC"}