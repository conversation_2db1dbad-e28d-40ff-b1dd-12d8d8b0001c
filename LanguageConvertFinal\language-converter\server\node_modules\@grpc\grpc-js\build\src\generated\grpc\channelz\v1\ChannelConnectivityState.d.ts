export declare const _grpc_channelz_v1_ChannelConnectivityState_State: {
    readonly UNKNOWN: "UNKNOWN";
    readonly IDLE: "IDLE";
    readonly CONNECTING: "CONNECTING";
    readonly READY: "READY";
    readonly TRANSIENT_FAILURE: "TRANSIENT_FAILURE";
    readonly SHUTDOWN: "SHUTDOWN";
};
export type _grpc_channelz_v1_ChannelConnectivityState_State = 'UNKNOWN' | 0 | 'IDLE' | 1 | 'CONNECTING' | 2 | 'READY' | 3 | 'TRANSIENT_FAILURE' | 4 | 'SHUTDOWN' | 5;
export type _grpc_channelz_v1_ChannelConnectivityState_State__Output = typeof _grpc_channelz_v1_ChannelConnectivityState_State[keyof typeof _grpc_channelz_v1_ChannelConnectivityState_State];
/**
 * These come from the specified states in this document:
 * https://github.com/grpc/grpc/blob/master/doc/connectivity-semantics-and-api.md
 */
export interface ChannelConnectivityState {
    'state'?: (_grpc_channelz_v1_ChannelConnectivityState_State);
}
/**
 * These come from the specified states in this document:
 * https://github.com/grpc/grpc/blob/master/doc/connectivity-semantics-and-api.md
 */
export interface ChannelConnectivityState__Output {
    'state': (_grpc_channelz_v1_ChannelConnectivityState_State__Output);
}
