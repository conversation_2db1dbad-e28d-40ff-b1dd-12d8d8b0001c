{"version": 3, "file": "resolver-dns.js", "sourceRoot": "", "sources": ["../../src/resolver-dns.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAuZH,sBAGC;AAxZD,yCAKoB;AACpB,6BAAsC;AACtC,qDAAgF;AAChF,2CAAqC;AAErC,yCAAsC;AACtC,qCAAqC;AACrC,2CAA2C;AAE3C,6CAAmE;AACnE,6BAAqC;AAErC,uDAAmE;AACnE,+CAAmE;AAEnE,MAAM,WAAW,GAAG,cAAc,CAAC;AAEnC,SAAS,KAAK,CAAC,IAAY;IACzB,OAAO,CAAC,KAAK,CAAC,wBAAY,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AACvD,CAAC;AAED;;GAEG;AACU,QAAA,YAAY,GAAG,GAAG,CAAC;AAEhC,MAAM,uCAAuC,GAAG,KAAM,CAAC;AAEvD;;GAEG;AACH,MAAM,WAAW;IAyBf,YACU,MAAe,EACf,QAA0B,EAClC,cAA8B;;QAFtB,WAAM,GAAN,MAAM,CAAS;QACf,aAAQ,GAAR,QAAQ,CAAkB;QAjB5B,yBAAoB,GAA2C,IAAI,CAAC;QACpE,sBAAiB,GAA+B,IAAI,CAAC;QACrD,uBAAkB,GAAsB,IAAI,CAAC;QAC7C,wBAAmB,GAAyB,IAAI,CAAC;QACjD,6BAAwB,GAAwB,IAAI,CAAC;QAIrD,sBAAiB,GAAG,KAAK,CAAC;QAE1B,iCAA4B,GAAG,KAAK,CAAC;QACrC,2BAAsB,GAAG,IAAI,CAAC;QAC9B,qBAAgB,GAAG,KAAK,CAAC;QACzB,wBAAmB,GAAG,IAAI,cAAG,CAAC,QAAQ,EAAE,CAAC;QAO/C,KAAK,CAAC,kCAAkC,GAAG,IAAA,wBAAW,EAAC,MAAM,CAAC,CAAC,CAAC;QAChE,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;QAC1D,CAAC;QACD,MAAM,QAAQ,GAAG,IAAA,0BAAa,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,CAAC;aAAM,CAAC;YACN,IAAI,IAAA,YAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAA,YAAM,EAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnD,IAAI,CAAC,QAAQ,GAAG;oBACd;wBACE,SAAS,EAAE;4BACT;gCACE,IAAI,EAAE,QAAQ,CAAC,IAAI;gCACnB,IAAI,EAAE,MAAA,QAAQ,CAAC,IAAI,mCAAI,oBAAY;6BACpC;yBACF;qBACF;iBACF,CAAC;gBACF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC;gBACjC,IAAI,CAAC,IAAI,GAAG,MAAA,QAAQ,CAAC,IAAI,mCAAI,oBAAY,CAAC;YAC5C,CAAC;QACH,CAAC;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;QAEtC,IAAI,cAAc,CAAC,wCAAwC,CAAC,KAAK,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,sBAAsB,GAAG;YAC5B,IAAI,EAAE,kBAAM,CAAC,WAAW;YACxB,OAAO,EAAE,qCAAqC,IAAA,wBAAW,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACxE,QAAQ,EAAE,IAAI,mBAAQ,EAAE;SACzB,CAAC;QAEF,MAAM,cAAc,GAAmB;YACrC,YAAY,EAAE,cAAc,CAAC,mCAAmC,CAAC;YACjE,QAAQ,EAAE,cAAc,CAAC,+BAA+B,CAAC;SAC1D,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG,IAAI,gCAAc,CAAC,GAAG,EAAE;YACrC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,IAAI,CAAC,0BAA0B,EAAE,CAAC;YACpC,CAAC;QACH,CAAC,EAAE,cAAc,CAAC,CAAC;QACnB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QAErB,IAAI,CAAC,2BAA2B;YAC9B,MAAA,cAAc,CAAC,0CAA0C,CAAC,mCAC1D,uCAAuC,CAAC;QAC1C,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,GAAG,EAAE,GAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACnD,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACzC,CAAC;IAED;;;OAGG;IACK,eAAe;QACrB,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC3B,KAAK,CAAC,kCAAkC,GAAG,IAAA,wBAAW,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;gBACrE,YAAY,CAAC,GAAG,EAAE;oBAChB,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAClC,IAAI,CAAC,QAAS,EACd,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,EAAE,CACH,CAAC;gBACJ,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC/B,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACrB,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,OAAO;QACT,CAAC;QACD,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;YAC9B,KAAK,CAAC,8BAA8B,GAAG,IAAA,wBAAW,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YACjE,YAAY,CAAC,GAAG,EAAE;gBAChB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;oBACpB,IAAI,EAAE,kBAAM,CAAC,WAAW;oBACxB,OAAO,EAAE,+BAA+B,IAAA,wBAAW,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBAClE,QAAQ,EAAE,IAAI,mBAAQ,EAAE;iBACzB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE,CAAC;gBACvC,OAAO;YACT,CAAC;YACD,KAAK,CAAC,0BAA0B,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;YACrD;;;;;qDAKyC;YACzC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,MAAM,QAAQ,GAAW,IAAI,CAAC,WAAW,CAAC;YAC1C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAClD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAC5B,WAAW,CAAC,EAAE;gBACZ,IAAI,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE,CAAC;oBACvC,OAAO;gBACT,CAAC;gBACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;gBACjC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;gBACrB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gBACpB,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;oBACpD,SAAS,EAAE,CAAC,OAAO,CAAC;iBACrB,CAAC,CAAC,CAAC;gBACJ,MAAM,kBAAkB,GACtB,GAAG;oBACH,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;oBAC9D,GAAG,CAAC;gBACN,KAAK,CACH,gCAAgC;oBAC9B,IAAA,wBAAW,EAAC,IAAI,CAAC,MAAM,CAAC;oBACxB,IAAI;oBACJ,kBAAkB,CACrB,CAAC;gBACF,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACzC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;oBACnD,OAAO;gBACT,CAAC;gBACD;;;kFAGkE;gBAClE,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAClC,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,wBAAwB,EAC7B,IAAI,EACJ,EAAE,CACH,CAAC;YACJ,CAAC,EACD,GAAG,CAAC,EAAE;gBACJ,IAAI,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE,CAAC;oBACvC,OAAO;gBACT,CAAC;gBACD,KAAK,CACH,8BAA8B;oBAC5B,IAAA,wBAAW,EAAC,IAAI,CAAC,MAAM,CAAC;oBACxB,IAAI;oBACH,GAAa,CAAC,OAAO,CACzB,CAAC;gBACF,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;gBACjC,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACrD,CAAC,CACF,CAAC;YACF;8CACkC;YAClC,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;gBACnE;;kCAEkB;gBAClB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACnD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CACzB,SAAS,CAAC,EAAE;oBACV,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;wBACpC,OAAO;oBACT,CAAC;oBACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBAC9B,IAAI,CAAC;wBACH,IAAI,CAAC,mBAAmB,GAAG,IAAA,8CAA6B,EACtD,SAAS,EACT,IAAI,CAAC,UAAU,CAChB,CAAC;oBACJ,CAAC;oBAAC,OAAO,GAAG,EAAE,CAAC;wBACb,IAAI,CAAC,wBAAwB,GAAG;4BAC9B,IAAI,EAAE,kBAAM,CAAC,WAAW;4BACxB,OAAO,EAAE,4CACN,GAAa,CAAC,OACjB,EAAE;4BACF,QAAQ,EAAE,IAAI,mBAAQ,EAAE;yBACzB,CAAC;oBACJ,CAAC;oBACD,IAAI,IAAI,CAAC,kBAAkB,KAAK,IAAI,EAAE,CAAC;wBACrC;;;8EAGsD;wBACtD,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAClC,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,wBAAwB,EAC7B,IAAI,EACJ,EAAE,CACH,CAAC;oBACJ,CAAC;gBACH,CAAC,EACD,GAAG,CAAC,EAAE;oBACJ;;;;;;sEAMkD;gBACpD,CAAC,CACF,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,MAAM,CAAC,QAAgB;QACnC,IAAI,gDAAkC,EAAE,CAAC;YACvC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YAEzC,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC;gBACvC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC3C,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC;aAC5C,CAAC,CAAC;YAEH,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,UAAU,CAAC,EAAE,CAAC;gBAC1D,MAAM,IAAI,KAAK,CAAE,OAAO,CAAC,CAAC,CAA2B,CAAC,MAAM,CAAC,CAAC;YAChE,CAAC;YAED,OAAO,OAAO;iBACX,MAAM,CAAW,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBAChC,OAAO,MAAM,CAAC,MAAM,KAAK,WAAW;oBAClC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC;oBAC3B,CAAC,CAAC,GAAG,CAAC;YACV,CAAC,EAAE,EAAE,CAAC;iBACL,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACZ,IAAI,EAAE,IAAI;gBACV,IAAI,EAAE,CAAC,IAAI,CAAC,IAAK;aAClB,CAAC,CAAC,CAAC;QACR,CAAC;QAED;;;mEAG2D;QAC3D,MAAM,WAAW,GAAG,MAAM,cAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;QAC9D,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,IAAK,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,QAAgB;QACvC,IAAI,gDAAkC,EAAE,CAAC;YACvC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,cAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC;IAEO,wBAAwB;;QAC9B,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACvC,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,GAAG,EAAE;YACzC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,IAAI,CAAC,0BAA0B,EAAE,CAAC;YACpC,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACrC,MAAA,MAAA,IAAI,CAAC,mBAAmB,EAAC,KAAK,kDAAI,CAAC;QACnC,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;IAC3C,CAAC;IAEO,uBAAuB;QAC7B,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACvC,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;IAC5C,CAAC;IAEO,0BAA0B;QAChC,IAAI,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE,CAAC;YACvC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAED,gBAAgB;QACd;;;4DAGoD;QACpD,IAAI,IAAI,CAAC,oBAAoB,KAAK,IAAI,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,4BAA4B,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;gBAClE,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;oBACtC,KAAK,CACH,wEAAwE,CACzE,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,KAAK,CACH,mDAAmD;wBACjD,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE,CAC1C,CAAC;gBACJ,CAAC;gBACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,0BAA0B,EAAE,CAAC;YACpC,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,OAAO;QACL,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,mBAAmB,CAAC,MAAe;QACxC,OAAO,MAAM,CAAC,IAAI,CAAC;IACrB,CAAC;CACF;AAED;;;GAGG;AACH,SAAgB,KAAK;IACnB,IAAA,2BAAgB,EAAC,KAAK,EAAE,WAAW,CAAC,CAAC;IACrC,IAAA,gCAAqB,EAAC,KAAK,CAAC,CAAC;AAC/B,CAAC"}