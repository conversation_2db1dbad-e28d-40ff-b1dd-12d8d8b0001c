import React from "react";

const OrderModal = ({ isOpen, onClose, orderData }) => {
  if (!isOpen || !orderData) return null;

  const totalQuantity = orderData.product_items.reduce((sum, item) => sum + item.quantity, 0);
  const totalAmount = orderData.product_items.reduce((sum, item) => sum + item.item_price * item.quantity, 0);

  return (
    <div className="position-fixed text-black top-0 start-0 w-100 h-100 d-flex align-items-center 
    justify-content-center bg-dark bg-opacity-50"
    style={{zIndex:"1"}}
    >
      <div className="bg-white p-4 rounded shadow-lg" style={{ maxWidth: "600px", width: "90%" }}>
        <h2 className="h5 mb-3">Order Details</h2>
        <p><strong>Catalog ID:</strong> {orderData.catalog_id}</p>
        <table className="table table-bordered mt-3">
          <thead className="table-light">
            <tr>
              <th>Product ID</th>
              <th>Quantity</th>
              <th>Price</th>
              
            </tr>
          </thead>
          <tbody>
            {orderData.product_items.map((item, index) => (
              <tr key={index}>
                <td>{item.product_retailer_id}</td>
                <td>{item.quantity}</td>
                <td>{item.currency} {item.item_price}</td>
              </tr>
            ))}
             <tr className="table-light">
              <td><strong>Total</strong></td>
              <td><strong>{totalQuantity}</strong></td>
              <td><strong>{orderData.product_items[0]?.currency} {totalAmount}</strong></td>
            </tr>
          </tbody>
        </table>
        <button onClick={onClose} className="btn btn-primary mt-3">Close</button>
      </div>
    </div>
  );
};

export default OrderModal;
