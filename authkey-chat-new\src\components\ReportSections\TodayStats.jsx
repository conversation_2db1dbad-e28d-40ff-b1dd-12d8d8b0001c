import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, LabelList } from 'recharts';

const TodayStats = ({ labelData, liveChats, readChats, unreadChats, repeatedChats }) => {

    // Count occurrences of each label
    const labelCountMap = {};
    let noLabelCount = 0;
    labelData.forEach(chat => {
        if (chat.label && chat.label.length > 0) {
            chat.label.forEach(({ name, color_code }) => {
                if (labelCountMap[name]) {
                    labelCountMap[name].count += 1;
                } else {
                    labelCountMap[name] = { count: 1, color: color_code };
                }
            });
        } else {
            noLabelCount += 1; // Increment count for chats with no label
        }
    });

    // Convert label count to chart data
    const labelChartData = Object.keys(labelCountMap).map(name => ({
        name,
        value: labelCountMap[name].count,
        color: labelCountMap[name].color
    }));

    if (noLabelCount > 0) {
        labelChartData.push({ name: "No Label", value: noLabelCount, color: "#D3D3D3" }); // Gray color
    }

    // Prepare bar chart data
    const barChartData = [
        { name: 'Live Chats', value: liveChats.length },
        { name: 'Read Chats', value: readChats.length },
        { name: 'Unread Chats', value: unreadChats.length },
        { name: 'Repeated Chats', value: repeatedChats.length },
    ];

    // Prepare pie chart data
    const pieChartData = barChartData.map(item => ({ name: item.name, value: item.value }));

    // Define color palette for Pie Chart
    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A28CD9', '#FF6384'];

    const hasData = labelChartData.length > 0;

    return (
        <div className="d-flex bg-white flex-column flex-md-row  justify-content-between w-100 w-md-75 shadow" style={{ borderRadius: ".8rem" }}>
            {/* Bar Chart - Label Distribution */}
            <div className="bg-white p-2 shadow w-100 w-md-50" style={{ borderRadius: ".8rem" }}>
                <h6 className="text-center mt-2">Labels Distribution</h6>
                <ResponsiveContainer width="100%" height={300}>
                    {hasData ? (
                        <BarChart data={labelChartData} margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
                            {/* Grid lines, styling */}

                            <XAxis dataKey="name" />
                            <YAxis />
                            <Tooltip />
                            <Legend />

                            {/* Define a gradient */}
                            <defs>
                                <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                                    <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                                    <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                                </linearGradient>
                            </defs>

                            <Bar dataKey="value" fill="url(#colorValue)"
                                animationDuration={800}
                                animationEasing="ease-in-out"
                                radius={[8, 8, 0, 0]}>
                                {/* Optional: If you want labels on the bar */}
                                <LabelList dataKey="value" position="top" fill="#444" />
                            </Bar>
                        </BarChart>
                    ) : (
                        <p className="text-muted text-center">No data available</p>
                    )}
                </ResponsiveContainer>

            </div>

            {/* Pie Chart - Chat Statistics */}
            {/* <div className="bg-white p-4 rounded shadow w-100 w-md-50">
                <h4 className="text-center">Chat Statistics</h4>
                <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                        <Pie data={pieChartData} cx="50%" cy="50%" outerRadius={100} fill="#8884d8" dataKey="value" label>
                            {pieChartData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                        </Pie>
                        <Tooltip />
                        <Legend />
                    </PieChart>
                </ResponsiveContainer>
            </div> */}
        </div>
    );
};

export default TodayStats;
