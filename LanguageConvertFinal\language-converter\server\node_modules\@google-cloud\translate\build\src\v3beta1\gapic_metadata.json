{"schema": "1.0", "comment": "This file maps proto services/RPCs to the corresponding library clients/methods", "language": "typescript", "protoPackage": "google.cloud.translation.v3beta1", "libraryPackage": "@google-cloud/translate", "services": {"TranslationService": {"clients": {"grpc": {"libraryClient": "TranslationServiceClient", "rpcs": {"TranslateText": {"methods": ["translateText"]}, "DetectLanguage": {"methods": ["detectLanguage"]}, "GetSupportedLanguages": {"methods": ["getSupportedLanguages"]}, "TranslateDocument": {"methods": ["translateDocument"]}, "GetGlossary": {"methods": ["getGlossary"]}, "BatchTranslateText": {"methods": ["batchTranslateText"]}, "BatchTranslateDocument": {"methods": ["batchTranslateDocument"]}, "CreateGlossary": {"methods": ["createGlossary"]}, "DeleteGlossary": {"methods": ["deleteGlossary"]}, "ListGlossaries": {"methods": ["listGlossaries", "listGlossariesStream", "listGlossariesAsync"]}}}, "grpc-fallback": {"libraryClient": "TranslationServiceClient", "rpcs": {"TranslateText": {"methods": ["translateText"]}, "DetectLanguage": {"methods": ["detectLanguage"]}, "GetSupportedLanguages": {"methods": ["getSupportedLanguages"]}, "TranslateDocument": {"methods": ["translateDocument"]}, "GetGlossary": {"methods": ["getGlossary"]}, "BatchTranslateText": {"methods": ["batchTranslateText"]}, "BatchTranslateDocument": {"methods": ["batchTranslateDocument"]}, "CreateGlossary": {"methods": ["createGlossary"]}, "DeleteGlossary": {"methods": ["deleteGlossary"]}, "ListGlossaries": {"methods": ["listGlossaries", "listGlossariesStream", "listGlossariesAsync"]}}}}}}}