{"ast": null, "code": "var _jsxFileName = \"D:\\\\DataGen\\\\authkey-chat-new\\\\src\\\\components\\\\InputBar\\\\Inputbar.jsx\",\n    _s = $RefreshSig$();\n\nimport React from \"react\";\nimport styles from \"./inputBar.module.css\"; // Import the CSS module\n\nimport { useContext, useState, useEffect, useRef } from \"react\";\nimport { ChatContext } from \"../../context/ChatContext\";\nimport Input from \"../Input\";\nimport useSentences from \"../../customHooks/useSentences\";\nimport { ChatState } from \"../../context/AllProviders\";\nimport { FaBold } from \"react-icons/fa\";\nimport { FaItalic } from \"react-icons/fa\";\nimport { FaStrikethrough } from \"react-icons/fa\";\nimport ReplyPreview from \"../ReplyPreview/ReplyPreview\";\nimport BlockCard from \"../BlockCard\";\nimport { AuthContext } from \"../../context/AuthContext\";\nimport axios from \"axios\";\nimport { BASE_URL2 } from \"../../api/api\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n\nconst Inputbar = _ref => {\n  _s();\n\n  let {\n    setFaqOpen,\n    notes,\n    setshowNotesCard,\n    setShowContactDetail,\n    activeTab,\n    setActiveTab,\n    setshowQuickReply\n  } = _ref;\n  const {\n    data\n  } = useContext(ChatContext);\n  const {\n    text,\n    setText,\n    reply,\n    setReply,\n    selectedMobileNumber,\n    selectedUserDetails\n  } = ChatState();\n  const textareaRef = useRef(null);\n  const [showPreview, setShowPreview] = useState(false);\n  const {\n    currentUser\n  } = useContext(AuthContext);\n  const lastTypingTimeRef = useRef(0);\n  const {\n    filteredSentences,\n    saveSentences,\n    splitParagraphIntoSentences\n  } = useSentences();\n\n  const handleSuggestionClick = suggestedText => {\n    var _textareaRef$current;\n\n    setText(prevState => {\n      // Split the current input into words\n      const words = prevState.split(/\\s+/); // Check if the last word matches the suggestion\n\n      const lastWord = words[words.length - 1];\n      const suggestionFirstWord = suggestedText.split(/\\s+/)[0];\n\n      if (suggestionFirstWord.toLowerCase().includes(lastWord.toLowerCase())) {\n        // If the last word matches the suggestion, replace it\n        words[words.length - 1] = suggestedText;\n      } else {\n        // Otherwise, append the suggestion\n        words.push(suggestedText);\n      } // Join the words back into a single string and return\n\n\n      return words.join(\" \");\n    });\n    (_textareaRef$current = textareaRef.current) === null || _textareaRef$current === void 0 ? void 0 : _textareaRef$current.focus();\n  };\n\n  const handleTabClick = tabName => {\n    setActiveTab(tabName);\n\n    if (tabName === \"Note\") {\n      setshowNotesCard(prevState => {\n        const newState = !prevState; // Calculate the new state\n\n        if (!newState) {\n          // If the new state is `false`, reset the active tab to \"\"\n          setActiveTab(\"\");\n        }\n\n        return newState;\n      });\n      setShowContactDetail(false);\n      setFaqOpen(false);\n      setshowQuickReply(false);\n    } else if (tabName === \"quickReplies\") {\n      setshowQuickReply(prevState => {\n        const newState = !prevState; // Calculate the new state\n\n        if (!newState) {\n          // If the new state is `false`, reset the active tab to \"\"\n          setActiveTab(\"\");\n        }\n\n        return newState;\n      });\n      setShowContactDetail(false);\n      setFaqOpen(false);\n      setshowNotesCard(false);\n    }\n  };\n\n  const showTypingStatus = async () => {\n    const now = Date.now();\n\n    if (now - lastTypingTimeRef.current < 25000) {\n      return;\n    }\n\n    lastTypingTimeRef.current = now;\n    const payload = {\n      user_id: currentUser.user_id,\n      token: currentUser.token,\n      method: \"typing\",\n      user_type: currentUser.user_type,\n      mobile: selectedMobileNumber,\n      brand_number: currentUser.brand_number\n    };\n\n    try {\n      const {\n        data\n      } = await axios.post(`${BASE_URL2}/conversation`, payload);\n    } catch (error) {\n      if (error.response && error.response.status === 429) {\n        toast.error(\"Too many requests. Please try again later.\");\n      } else {\n        console.error(error);\n        toast.error(\"Something went wrong, please try again later\");\n      }\n    }\n  };\n\n  useEffect(() => {\n    if (!text || !currentUser.user_id || !currentUser.user_type || !currentUser.brand_number || !currentUser.token) return;\n    showTypingStatus();\n  }, [text, currentUser]);\n\n  const applyFormat = wrapSymbol => {\n    const input = textareaRef.current;\n    if (!input) return;\n    const startPos = input.selectionStart;\n    const endPos = input.selectionEnd;\n    const beforeSelection = text.substring(0, startPos);\n    const selectedText = text.substring(startPos, endPos);\n    const afterSelection = text.substring(endPos);\n\n    if (selectedText === \"\") {\n      const newText = beforeSelection + wrapSymbol + wrapSymbol + afterSelection;\n      setText(newText);\n      setShowPreview(true); // Move cursor between added symbols\n\n      setTimeout(() => {\n        input.focus();\n        input.setSelectionRange(startPos + wrapSymbol.length, startPos + wrapSymbol.length);\n      }, 10);\n    } else {\n      const newText = beforeSelection + wrapSymbol + selectedText + wrapSymbol + afterSelection;\n      setText(newText);\n      setShowPreview(true);\n      setTimeout(() => input.focus(), 10);\n    }\n  };\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.inputContainer,\n    children: selectedUserDetails.isBlock === 1 ? /*#__PURE__*/_jsxDEV(BlockCard, {\n      selectedMobileNumber: selectedMobileNumber,\n      selectedUserDetails: selectedUserDetails\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 44\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [reply && /*#__PURE__*/_jsxDEV(ReplyPreview, {\n        reply: reply,\n        onClose: () => setReply(null)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"d-flex justify-content-start align-items-center w-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleTabClick(\"Note\"),\n          className: `btn ${styles.button} ms-2 ${activeTab === \"Note\" ? styles.activeButton : \"\"}`,\n          children: [\"Notes \", notes.length > 0 ? `(${notes.length})` : \"\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleTabClick(\"quickReplies\"),\n          className: `btn ${styles.button} ms-2 ${activeTab === \"quickReplies\" ? styles.activeButton : \"\"}`,\n          children: \"Quick Replies\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-1 px-1\",\n          role: \"button\",\n          onClick: () => applyFormat(\"*\"),\n          children: /*#__PURE__*/_jsxDEV(FaBold, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-1 px-1\",\n          role: \"button\",\n          onClick: () => applyFormat(\"_\"),\n          children: /*#__PURE__*/_jsxDEV(FaItalic, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-1 px-1\",\n          role: \"button\",\n          onClick: () => applyFormat(\"~\"),\n          children: /*#__PURE__*/_jsxDEV(FaStrikethrough, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          class: \"dropdown\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            class: \"btn btn-secondary dropdown-toggle\",\n            type: \"button\",\n            \"data-bs-toggle\": \"dropdown\",\n            \"aria-expanded\": \"false\",\n            children: \"Language\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            class: \"dropdown-menu\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                class: \"dropdown-item\",\n                href: \"#\",\n                children: \"Action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                class: \"dropdown-item\",\n                href: \"#\",\n                children: \"Another action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                class: \"dropdown-item\",\n                href: \"#\",\n                children: \"Something else here\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 5\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 3\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this), filteredSentences.length ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.suggestionsContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n          className: styles.suggestionsList,\n          children: filteredSentences.map((sentence, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: styles.suggestionPill,\n            onClick: () => handleSuggestionClick(sentence),\n            children: sentence\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-none d-md-flex justify-content-center align-items-end w-100\",\n          style: {\n            fontSize: \".7rem\",\n            color: \"grey\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Press Tab to add text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 13\n      }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.textArea,\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          showPreview: showPreview,\n          setShowPreview: setShowPreview,\n          textareaRef: textareaRef,\n          handleSuggestionClick: handleSuggestionClick,\n          saveSentences: saveSentences,\n          splitParagraphIntoSentences: splitParagraphIntoSentences,\n          filteredSentences: filteredSentences,\n          selectedMobile: data.selectedMobile,\n          convData: data\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n};\n\n_s(Inputbar, \"8JSio8RBIh0+E46ChfbZiRsqIPQ=\", false, function () {\n  return [useSentences];\n});\n\n_c = Inputbar;\nexport default Inputbar;\n\nvar _c;\n\n$RefreshReg$(_c, \"Inputbar\");", "map": {"version": 3, "sources": ["D:/DataGen/authkey-chat-new/src/components/InputBar/Inputbar.jsx"], "names": ["React", "styles", "useContext", "useState", "useEffect", "useRef", "ChatContext", "Input", "useSentences", "ChatState", "FaBold", "FaItalic", "FaStrikethrough", "ReplyPreview", "BlockCard", "AuthContext", "axios", "BASE_URL2", "toast", "Inputbar", "setFaqOpen", "notes", "setshowNotesCard", "setShowContactDetail", "activeTab", "setActiveTab", "setshowQuickReply", "data", "text", "setText", "reply", "setReply", "selectedMobileNumber", "selectedUserDetails", "textareaRef", "showPreview", "setShowPreview", "currentUser", "lastTypingTimeRef", "filteredSentences", "saveSentences", "splitParagraphIntoSentences", "handleSuggestionClick", "suggestedText", "prevState", "words", "split", "lastWord", "length", "suggestionFirstWord", "toLowerCase", "includes", "push", "join", "current", "focus", "handleTabClick", "tabName", "newState", "showTypingStatus", "now", "Date", "payload", "user_id", "token", "method", "user_type", "mobile", "brand_number", "post", "error", "response", "status", "console", "applyFormat", "wrapSymbol", "input", "startPos", "selectionStart", "endPos", "selectionEnd", "beforeSelection", "substring", "selectedText", "afterSelection", "newText", "setTimeout", "setSelectionRange", "inputContainer", "isBlock", "button", "activeButton", "<PERSON><PERSON><PERSON><PERSON>", "suggestionsList", "map", "sentence", "index", "suggestionPill", "fontSize", "color", "textArea", "selected<PERSON><PERSON><PERSON>"], "mappings": ";;;AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,OAAOC,MAAP,MAAmB,uBAAnB,C,CAA4C;;AAC5C,SAASC,UAAT,EAAqBC,QAArB,EAA+BC,SAA/B,EAA0CC,MAA1C,QAAwD,OAAxD;AACA,SAASC,WAAT,QAA4B,2BAA5B;AACA,OAAOC,KAAP,MAAkB,UAAlB;AACA,OAAOC,YAAP,MAAyB,gCAAzB;AACA,SAASC,SAAT,QAA0B,4BAA1B;AACA,SAASC,MAAT,QAAuB,gBAAvB;AACA,SAASC,QAAT,QAAyB,gBAAzB;AACA,SAASC,eAAT,QAAgC,gBAAhC;AACA,OAAOC,YAAP,MAAyB,8BAAzB;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,SAASC,WAAT,QAA4B,2BAA5B;AACA,OAAOC,KAAP,MAAkB,OAAlB;AACA,SAASC,SAAT,QAA0B,eAA1B;AACA,SAASC,KAAT,QAAsB,gBAAtB;;;;AACA,MAAMC,QAAQ,GAAG,QASX;AAAA;;AAAA,MATY;AAChBC,IAAAA,UADgB;AAEhBC,IAAAA,KAFgB;AAGhBC,IAAAA,gBAHgB;AAIhBC,IAAAA,oBAJgB;AAKhBC,IAAAA,SALgB;AAMhBC,IAAAA,YANgB;AAOhBC,IAAAA;AAPgB,GASZ;AACJ,QAAM;AAAEC,IAAAA;AAAF,MAAWzB,UAAU,CAACI,WAAD,CAA3B;AACA,QAAM;AAAEsB,IAAAA,IAAF;AAAQC,IAAAA,OAAR;AAAiBC,IAAAA,KAAjB;AAAwBC,IAAAA,QAAxB;AAAkCC,IAAAA,oBAAlC;AAAwDC,IAAAA;AAAxD,MAAgFxB,SAAS,EAA/F;AACA,QAAMyB,WAAW,GAAG7B,MAAM,CAAC,IAAD,CAA1B;AACA,QAAM,CAAC8B,WAAD,EAAcC,cAAd,IAAgCjC,QAAQ,CAAC,KAAD,CAA9C;AACA,QAAM;AAAEkC,IAAAA;AAAF,MAAkBnC,UAAU,CAACa,WAAD,CAAlC;AACA,QAAMuB,iBAAiB,GAAGjC,MAAM,CAAC,CAAD,CAAhC;AAGA,QAAM;AAAEkC,IAAAA,iBAAF;AAAqBC,IAAAA,aAArB;AAAoCC,IAAAA;AAApC,MACJjC,YAAY,EADd;;AAGA,QAAMkC,qBAAqB,GAAIC,aAAD,IAAmB;AAAA;;AAC/Cd,IAAAA,OAAO,CAAEe,SAAD,IAAe;AACrB;AACA,YAAMC,KAAK,GAAGD,SAAS,CAACE,KAAV,CAAgB,KAAhB,CAAd,CAFqB,CAIrB;;AACA,YAAMC,QAAQ,GAAGF,KAAK,CAACA,KAAK,CAACG,MAAN,GAAe,CAAhB,CAAtB;AACA,YAAMC,mBAAmB,GAAGN,aAAa,CAACG,KAAd,CAAoB,KAApB,EAA2B,CAA3B,CAA5B;;AAEA,UAAIG,mBAAmB,CAACC,WAApB,GAAkCC,QAAlC,CAA2CJ,QAAQ,CAACG,WAAT,EAA3C,CAAJ,EAAwE;AACtE;AACAL,QAAAA,KAAK,CAACA,KAAK,CAACG,MAAN,GAAe,CAAhB,CAAL,GAA0BL,aAA1B;AACD,OAHD,MAGO;AACL;AACAE,QAAAA,KAAK,CAACO,IAAN,CAAWT,aAAX;AACD,OAdoB,CAerB;;;AACA,aAAOE,KAAK,CAACQ,IAAN,CAAW,GAAX,CAAP;AACD,KAjBM,CAAP;AAkBA,4BAAAnB,WAAW,CAACoB,OAAZ,8EAAqBC,KAArB;AACD,GApBD;;AAqBA,QAAMC,cAAc,GAAIC,OAAD,IAAa;AAClChC,IAAAA,YAAY,CAACgC,OAAD,CAAZ;;AAEA,QAAIA,OAAO,KAAK,MAAhB,EAAwB;AACtBnC,MAAAA,gBAAgB,CAAEsB,SAAD,IAAe;AAC9B,cAAMc,QAAQ,GAAG,CAACd,SAAlB,CAD8B,CACD;;AAC7B,YAAI,CAACc,QAAL,EAAe;AACb;AACAjC,UAAAA,YAAY,CAAC,EAAD,CAAZ;AACD;;AACD,eAAOiC,QAAP;AACD,OAPe,CAAhB;AAQAnC,MAAAA,oBAAoB,CAAC,KAAD,CAApB;AACAH,MAAAA,UAAU,CAAC,KAAD,CAAV;AACAM,MAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACD,KAZD,MAYO,IAAI+B,OAAO,KAAK,cAAhB,EAAgC;AACrC/B,MAAAA,iBAAiB,CAAEkB,SAAD,IAAe;AAC/B,cAAMc,QAAQ,GAAG,CAACd,SAAlB,CAD+B,CACF;;AAC7B,YAAI,CAACc,QAAL,EAAe;AACb;AACAjC,UAAAA,YAAY,CAAC,EAAD,CAAZ;AACD;;AACD,eAAOiC,QAAP;AACD,OAPgB,CAAjB;AAQAnC,MAAAA,oBAAoB,CAAC,KAAD,CAApB;AACAH,MAAAA,UAAU,CAAC,KAAD,CAAV;AACAE,MAAAA,gBAAgB,CAAC,KAAD,CAAhB;AACD;AACF,GA5BD;;AA+BA,QAAMqC,gBAAgB,GAAG,YAAY;AACnC,UAAMC,GAAG,GAAGC,IAAI,CAACD,GAAL,EAAZ;;AACA,QAAIA,GAAG,GAAGtB,iBAAiB,CAACgB,OAAxB,GAAkC,KAAtC,EAA6C;AAC3C;AACD;;AAEDhB,IAAAA,iBAAiB,CAACgB,OAAlB,GAA4BM,GAA5B;AACA,UAAME,OAAO,GAAG;AACdC,MAAAA,OAAO,EAAE1B,WAAW,CAAC0B,OADP;AAEdC,MAAAA,KAAK,EAAE3B,WAAW,CAAC2B,KAFL;AAGdC,MAAAA,MAAM,EAAE,QAHM;AAIdC,MAAAA,SAAS,EAAE7B,WAAW,CAAC6B,SAJT;AAKdC,MAAAA,MAAM,EAAEnC,oBALM;AAMdoC,MAAAA,YAAY,EAAE/B,WAAW,CAAC+B;AANZ,KAAhB;;AASA,QAAI;AAEF,YAAM;AAAEzC,QAAAA;AAAF,UAAW,MAAMX,KAAK,CAACqD,IAAN,CAAY,GAAEpD,SAAU,eAAxB,EAAwC6C,OAAxC,CAAvB;AAED,KAJD,CAIE,OAAOQ,KAAP,EAAc;AACd,UAAIA,KAAK,CAACC,QAAN,IAAkBD,KAAK,CAACC,QAAN,CAAeC,MAAf,KAA0B,GAAhD,EAAqD;AACnDtD,QAAAA,KAAK,CAACoD,KAAN,CAAY,4CAAZ;AAED,OAHD,MAGO;AACLG,QAAAA,OAAO,CAACH,KAAR,CAAcA,KAAd;AACApD,QAAAA,KAAK,CAACoD,KAAN,CAAY,8CAAZ;AACD;AACF;AACF,GA7BD;;AA+BAlE,EAAAA,SAAS,CAAC,MAAM;AACd,QAAI,CAACwB,IAAD,IAAS,CAACS,WAAW,CAAC0B,OAAtB,IACC,CAAC1B,WAAW,CAAC6B,SADd,IAC2B,CAAC7B,WAAW,CAAC+B,YADxC,IAEC,CAAC/B,WAAW,CAAC2B,KAFlB,EAEyB;AAEzBL,IAAAA,gBAAgB;AACjB,GANQ,EAMN,CAAC/B,IAAD,EAAOS,WAAP,CANM,CAAT;;AASA,QAAMqC,WAAW,GAAIC,UAAD,IAAgB;AAClC,UAAMC,KAAK,GAAG1C,WAAW,CAACoB,OAA1B;AACA,QAAI,CAACsB,KAAL,EAAY;AAEZ,UAAMC,QAAQ,GAAGD,KAAK,CAACE,cAAvB;AACA,UAAMC,MAAM,GAAGH,KAAK,CAACI,YAArB;AAEA,UAAMC,eAAe,GAAGrD,IAAI,CAACsD,SAAL,CAAe,CAAf,EAAkBL,QAAlB,CAAxB;AACA,UAAMM,YAAY,GAAGvD,IAAI,CAACsD,SAAL,CAAeL,QAAf,EAAyBE,MAAzB,CAArB;AACA,UAAMK,cAAc,GAAGxD,IAAI,CAACsD,SAAL,CAAeH,MAAf,CAAvB;;AAEA,QAAII,YAAY,KAAK,EAArB,EAAyB;AACvB,YAAME,OAAO,GAAGJ,eAAe,GAAGN,UAAlB,GAA+BA,UAA/B,GAA4CS,cAA5D;AACAvD,MAAAA,OAAO,CAACwD,OAAD,CAAP;AACAjD,MAAAA,cAAc,CAAC,IAAD,CAAd,CAHuB,CAKvB;;AACAkD,MAAAA,UAAU,CAAC,MAAM;AACfV,QAAAA,KAAK,CAACrB,KAAN;AACAqB,QAAAA,KAAK,CAACW,iBAAN,CAAwBV,QAAQ,GAAGF,UAAU,CAAC3B,MAA9C,EAAsD6B,QAAQ,GAAGF,UAAU,CAAC3B,MAA5E;AACD,OAHS,EAGP,EAHO,CAAV;AAID,KAVD,MAUO;AACL,YAAMqC,OAAO,GAAGJ,eAAe,GAAGN,UAAlB,GAA+BQ,YAA/B,GAA8CR,UAA9C,GAA2DS,cAA3E;AACAvD,MAAAA,OAAO,CAACwD,OAAD,CAAP;AACAjD,MAAAA,cAAc,CAAC,IAAD,CAAd;AAEAkD,MAAAA,UAAU,CAAC,MAAMV,KAAK,CAACrB,KAAN,EAAP,EAAsB,EAAtB,CAAV;AACD;AACF,GA5BD;;AA8BA,sBACE;AAAK,IAAA,SAAS,EAAEtD,MAAM,CAACuF,cAAvB;AAAA,cACGvD,mBAAmB,CAACwD,OAApB,KAAgC,CAAhC,gBAAoC,QAAC,SAAD;AAAW,MAAA,oBAAoB,EAAEzD,oBAAjC;AAAuD,MAAA,mBAAmB,EAAEC;AAA5E;AAAA;AAAA;AAAA;AAAA,YAApC,gBACC;AAAA,iBACGH,KAAK,iBAAI,QAAC,YAAD;AAAc,QAAA,KAAK,EAAEA,KAArB;AAA4B,QAAA,OAAO,EAAE,MAAMC,QAAQ,CAAC,IAAD;AAAnD;AAAA;AAAA;AAAA;AAAA,cADZ,eAEE;AAAQ,QAAA,SAAS,EAAC,uDAAlB;AAAA,gCACE;AACE,UAAA,OAAO,EAAE,MAAMyB,cAAc,CAAC,MAAD,CAD/B;AAEE,UAAA,SAAS,EAAG,OAAMvD,MAAM,CAACyF,MAAO,SAAQlE,SAAS,KAAK,MAAd,GAAuBvB,MAAM,CAAC0F,YAA9B,GAA6C,EAClF,EAHL;AAAA,+BAKStE,KAAK,CAAC2B,MAAN,GAAe,CAAf,GAAoB,IAAG3B,KAAK,CAAC2B,MAAO,GAApC,GAAyC,EALlD;AAAA;AAAA;AAAA;AAAA;AAAA,gBADF,eAQE;AACE,UAAA,OAAO,EAAE,MAAMQ,cAAc,CAAC,cAAD,CAD/B;AAEE,UAAA,SAAS,EAAG,OAAMvD,MAAM,CAACyF,MAAO,SAAQlE,SAAS,KAAK,cAAd,GAA+BvB,MAAM,CAAC0F,YAAtC,GAAqD,EAC1F,EAHL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBARF,eAeE;AACE,UAAA,SAAS,EAAC,WADZ;AAEE,UAAA,IAAI,EAAC,QAFP;AAGE,UAAA,OAAO,EAAE,MAAMjB,WAAW,CAAC,GAAD,CAH5B;AAAA,iCAKE,QAAC,MAAD;AAAA;AAAA;AAAA;AAAA;AALF;AAAA;AAAA;AAAA;AAAA,gBAfF,eAsBE;AACE,UAAA,SAAS,EAAC,WADZ;AAEE,UAAA,IAAI,EAAC,QAFP;AAGE,UAAA,OAAO,EAAE,MAAMA,WAAW,CAAC,GAAD,CAH5B;AAAA,iCAKE,QAAC,QAAD;AAAA;AAAA;AAAA;AAAA;AALF;AAAA;AAAA;AAAA;AAAA,gBAtBF,eA6BE;AACE,UAAA,SAAS,EAAC,WADZ;AAEE,UAAA,IAAI,EAAC,QAFP;AAGE,UAAA,OAAO,EAAE,MAAMA,WAAW,CAAC,GAAD,CAH5B;AAAA,iCAKE,QAAC,eAAD;AAAA;AAAA;AAAA;AAAA;AALF;AAAA;AAAA;AAAA;AAAA,gBA7BF,eAoCE;AAAK,UAAA,KAAK,EAAC,UAAX;AAAA,kCACV;AAAQ,YAAA,KAAK,EAAC,mCAAd;AAAkD,YAAA,IAAI,EAAC,QAAvD;AAAgE,8BAAe,UAA/E;AAA0F,6BAAc,OAAxG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBADU,eAIV;AAAI,YAAA,KAAK,EAAC,eAAV;AAAA,oCACE;AAAA,qCAAI;AAAG,gBAAA,KAAK,EAAC,eAAT;AAAyB,gBAAA,IAAI,EAAC,GAA9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAJ;AAAA;AAAA;AAAA;AAAA,oBADF,eAEE;AAAA,qCAAI;AAAG,gBAAA,KAAK,EAAC,eAAT;AAAyB,gBAAA,IAAI,EAAC,GAA9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAJ;AAAA;AAAA;AAAA;AAAA,oBAFF,eAGE;AAAA,qCAAI;AAAG,gBAAA,KAAK,EAAC,eAAT;AAAyB,gBAAA,IAAI,EAAC,GAA9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAJ;AAAA;AAAA;AAAA;AAAA,oBAHF;AAAA;AAAA;AAAA;AAAA;AAAA,kBAJU;AAAA;AAAA;AAAA;AAAA;AAAA,gBApCF;AAAA;AAAA;AAAA;AAAA;AAAA,cAFF,EAqDGnC,iBAAiB,CAACS,MAAlB,gBACC;AAAK,QAAA,SAAS,EAAE/C,MAAM,CAAC2F,oBAAvB;AAAA,gCACE;AAAI,UAAA,SAAS,EAAE3F,MAAM,CAAC4F,eAAtB;AAAA,oBACGtD,iBAAiB,CAACuD,GAAlB,CAAsB,CAACC,QAAD,EAAWC,KAAX,kBACrB;AAEE,YAAA,SAAS,EAAE/F,MAAM,CAACgG,cAFpB;AAGE,YAAA,OAAO,EAAE,MAAMvD,qBAAqB,CAACqD,QAAD,CAHtC;AAAA,sBAKGA;AALH,aACOC,KADP;AAAA;AAAA;AAAA;AAAA,kBADD;AADH;AAAA;AAAA;AAAA;AAAA,gBADF,eAYE;AACE,UAAA,SAAS,EAAC,+DADZ;AAEE,UAAA,KAAK,EAAE;AAAEE,YAAAA,QAAQ,EAAE,OAAZ;AAAqBC,YAAAA,KAAK,EAAE;AAA5B,WAFT;AAAA,iCAIE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJF;AAAA;AAAA;AAAA;AAAA,gBAZF;AAAA;AAAA;AAAA;AAAA;AAAA,cADD,GAoBG,IAzEN,eA2EE;AAAK,QAAA,SAAS,EAAElG,MAAM,CAACmG,QAAvB;AAAA,+BACE,QAAC,KAAD;AACE,UAAA,WAAW,EAAEjE,WADf;AAC4B,UAAA,cAAc,EAAEC,cAD5C;AAEE,UAAA,WAAW,EAAEF,WAFf;AAGE,UAAA,qBAAqB,EAAEQ,qBAHzB;AAIE,UAAA,aAAa,EAAEF,aAJjB;AAKE,UAAA,2BAA2B,EAAEC,2BAL/B;AAME,UAAA,iBAAiB,EAAEF,iBANrB;AAOE,UAAA,cAAc,EAAEZ,IAAI,CAAC0E,cAPvB;AAQE,UAAA,QAAQ,EAAE1E;AARZ;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,cA3EF;AAAA;AAFJ;AAAA;AAAA;AAAA;AAAA,UADF;AA8FD,CA7OD;;GAAMR,Q;UAmBFX,Y;;;KAnBEW,Q;AA+ON,eAAeA,QAAf", "sourcesContent": ["import React from \"react\";\r\nimport styles from \"./inputBar.module.css\"; // Import the CSS module\r\nimport { useContext, useState, useEffect, useRef } from \"react\";\r\nimport { ChatContext } from \"../../context/ChatContext\";\r\nimport Input from \"../Input\";\r\nimport useSentences from \"../../customHooks/useSentences\";\r\nimport { ChatState } from \"../../context/AllProviders\";\r\nimport { FaBold } from \"react-icons/fa\";\r\nimport { FaItalic } from \"react-icons/fa\";\r\nimport { FaStrikethrough } from \"react-icons/fa\";\r\nimport ReplyPreview from \"../ReplyPreview/ReplyPreview\";\r\nimport BlockCard from \"../BlockCard\";\r\nimport { AuthContext } from \"../../context/AuthContext\";\r\nimport axios from \"axios\";\r\nimport { BASE_URL2 } from \"../../api/api\";\r\nimport { toast } from \"react-toastify\";\r\nconst Inputbar = ({\r\n  setFaqOpen,\r\n  notes,\r\n  setshowNotesCard,\r\n  setShowContactDetail,\r\n  activeTab,\r\n  setActiveTab,\r\n  setshowQuickReply,\r\n\r\n}) => {\r\n  const { data } = useContext(ChatContext);\r\n  const { text, setText, reply, setReply, selectedMobileNumber, selectedUserDetails } = ChatState();\r\n  const textareaRef = useRef(null);\r\n  const [showPreview, setShowPreview] = useState(false);\r\n  const { currentUser } = useContext(AuthContext);\r\n  const lastTypingTimeRef = useRef(0);\r\n\r\n\r\n  const { filteredSentences, saveSentences, splitParagraphIntoSentences } =\r\n    useSentences();\r\n\r\n  const handleSuggestionClick = (suggestedText) => {\r\n    setText((prevState) => {\r\n      // Split the current input into words\r\n      const words = prevState.split(/\\s+/);\r\n\r\n      // Check if the last word matches the suggestion\r\n      const lastWord = words[words.length - 1];\r\n      const suggestionFirstWord = suggestedText.split(/\\s+/)[0];\r\n\r\n      if (suggestionFirstWord.toLowerCase().includes(lastWord.toLowerCase())) {\r\n        // If the last word matches the suggestion, replace it\r\n        words[words.length - 1] = suggestedText;\r\n      } else {\r\n        // Otherwise, append the suggestion\r\n        words.push(suggestedText);\r\n      }\r\n      // Join the words back into a single string and return\r\n      return words.join(\" \");\r\n    });\r\n    textareaRef.current?.focus();\r\n  };\r\n  const handleTabClick = (tabName) => {\r\n    setActiveTab(tabName);\r\n\r\n    if (tabName === \"Note\") {\r\n      setshowNotesCard((prevState) => {\r\n        const newState = !prevState; // Calculate the new state\r\n        if (!newState) {\r\n          // If the new state is `false`, reset the active tab to \"\"\r\n          setActiveTab(\"\");\r\n        }\r\n        return newState;\r\n      });\r\n      setShowContactDetail(false);\r\n      setFaqOpen(false);\r\n      setshowQuickReply(false);\r\n    } else if (tabName === \"quickReplies\") {\r\n      setshowQuickReply((prevState) => {\r\n        const newState = !prevState; // Calculate the new state\r\n        if (!newState) {\r\n          // If the new state is `false`, reset the active tab to \"\"\r\n          setActiveTab(\"\");\r\n        }\r\n        return newState;\r\n      });\r\n      setShowContactDetail(false);\r\n      setFaqOpen(false);\r\n      setshowNotesCard(false);\r\n    }\r\n  };\r\n\r\n\r\n  const showTypingStatus = async () => {\r\n    const now = Date.now();\r\n    if (now - lastTypingTimeRef.current < 25000) {\r\n      return;\r\n    }\r\n\r\n    lastTypingTimeRef.current = now;\r\n    const payload = {\r\n      user_id: currentUser.user_id,\r\n      token: currentUser.token,\r\n      method: \"typing\",\r\n      user_type: currentUser.user_type,\r\n      mobile: selectedMobileNumber,\r\n      brand_number: currentUser.brand_number,\r\n\r\n    }\r\n    try {\r\n\r\n      const { data } = await axios.post(`${BASE_URL2}/conversation`, payload);\r\n\r\n    } catch (error) {\r\n      if (error.response && error.response.status === 429) {\r\n        toast.error(\"Too many requests. Please try again later.\")\r\n\r\n      } else {\r\n        console.error(error);\r\n        toast.error(\"Something went wrong, please try again later\")\r\n      }\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (!text || !currentUser.user_id\r\n      || !currentUser.user_type || !currentUser.brand_number\r\n      || !currentUser.token) return;\r\n\r\n    showTypingStatus();\r\n  }, [text, currentUser]);\r\n\r\n\r\n  const applyFormat = (wrapSymbol) => {\r\n    const input = textareaRef.current;\r\n    if (!input) return;\r\n\r\n    const startPos = input.selectionStart;\r\n    const endPos = input.selectionEnd;\r\n\r\n    const beforeSelection = text.substring(0, startPos);\r\n    const selectedText = text.substring(startPos, endPos);\r\n    const afterSelection = text.substring(endPos);\r\n\r\n    if (selectedText === \"\") {\r\n      const newText = beforeSelection + wrapSymbol + wrapSymbol + afterSelection;\r\n      setText(newText);\r\n      setShowPreview(true);\r\n\r\n      // Move cursor between added symbols\r\n      setTimeout(() => {\r\n        input.focus();\r\n        input.setSelectionRange(startPos + wrapSymbol.length, startPos + wrapSymbol.length);\r\n      }, 10);\r\n    } else {\r\n      const newText = beforeSelection + wrapSymbol + selectedText + wrapSymbol + afterSelection;\r\n      setText(newText);\r\n      setShowPreview(true);\r\n\r\n      setTimeout(() => input.focus(), 10);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={styles.inputContainer}>\r\n      {selectedUserDetails.isBlock === 1 ? <BlockCard selectedMobileNumber={selectedMobileNumber} selectedUserDetails={selectedUserDetails} /> :\r\n        <>\r\n          {reply && <ReplyPreview reply={reply} onClose={() => setReply(null)} />}\r\n          <header className=\"d-flex justify-content-start align-items-center w-100\">\r\n            <button\r\n              onClick={() => handleTabClick(\"Note\")}\r\n              className={`btn ${styles.button} ms-2 ${activeTab === \"Note\" ? styles.activeButton : \"\"\r\n                }`}\r\n            >\r\n              Notes {notes.length > 0 ? `(${notes.length})` : \"\"}\r\n            </button>\r\n            <button\r\n              onClick={() => handleTabClick(\"quickReplies\")}\r\n              className={`btn ${styles.button} ms-2 ${activeTab === \"quickReplies\" ? styles.activeButton : \"\"\r\n                }`}\r\n            >\r\n              Quick Replies\r\n            </button>\r\n            <div\r\n              className=\"mx-1 px-1\"\r\n              role=\"button\"\r\n              onClick={() => applyFormat(\"*\")}\r\n            >\r\n              <FaBold />\r\n            </div>\r\n            <div\r\n              className=\"mx-1 px-1\"\r\n              role=\"button\"\r\n              onClick={() => applyFormat(\"_\")}\r\n            >\r\n              <FaItalic />\r\n            </div>\r\n            <div\r\n              className=\"mx-1 px-1\"\r\n              role=\"button\"\r\n              onClick={() => applyFormat(\"~\")}\r\n            >\r\n              <FaStrikethrough />\r\n            </div>\r\n            <div class=\"dropdown\">\r\n  <button class=\"btn btn-secondary dropdown-toggle\" type=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n    Language\r\n  </button>\r\n  <ul class=\"dropdown-menu\">\r\n    <li><a class=\"dropdown-item\" href=\"#\">Action</a></li>\r\n    <li><a class=\"dropdown-item\" href=\"#\">Another action</a></li>\r\n    <li><a class=\"dropdown-item\" href=\"#\">Something else here</a></li>\r\n  </ul>\r\n</div>\r\n          </header>\r\n\r\n\r\n\r\n          {/* Suggestion Section */}\r\n          {filteredSentences.length ? (\r\n            <div className={styles.suggestionsContainer}>\r\n              <ul className={styles.suggestionsList}>\r\n                {filteredSentences.map((sentence, index) => (\r\n                  <li\r\n                    key={index}\r\n                    className={styles.suggestionPill}\r\n                    onClick={() => handleSuggestionClick(sentence)}\r\n                  >\r\n                    {sentence}\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n              <div\r\n                className=\"d-none d-md-flex justify-content-center align-items-end w-100\"\r\n                style={{ fontSize: \".7rem\", color: \"grey\" }}\r\n              >\r\n                <div>Press Tab to add text</div>\r\n              </div>\r\n            </div>\r\n          ) : null}\r\n\r\n          <div className={styles.textArea}>\r\n            <Input\r\n              showPreview={showPreview} setShowPreview={setShowPreview}\r\n              textareaRef={textareaRef}\r\n              handleSuggestionClick={handleSuggestionClick}\r\n              saveSentences={saveSentences}\r\n              splitParagraphIntoSentences={splitParagraphIntoSentences}\r\n              filteredSentences={filteredSentences}\r\n              selectedMobile={data.selectedMobile}\r\n              convData={data}\r\n            />\r\n          </div>\r\n        </>\r\n      }\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Inputbar;\r\n"]}, "metadata": {}, "sourceType": "module"}