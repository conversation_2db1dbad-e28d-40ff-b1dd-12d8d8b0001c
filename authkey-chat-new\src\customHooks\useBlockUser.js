import { useState, useContext } from "react";
import axios from "axios";
import { toast } from "react-toastify";
import { BASE_URL2 } from "../api/api";
import { AuthContext } from "../context/AuthContext";
import { ChatState } from "../context/AllProviders";
const useBlockUser = () => {
    const [loading, setLoading] = useState(false);
    const { currentUser } = useContext(AuthContext);
    const { setSelectedUserDetails, setAllChats } = ChatState();

    const handleBlock = async (mobile, status) => {
        const payload = {
            user_id: currentUser.parent_id,
            token: currentUser.parent_token,
            brand_number: currentUser.brand_number,
            method: "block_user",
            mobile: mobile,
            status: status === 0 ? false : true,
        }

        try {
            setLoading(true);
            const { data } = await axios.post(`${BASE_URL2}/whatsapp_setting`, payload);
            if (data.success) {
                if (mobile) {
                    setSelectedUserDetails((prevState) => ({ ...prevState, isBlock: status }))
                }

                setAllChats((prevState) => prevState.map((item) => {
                    if (item.mobile === mobile) {
                        return { ...item, is_block: status }

                    }
                    return item;
                }))


                toast.success(`User ${status === 1 ? "blocked" : "unblocked"} successfully!`);
            } else {
                toast.error("Failed to update block status.");
            }
        } catch (error) {
            toast.error("An error occurred while updating block status.");
        } finally {
            setLoading(false);
        }
    };

    return { handleBlock, bLoading: loading };
};

export default useBlockUser;
