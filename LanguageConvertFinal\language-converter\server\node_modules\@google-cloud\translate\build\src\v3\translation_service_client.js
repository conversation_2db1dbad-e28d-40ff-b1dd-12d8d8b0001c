"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.TranslationServiceClient = void 0;
const jsonProtos = require("../../protos/protos.json");
const google_gax_1 = require("google-gax");
/**
 * Client JSON configuration object, loaded from
 * `src/v3/translation_service_client_config.json`.
 * This file defines retry strategy and timeouts for all API methods in this library.
 */
const gapicConfig = require("./translation_service_client_config.json");
const version = require('../../../package.json').version;
/**
 *  Provides natural language translation operations.
 * @class
 * @memberof v3
 */
class TranslationServiceClient {
    _terminated = false;
    _opts;
    _providedCustomServicePath;
    _gaxModule;
    _gaxGrpc;
    _protos;
    _defaults;
    _universeDomain;
    _servicePath;
    _log = google_gax_1.loggingUtils.log('translate');
    auth;
    descriptors = {
        page: {},
        stream: {},
        longrunning: {},
        batching: {},
    };
    warn;
    innerApiCalls;
    iamClient;
    locationsClient;
    pathTemplates;
    operationsClient;
    translationServiceStub;
    /**
     * Construct an instance of TranslationServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://cloud.google.com/docs/authentication/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new TranslationServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts, gaxInstance) {
        // Ensure that options include all the required fields.
        const staticMembers = this.constructor;
        if (opts?.universe_domain && opts?.universeDomain && opts?.universe_domain !== opts?.universeDomain) {
            throw new Error('Please set either universe_domain or universeDomain, but not both.');
        }
        const universeDomainEnvVar = (typeof process === 'object' && typeof process.env === 'object') ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN'] : undefined;
        this._universeDomain = opts?.universeDomain ?? opts?.universe_domain ?? universeDomainEnvVar ?? 'googleapis.com';
        this._servicePath = 'translate.' + this._universeDomain;
        const servicePath = opts?.servicePath || opts?.apiEndpoint || this._servicePath;
        this._providedCustomServicePath = !!(opts?.servicePath || opts?.apiEndpoint);
        const port = opts?.port || staticMembers.port;
        const clientConfig = opts?.clientConfig ?? {};
        const fallback = opts?.fallback ?? (typeof window !== 'undefined' && typeof window?.fetch === 'function');
        opts = Object.assign({ servicePath, port, clientConfig, fallback }, opts);
        // Request numeric enum values if REST transport is used.
        opts.numericEnums = true;
        // If scopes are unset in options and we're connecting to a non-default endpoint, set scopes just in case.
        if (servicePath !== this._servicePath && !('scopes' in opts)) {
            opts['scopes'] = staticMembers.scopes;
        }
        // Load google-gax module synchronously if needed
        if (!gaxInstance) {
            gaxInstance = require('google-gax');
        }
        // Choose either gRPC or proto-over-HTTP implementation of google-gax.
        this._gaxModule = opts.fallback ? gaxInstance.fallback : gaxInstance;
        // Create a `gaxGrpc` object, with any grpc-specific options sent to the client.
        this._gaxGrpc = new this._gaxModule.GrpcClient(opts);
        // Save options to use in initialize() method.
        this._opts = opts;
        // Save the auth object to the client, for use by other methods.
        this.auth = this._gaxGrpc.auth;
        // Set useJWTAccessWithScope on the auth object.
        this.auth.useJWTAccessWithScope = true;
        // Set defaultServicePath on the auth object.
        this.auth.defaultServicePath = this._servicePath;
        // Set the default scopes in auth client if needed.
        if (servicePath === this._servicePath) {
            this.auth.defaultScopes = staticMembers.scopes;
        }
        this.iamClient = new this._gaxModule.IamClient(this._gaxGrpc, opts);
        this.locationsClient = new this._gaxModule.LocationsClient(this._gaxGrpc, opts);
        // Determine the client header string.
        const clientHeader = [
            `gax/${this._gaxModule.version}`,
            `gapic/${version}`,
        ];
        if (typeof process === 'object' && 'versions' in process) {
            clientHeader.push(`gl-node/${process.versions.node}`);
        }
        else {
            clientHeader.push(`gl-web/${this._gaxModule.version}`);
        }
        if (!opts.fallback) {
            clientHeader.push(`grpc/${this._gaxGrpc.grpcVersion}`);
        }
        else {
            clientHeader.push(`rest/${this._gaxGrpc.grpcVersion}`);
        }
        if (opts.libName && opts.libVersion) {
            clientHeader.push(`${opts.libName}/${opts.libVersion}`);
        }
        // Load the applicable protos.
        this._protos = this._gaxGrpc.loadProtoJSON(jsonProtos);
        // This API contains "path templates"; forward-slash-separated
        // identifiers to uniquely identify resources within the API.
        // Create useful helper objects for these.
        this.pathTemplates = {
            adaptiveMtDatasetPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}'),
            adaptiveMtFilePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}'),
            adaptiveMtSentencePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}/adaptiveMtSentences/{sentence}'),
            datasetPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/datasets/{dataset}'),
            examplePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/datasets/{dataset}/examples/{example}'),
            glossaryPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/glossaries/{glossary}'),
            glossaryEntryPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/glossaries/{glossary}/glossaryEntries/{glossary_entry}'),
            locationPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}'),
            modelPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/models/{model}'),
        };
        // Some of the methods on this service return "paged" results,
        // (e.g. 50 results at a time, with tokens to get subsequent
        // pages). Denote the keys used for pagination and results.
        this.descriptors.page = {
            listGlossaries: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'glossaries'),
            listGlossaryEntries: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'glossaryEntries'),
            listDatasets: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'datasets'),
            listAdaptiveMtDatasets: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'adaptiveMtDatasets'),
            listAdaptiveMtFiles: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'adaptiveMtFiles'),
            listAdaptiveMtSentences: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'adaptiveMtSentences'),
            listExamples: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'examples'),
            listModels: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'models')
        };
        const protoFilesRoot = this._gaxModule.protobufFromJSON(jsonProtos);
        // This API contains "long-running operations", which return a
        // an Operation object that allows for tracking of the operation,
        // rather than holding a request open.
        const lroOptions = {
            auth: this.auth,
            grpc: 'grpc' in this._gaxGrpc ? this._gaxGrpc.grpc : undefined
        };
        if (opts.fallback) {
            lroOptions.protoJson = protoFilesRoot;
            lroOptions.httpRules = [{ selector: 'google.cloud.location.Locations.GetLocation', get: '/v3/{name=projects/*/locations/*}', }, { selector: 'google.cloud.location.Locations.ListLocations', get: '/v3/{name=projects/*}/locations', }, { selector: 'google.longrunning.Operations.CancelOperation', post: '/v3/{name=projects/*/locations/*/operations/*}:cancel', body: '*', }, { selector: 'google.longrunning.Operations.DeleteOperation', delete: '/v3/{name=projects/*/locations/*/operations/*}', }, { selector: 'google.longrunning.Operations.GetOperation', get: '/v3/{name=projects/*/locations/*/operations/*}', }, { selector: 'google.longrunning.Operations.ListOperations', get: '/v3/{name=projects/*/locations/*}/operations', }, { selector: 'google.longrunning.Operations.WaitOperation', post: '/v3/{name=projects/*/locations/*/operations/*}:wait', body: '*', }];
        }
        this.operationsClient = this._gaxModule.lro(lroOptions).operationsClient(opts);
        const batchTranslateTextResponse = protoFilesRoot.lookup('.google.cloud.translation.v3.BatchTranslateResponse');
        const batchTranslateTextMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3.BatchTranslateMetadata');
        const batchTranslateDocumentResponse = protoFilesRoot.lookup('.google.cloud.translation.v3.BatchTranslateDocumentResponse');
        const batchTranslateDocumentMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3.BatchTranslateDocumentMetadata');
        const createGlossaryResponse = protoFilesRoot.lookup('.google.cloud.translation.v3.Glossary');
        const createGlossaryMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3.CreateGlossaryMetadata');
        const updateGlossaryResponse = protoFilesRoot.lookup('.google.cloud.translation.v3.Glossary');
        const updateGlossaryMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3.UpdateGlossaryMetadata');
        const deleteGlossaryResponse = protoFilesRoot.lookup('.google.cloud.translation.v3.DeleteGlossaryResponse');
        const deleteGlossaryMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3.DeleteGlossaryMetadata');
        const createDatasetResponse = protoFilesRoot.lookup('.google.cloud.translation.v3.Dataset');
        const createDatasetMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3.CreateDatasetMetadata');
        const deleteDatasetResponse = protoFilesRoot.lookup('.google.protobuf.Empty');
        const deleteDatasetMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3.DeleteDatasetMetadata');
        const importDataResponse = protoFilesRoot.lookup('.google.protobuf.Empty');
        const importDataMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3.ImportDataMetadata');
        const exportDataResponse = protoFilesRoot.lookup('.google.protobuf.Empty');
        const exportDataMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3.ExportDataMetadata');
        const createModelResponse = protoFilesRoot.lookup('.google.cloud.translation.v3.Model');
        const createModelMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3.CreateModelMetadata');
        const deleteModelResponse = protoFilesRoot.lookup('.google.protobuf.Empty');
        const deleteModelMetadata = protoFilesRoot.lookup('.google.cloud.translation.v3.DeleteModelMetadata');
        this.descriptors.longrunning = {
            batchTranslateText: new this._gaxModule.LongrunningDescriptor(this.operationsClient, batchTranslateTextResponse.decode.bind(batchTranslateTextResponse), batchTranslateTextMetadata.decode.bind(batchTranslateTextMetadata)),
            batchTranslateDocument: new this._gaxModule.LongrunningDescriptor(this.operationsClient, batchTranslateDocumentResponse.decode.bind(batchTranslateDocumentResponse), batchTranslateDocumentMetadata.decode.bind(batchTranslateDocumentMetadata)),
            createGlossary: new this._gaxModule.LongrunningDescriptor(this.operationsClient, createGlossaryResponse.decode.bind(createGlossaryResponse), createGlossaryMetadata.decode.bind(createGlossaryMetadata)),
            updateGlossary: new this._gaxModule.LongrunningDescriptor(this.operationsClient, updateGlossaryResponse.decode.bind(updateGlossaryResponse), updateGlossaryMetadata.decode.bind(updateGlossaryMetadata)),
            deleteGlossary: new this._gaxModule.LongrunningDescriptor(this.operationsClient, deleteGlossaryResponse.decode.bind(deleteGlossaryResponse), deleteGlossaryMetadata.decode.bind(deleteGlossaryMetadata)),
            createDataset: new this._gaxModule.LongrunningDescriptor(this.operationsClient, createDatasetResponse.decode.bind(createDatasetResponse), createDatasetMetadata.decode.bind(createDatasetMetadata)),
            deleteDataset: new this._gaxModule.LongrunningDescriptor(this.operationsClient, deleteDatasetResponse.decode.bind(deleteDatasetResponse), deleteDatasetMetadata.decode.bind(deleteDatasetMetadata)),
            importData: new this._gaxModule.LongrunningDescriptor(this.operationsClient, importDataResponse.decode.bind(importDataResponse), importDataMetadata.decode.bind(importDataMetadata)),
            exportData: new this._gaxModule.LongrunningDescriptor(this.operationsClient, exportDataResponse.decode.bind(exportDataResponse), exportDataMetadata.decode.bind(exportDataMetadata)),
            createModel: new this._gaxModule.LongrunningDescriptor(this.operationsClient, createModelResponse.decode.bind(createModelResponse), createModelMetadata.decode.bind(createModelMetadata)),
            deleteModel: new this._gaxModule.LongrunningDescriptor(this.operationsClient, deleteModelResponse.decode.bind(deleteModelResponse), deleteModelMetadata.decode.bind(deleteModelMetadata))
        };
        // Put together the default options sent with requests.
        this._defaults = this._gaxGrpc.constructSettings('google.cloud.translation.v3.TranslationService', gapicConfig, opts.clientConfig || {}, { 'x-goog-api-client': clientHeader.join(' ') });
        // Set up a dictionary of "inner API calls"; the core implementation
        // of calling the API is handled in `google-gax`, with this code
        // merely providing the destination and request information.
        this.innerApiCalls = {};
        // Add a warn function to the client constructor so it can be easily tested.
        this.warn = this._gaxModule.warn;
    }
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize() {
        // If the client stub promise is already initialized, return immediately.
        if (this.translationServiceStub) {
            return this.translationServiceStub;
        }
        // Put together the "service stub" for
        // google.cloud.translation.v3.TranslationService.
        this.translationServiceStub = this._gaxGrpc.createStub(this._opts.fallback ?
            this._protos.lookupService('google.cloud.translation.v3.TranslationService') :
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            this._protos.google.cloud.translation.v3.TranslationService, this._opts, this._providedCustomServicePath);
        // Iterate over each of the methods that the service provides
        // and create an API call method for each.
        const translationServiceStubMethods = ['translateText', 'romanizeText', 'detectLanguage', 'getSupportedLanguages', 'translateDocument', 'batchTranslateText', 'batchTranslateDocument', 'createGlossary', 'updateGlossary', 'listGlossaries', 'getGlossary', 'deleteGlossary', 'getGlossaryEntry', 'listGlossaryEntries', 'createGlossaryEntry', 'updateGlossaryEntry', 'deleteGlossaryEntry', 'createDataset', 'getDataset', 'listDatasets', 'deleteDataset', 'createAdaptiveMtDataset', 'deleteAdaptiveMtDataset', 'getAdaptiveMtDataset', 'listAdaptiveMtDatasets', 'adaptiveMtTranslate', 'getAdaptiveMtFile', 'deleteAdaptiveMtFile', 'importAdaptiveMtFile', 'listAdaptiveMtFiles', 'listAdaptiveMtSentences', 'importData', 'exportData', 'listExamples', 'createModel', 'listModels', 'getModel', 'deleteModel'];
        for (const methodName of translationServiceStubMethods) {
            const callPromise = this.translationServiceStub.then(stub => (...args) => {
                if (this._terminated) {
                    return Promise.reject('The client has already been closed.');
                }
                const func = stub[methodName];
                return func.apply(stub, args);
            }, (err) => () => {
                throw err;
            });
            const descriptor = this.descriptors.page[methodName] ||
                this.descriptors.longrunning[methodName] ||
                undefined;
            const apiCall = this._gaxModule.createApiCall(callPromise, this._defaults[methodName], descriptor, this._opts.fallback);
            this.innerApiCalls[methodName] = apiCall;
        }
        return this.translationServiceStub;
    }
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath() {
        if (typeof process === 'object' && typeof process.emitWarning === 'function') {
            process.emitWarning('Static servicePath is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'translate.googleapis.com';
    }
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint() {
        if (typeof process === 'object' && typeof process.emitWarning === 'function') {
            process.emitWarning('Static apiEndpoint is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'translate.googleapis.com';
    }
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint() {
        return this._servicePath;
    }
    get universeDomain() {
        return this._universeDomain;
    }
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port() {
        return 443;
    }
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes() {
        return [
            'https://www.googleapis.com/auth/cloud-platform',
            'https://www.googleapis.com/auth/cloud-translation'
        ];
    }
    /**
     * Return the project ID used by this class.
     * @returns {Promise} A promise that resolves to string containing the project ID.
     */
    getProjectId(callback) {
        if (callback) {
            this.auth.getProjectId(callback);
            return;
        }
        return this.auth.getProjectId();
    }
    translateText(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('translateText request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('translateText response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.translateText(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('translateText response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    romanizeText(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('romanizeText request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('romanizeText response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.romanizeText(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('romanizeText response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    detectLanguage(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('detectLanguage request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('detectLanguage response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.detectLanguage(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('detectLanguage response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    getSupportedLanguages(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('getSupportedLanguages request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getSupportedLanguages response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.getSupportedLanguages(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getSupportedLanguages response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    translateDocument(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('translateDocument request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('translateDocument response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.translateDocument(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('translateDocument response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    getGlossary(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'name': request.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('getGlossary request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getGlossary response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.getGlossary(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getGlossary response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    getGlossaryEntry(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'name': request.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('getGlossaryEntry request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getGlossaryEntry response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.getGlossaryEntry(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getGlossaryEntry response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    createGlossaryEntry(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('createGlossaryEntry request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('createGlossaryEntry response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.createGlossaryEntry(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('createGlossaryEntry response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    updateGlossaryEntry(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'glossary_entry.name': request.glossaryEntry.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('updateGlossaryEntry request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('updateGlossaryEntry response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.updateGlossaryEntry(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('updateGlossaryEntry response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    deleteGlossaryEntry(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'name': request.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('deleteGlossaryEntry request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('deleteGlossaryEntry response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.deleteGlossaryEntry(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('deleteGlossaryEntry response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    getDataset(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'name': request.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('getDataset request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getDataset response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.getDataset(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getDataset response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    createAdaptiveMtDataset(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('createAdaptiveMtDataset request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('createAdaptiveMtDataset response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.createAdaptiveMtDataset(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('createAdaptiveMtDataset response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    deleteAdaptiveMtDataset(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'name': request.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('deleteAdaptiveMtDataset request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('deleteAdaptiveMtDataset response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.deleteAdaptiveMtDataset(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('deleteAdaptiveMtDataset response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    getAdaptiveMtDataset(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'name': request.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('getAdaptiveMtDataset request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getAdaptiveMtDataset response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.getAdaptiveMtDataset(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getAdaptiveMtDataset response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    adaptiveMtTranslate(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('adaptiveMtTranslate request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('adaptiveMtTranslate response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.adaptiveMtTranslate(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('adaptiveMtTranslate response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    getAdaptiveMtFile(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'name': request.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('getAdaptiveMtFile request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getAdaptiveMtFile response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.getAdaptiveMtFile(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getAdaptiveMtFile response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    deleteAdaptiveMtFile(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'name': request.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('deleteAdaptiveMtFile request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('deleteAdaptiveMtFile response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.deleteAdaptiveMtFile(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('deleteAdaptiveMtFile response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    importAdaptiveMtFile(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('importAdaptiveMtFile request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('importAdaptiveMtFile response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.importAdaptiveMtFile(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('importAdaptiveMtFile response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    getModel(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'name': request.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        this._log.info('getModel request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getModel response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls.getModel(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getModel response %j', response);
            return [response, options, rawResponse];
        }).catch((error) => {
            if (error && 'statusDetails' in error && error.statusDetails instanceof Array) {
                const protos = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
                error.statusDetails = (0, google_gax_1.decodeAnyProtosInArray)(error.statusDetails, protos);
            }
            throw error;
        });
    }
    batchTranslateText(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('batchTranslateText response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('batchTranslateText request %j', request);
        return this.innerApiCalls.batchTranslateText(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('batchTranslateText response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `batchTranslateText()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.batch_translate_text.js</caption>
     * region_tag:translate_v3_generated_TranslationService_BatchTranslateText_async
     */
    async checkBatchTranslateTextProgress(name) {
        this._log.info('batchTranslateText long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.batchTranslateText, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    batchTranslateDocument(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('batchTranslateDocument response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('batchTranslateDocument request %j', request);
        return this.innerApiCalls.batchTranslateDocument(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('batchTranslateDocument response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `batchTranslateDocument()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.batch_translate_document.js</caption>
     * region_tag:translate_v3_generated_TranslationService_BatchTranslateDocument_async
     */
    async checkBatchTranslateDocumentProgress(name) {
        this._log.info('batchTranslateDocument long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.batchTranslateDocument, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    createGlossary(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('createGlossary response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('createGlossary request %j', request);
        return this.innerApiCalls.createGlossary(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('createGlossary response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `createGlossary()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.create_glossary.js</caption>
     * region_tag:translate_v3_generated_TranslationService_CreateGlossary_async
     */
    async checkCreateGlossaryProgress(name) {
        this._log.info('createGlossary long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.createGlossary, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    updateGlossary(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'glossary.name': request.glossary.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('updateGlossary response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('updateGlossary request %j', request);
        return this.innerApiCalls.updateGlossary(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('updateGlossary response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `updateGlossary()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.update_glossary.js</caption>
     * region_tag:translate_v3_generated_TranslationService_UpdateGlossary_async
     */
    async checkUpdateGlossaryProgress(name) {
        this._log.info('updateGlossary long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.updateGlossary, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    deleteGlossary(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'name': request.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('deleteGlossary response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('deleteGlossary request %j', request);
        return this.innerApiCalls.deleteGlossary(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('deleteGlossary response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `deleteGlossary()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.delete_glossary.js</caption>
     * region_tag:translate_v3_generated_TranslationService_DeleteGlossary_async
     */
    async checkDeleteGlossaryProgress(name) {
        this._log.info('deleteGlossary long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.deleteGlossary, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    createDataset(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('createDataset response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('createDataset request %j', request);
        return this.innerApiCalls.createDataset(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('createDataset response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `createDataset()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.create_dataset.js</caption>
     * region_tag:translate_v3_generated_TranslationService_CreateDataset_async
     */
    async checkCreateDatasetProgress(name) {
        this._log.info('createDataset long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.createDataset, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    deleteDataset(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'name': request.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('deleteDataset response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('deleteDataset request %j', request);
        return this.innerApiCalls.deleteDataset(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('deleteDataset response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `deleteDataset()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.delete_dataset.js</caption>
     * region_tag:translate_v3_generated_TranslationService_DeleteDataset_async
     */
    async checkDeleteDatasetProgress(name) {
        this._log.info('deleteDataset long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.deleteDataset, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    importData(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'dataset': request.dataset ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('importData response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('importData request %j', request);
        return this.innerApiCalls.importData(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('importData response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `importData()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.import_data.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ImportData_async
     */
    async checkImportDataProgress(name) {
        this._log.info('importData long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.importData, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    exportData(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'dataset': request.dataset ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('exportData response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('exportData request %j', request);
        return this.innerApiCalls.exportData(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('exportData response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `exportData()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.export_data.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ExportData_async
     */
    async checkExportDataProgress(name) {
        this._log.info('exportData long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.exportData, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    createModel(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('createModel response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('createModel request %j', request);
        return this.innerApiCalls.createModel(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('createModel response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `createModel()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.create_model.js</caption>
     * region_tag:translate_v3_generated_TranslationService_CreateModel_async
     */
    async checkCreateModelProgress(name) {
        this._log.info('createModel long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.createModel, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    deleteModel(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'name': request.name ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('deleteModel response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('deleteModel request %j', request);
        return this.innerApiCalls.deleteModel(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('deleteModel response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `deleteModel()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.delete_model.js</caption>
     * region_tag:translate_v3_generated_TranslationService_DeleteModel_async
     */
    async checkDeleteModelProgress(name) {
        this._log.info('deleteModel long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.deleteModel, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    listGlossaries(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listGlossaries values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listGlossaries request %j', request);
        return this.innerApiCalls
            .listGlossaries(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listGlossaries values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listGlossaries`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the project from which to list all of the glossaries.
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server may return fewer glossaries than
     *   requested. If unspecified, the server picks an appropriate default.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of [ListGlossariesResponse.next_page_token]
     *   returned from the previous call to `ListGlossaries` method.
     *   The first page is returned if `page_token`is empty or missing.
     * @param {string} [request.filter]
     *   Optional. Filter specifying constraints of a list operation.
     *   Specify the constraint by the format of "key=value", where key must be
     *   "src" or "tgt", and the value must be a valid language code.
     *   For multiple restrictions, concatenate them by "AND" (uppercase only),
     *   such as: "src=en-US AND tgt=zh-CN". Notice that the exact match is used
     *   here, which means using 'en-US' and 'en' can lead to different results,
     *   which depends on the language code you used when you create the glossary.
     *   For the unidirectional glossaries, the "src" and "tgt" add restrictions
     *   on the source and target language code separately.
     *   For the equivalent term set glossaries, the "src" and/or "tgt" add
     *   restrictions on the term set.
     *   For example: "src=en-US AND tgt=zh-CN" will only pick the unidirectional
     *   glossaries which exactly match the source language code as "en-US" and the
     *   target language code "zh-CN", but all equivalent term set glossaries which
     *   contain "en-US" and "zh-CN" in their language set will be picked.
     *   If missing, no filtering is performed.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.Glossary|Glossary} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listGlossariesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listGlossariesStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listGlossaries'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listGlossaries stream %j', request);
        return this.descriptors.page.listGlossaries.createStream(this.innerApiCalls.listGlossaries, request, callSettings);
    }
    /**
     * Equivalent to `listGlossaries`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The name of the project from which to list all of the glossaries.
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server may return fewer glossaries than
     *   requested. If unspecified, the server picks an appropriate default.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of [ListGlossariesResponse.next_page_token]
     *   returned from the previous call to `ListGlossaries` method.
     *   The first page is returned if `page_token`is empty or missing.
     * @param {string} [request.filter]
     *   Optional. Filter specifying constraints of a list operation.
     *   Specify the constraint by the format of "key=value", where key must be
     *   "src" or "tgt", and the value must be a valid language code.
     *   For multiple restrictions, concatenate them by "AND" (uppercase only),
     *   such as: "src=en-US AND tgt=zh-CN". Notice that the exact match is used
     *   here, which means using 'en-US' and 'en' can lead to different results,
     *   which depends on the language code you used when you create the glossary.
     *   For the unidirectional glossaries, the "src" and "tgt" add restrictions
     *   on the source and target language code separately.
     *   For the equivalent term set glossaries, the "src" and/or "tgt" add
     *   restrictions on the term set.
     *   For example: "src=en-US AND tgt=zh-CN" will only pick the unidirectional
     *   glossaries which exactly match the source language code as "en-US" and the
     *   target language code "zh-CN", but all equivalent term set glossaries which
     *   contain "en-US" and "zh-CN" in their language set will be picked.
     *   If missing, no filtering is performed.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.Glossary|Glossary}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_glossaries.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListGlossaries_async
     */
    listGlossariesAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listGlossaries'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listGlossaries iterate %j', request);
        return this.descriptors.page.listGlossaries.asyncIterate(this.innerApiCalls['listGlossaries'], request, callSettings);
    }
    listGlossaryEntries(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listGlossaryEntries values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listGlossaryEntries request %j', request);
        return this.innerApiCalls
            .listGlossaryEntries(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listGlossaryEntries values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listGlossaryEntries`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The parent glossary resource name for listing the glossary's
     *   entries.
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server may return fewer glossary entries
     *   than requested. If unspecified, the server picks an appropriate default.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   [ListGlossaryEntriesResponse.next_page_token] returned from the previous
     *   call. The first page is returned if `page_token`is empty or missing.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.GlossaryEntry|GlossaryEntry} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listGlossaryEntriesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listGlossaryEntriesStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listGlossaryEntries'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listGlossaryEntries stream %j', request);
        return this.descriptors.page.listGlossaryEntries.createStream(this.innerApiCalls.listGlossaryEntries, request, callSettings);
    }
    /**
     * Equivalent to `listGlossaryEntries`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The parent glossary resource name for listing the glossary's
     *   entries.
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server may return fewer glossary entries
     *   than requested. If unspecified, the server picks an appropriate default.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   [ListGlossaryEntriesResponse.next_page_token] returned from the previous
     *   call. The first page is returned if `page_token`is empty or missing.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.GlossaryEntry|GlossaryEntry}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_glossary_entries.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListGlossaryEntries_async
     */
    listGlossaryEntriesAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listGlossaryEntries'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listGlossaryEntries iterate %j', request);
        return this.descriptors.page.listGlossaryEntries.asyncIterate(this.innerApiCalls['listGlossaryEntries'], request, callSettings);
    }
    listDatasets(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listDatasets values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listDatasets request %j', request);
        return this.innerApiCalls
            .listDatasets(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listDatasets values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listDatasets`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Name of the parent project. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server can return fewer results than
     *   requested.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results for the server to return.
     *   Typically obtained from next_page_token field in the response of a
     *   ListDatasets call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.Dataset|Dataset} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listDatasetsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listDatasetsStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listDatasets'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listDatasets stream %j', request);
        return this.descriptors.page.listDatasets.createStream(this.innerApiCalls.listDatasets, request, callSettings);
    }
    /**
     * Equivalent to `listDatasets`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Name of the parent project. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server can return fewer results than
     *   requested.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results for the server to return.
     *   Typically obtained from next_page_token field in the response of a
     *   ListDatasets call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.Dataset|Dataset}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_datasets.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListDatasets_async
     */
    listDatasetsAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listDatasets'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listDatasets iterate %j', request);
        return this.descriptors.page.listDatasets.asyncIterate(this.innerApiCalls['listDatasets'], request, callSettings);
    }
    listAdaptiveMtDatasets(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listAdaptiveMtDatasets values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listAdaptiveMtDatasets request %j', request);
        return this.innerApiCalls
            .listAdaptiveMtDatasets(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listAdaptiveMtDatasets values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listAdaptiveMtDatasets`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the project from which to list the Adaptive
     *   MT datasets. `projects/{project-number-or-id}/locations/{location-id}`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server may return fewer results than
     *   requested. If unspecified, the server picks an appropriate default.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   ListAdaptiveMtDatasetsResponse.next_page_token returned from the
     *   previous call to `ListAdaptiveMtDatasets` method. The first page is
     *   returned if `page_token`is empty or missing.
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the results of the request.
     *   Filter is not supported yet.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.AdaptiveMtDataset|AdaptiveMtDataset} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listAdaptiveMtDatasetsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listAdaptiveMtDatasetsStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listAdaptiveMtDatasets'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listAdaptiveMtDatasets stream %j', request);
        return this.descriptors.page.listAdaptiveMtDatasets.createStream(this.innerApiCalls.listAdaptiveMtDatasets, request, callSettings);
    }
    /**
     * Equivalent to `listAdaptiveMtDatasets`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the project from which to list the Adaptive
     *   MT datasets. `projects/{project-number-or-id}/locations/{location-id}`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server may return fewer results than
     *   requested. If unspecified, the server picks an appropriate default.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   ListAdaptiveMtDatasetsResponse.next_page_token returned from the
     *   previous call to `ListAdaptiveMtDatasets` method. The first page is
     *   returned if `page_token`is empty or missing.
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the results of the request.
     *   Filter is not supported yet.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.AdaptiveMtDataset|AdaptiveMtDataset}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_adaptive_mt_datasets.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListAdaptiveMtDatasets_async
     */
    listAdaptiveMtDatasetsAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listAdaptiveMtDatasets'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listAdaptiveMtDatasets iterate %j', request);
        return this.descriptors.page.listAdaptiveMtDatasets.asyncIterate(this.innerApiCalls['listAdaptiveMtDatasets'], request, callSettings);
    }
    listAdaptiveMtFiles(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listAdaptiveMtFiles values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listAdaptiveMtFiles request %j', request);
        return this.innerApiCalls
            .listAdaptiveMtFiles(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listAdaptiveMtFiles values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listAdaptiveMtFiles`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the project from which to list the Adaptive
     *   MT files.
     *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
     * @param {number} [request.pageSize]
     *   Optional.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   ListAdaptiveMtFilesResponse.next_page_token returned from the
     *   previous call to `ListAdaptiveMtFiles` method. The first page is
     *   returned if `page_token`is empty or missing.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.AdaptiveMtFile|AdaptiveMtFile} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listAdaptiveMtFilesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listAdaptiveMtFilesStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listAdaptiveMtFiles'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listAdaptiveMtFiles stream %j', request);
        return this.descriptors.page.listAdaptiveMtFiles.createStream(this.innerApiCalls.listAdaptiveMtFiles, request, callSettings);
    }
    /**
     * Equivalent to `listAdaptiveMtFiles`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the project from which to list the Adaptive
     *   MT files.
     *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
     * @param {number} [request.pageSize]
     *   Optional.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   ListAdaptiveMtFilesResponse.next_page_token returned from the
     *   previous call to `ListAdaptiveMtFiles` method. The first page is
     *   returned if `page_token`is empty or missing.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.AdaptiveMtFile|AdaptiveMtFile}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_adaptive_mt_files.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListAdaptiveMtFiles_async
     */
    listAdaptiveMtFilesAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listAdaptiveMtFiles'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listAdaptiveMtFiles iterate %j', request);
        return this.descriptors.page.listAdaptiveMtFiles.asyncIterate(this.innerApiCalls['listAdaptiveMtFiles'], request, callSettings);
    }
    listAdaptiveMtSentences(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listAdaptiveMtSentences values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listAdaptiveMtSentences request %j', request);
        return this.innerApiCalls
            .listAdaptiveMtSentences(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listAdaptiveMtSentences values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listAdaptiveMtSentences`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the project from which to list the Adaptive
     *   MT files. The following format lists all sentences under a file.
     *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}`
     *   The following format lists all sentences within a dataset.
     *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
     * @param {number} request.pageSize
     * @param {string} request.pageToken
     *   A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   ListAdaptiveMtSentencesRequest.next_page_token returned from the
     *   previous call to `ListTranslationMemories` method. The first page is
     *   returned if `page_token` is empty or missing.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.AdaptiveMtSentence|AdaptiveMtSentence} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listAdaptiveMtSentencesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listAdaptiveMtSentencesStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listAdaptiveMtSentences'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listAdaptiveMtSentences stream %j', request);
        return this.descriptors.page.listAdaptiveMtSentences.createStream(this.innerApiCalls.listAdaptiveMtSentences, request, callSettings);
    }
    /**
     * Equivalent to `listAdaptiveMtSentences`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the project from which to list the Adaptive
     *   MT files. The following format lists all sentences under a file.
     *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}`
     *   The following format lists all sentences within a dataset.
     *   `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`
     * @param {number} request.pageSize
     * @param {string} request.pageToken
     *   A token identifying a page of results the server should return.
     *   Typically, this is the value of
     *   ListAdaptiveMtSentencesRequest.next_page_token returned from the
     *   previous call to `ListTranslationMemories` method. The first page is
     *   returned if `page_token` is empty or missing.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.AdaptiveMtSentence|AdaptiveMtSentence}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_adaptive_mt_sentences.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListAdaptiveMtSentences_async
     */
    listAdaptiveMtSentencesAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listAdaptiveMtSentences'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listAdaptiveMtSentences iterate %j', request);
        return this.descriptors.page.listAdaptiveMtSentences.asyncIterate(this.innerApiCalls['listAdaptiveMtSentences'], request, callSettings);
    }
    listExamples(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listExamples values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listExamples request %j', request);
        return this.innerApiCalls
            .listExamples(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listExamples values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listExamples`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Name of the parent dataset. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}/datasets/{dataset-id}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the examples that will be returned.
     *   Example filter:
     *   * `usage=TRAIN`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server can return fewer results than
     *   requested.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results for the server to return.
     *   Typically obtained from next_page_token field in the response of a
     *   ListExamples call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.Example|Example} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listExamplesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listExamplesStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listExamples'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listExamples stream %j', request);
        return this.descriptors.page.listExamples.createStream(this.innerApiCalls.listExamples, request, callSettings);
    }
    /**
     * Equivalent to `listExamples`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Name of the parent dataset. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}/datasets/{dataset-id}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the examples that will be returned.
     *   Example filter:
     *   * `usage=TRAIN`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server can return fewer results than
     *   requested.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results for the server to return.
     *   Typically obtained from next_page_token field in the response of a
     *   ListExamples call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.Example|Example}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_examples.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListExamples_async
     */
    listExamplesAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listExamples'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listExamples iterate %j', request);
        return this.descriptors.page.listExamples.asyncIterate(this.innerApiCalls['listExamples'], request, callSettings);
    }
    listModels(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        this.initialize().catch(err => { throw err; });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listModels values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listModels request %j', request);
        return this.innerApiCalls
            .listModels(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listModels values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listModels`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Name of the parent project. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the models that will be returned.
     *   Supported filter:
     *   `dataset_id=${dataset_id}`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server can return fewer results than
     *   requested.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results for the server to return.
     *   Typically obtained from next_page_token field in the response of a
     *   ListModels call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.translation.v3.Model|Model} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listModelsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listModelsStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listModels'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listModels stream %j', request);
        return this.descriptors.page.listModels.createStream(this.innerApiCalls.listModels, request, callSettings);
    }
    /**
     * Equivalent to `listModels`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Name of the parent project. In form of
     *   `projects/{project-number-or-id}/locations/{location-id}`
     * @param {string} [request.filter]
     *   Optional. An expression for filtering the models that will be returned.
     *   Supported filter:
     *   `dataset_id=${dataset_id}`
     * @param {number} [request.pageSize]
     *   Optional. Requested page size. The server can return fewer results than
     *   requested.
     * @param {string} [request.pageToken]
     *   Optional. A token identifying a page of results for the server to return.
     *   Typically obtained from next_page_token field in the response of a
     *   ListModels call.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.translation.v3.Model|Model}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v3/translation_service.list_models.js</caption>
     * region_tag:translate_v3_generated_TranslationService_ListModels_async
     */
    listModelsAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] = this._gaxModule.routingHeader.fromParams({
            'parent': request.parent ?? '',
        });
        const defaultCallSettings = this._defaults['listModels'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => { throw err; });
        this._log.info('listModels iterate %j', request);
        return this.descriptors.page.listModels.asyncIterate(this.innerApiCalls['listModels'], request, callSettings);
    }
    /**
     * Gets the access control policy for a resource. Returns an empty policy
     * if the resource exists and does not have a policy set.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {Object} [request.options]
     *   OPTIONAL: A `GetPolicyOptions` object for specifying options to
     *   `GetIamPolicy`. This field is only used by Cloud IAM.
     *
     *   This object should have the same structure as {@link google.iam.v1.GetPolicyOptions | GetPolicyOptions}.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.Policy | Policy}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.Policy | Policy}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    getIamPolicy(request, options, callback) {
        return this.iamClient.getIamPolicy(request, options, callback);
    }
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     */
    setIamPolicy(request, options, callback) {
        return this.iamClient.setIamPolicy(request, options, callback);
    }
    /**
     * Returns permissions that a caller has on the specified resource. If the
     * resource does not exist, this will return an empty set of
     * permissions, not a NOT_FOUND error.
     *
     * Note: This operation is designed to be used for building
     * permission-aware UIs and command-line tools, not for authorization
     * checking. This operation may "fail open" without warning.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.resource
     *   REQUIRED: The resource for which the policy detail is being requested.
     *   See the operation documentation for the appropriate value for this field.
     * @param {string[]} request.permissions
     *   The set of permissions to check for the `resource`. Permissions with
     *   wildcards (such as '*' or 'storage.*') are not allowed. For more
     *   information see {@link https://cloud.google.com/iam/docs/overview#permissions | IAM Overview }.
     * @param {Object} [options]
     *   Optional parameters. You can override the default settings for this call, e.g, timeout,
     *   retries, paginations, etc. See {@link https://googleapis.github.io/gax-nodejs/interfaces/CallOptions.html | gax.CallOptions} for the details.
     * @param {function(?Error, ?Object)} [callback]
     *   The function which will be called with the result of the API call.
     *
     *   The second parameter to the callback is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.iam.v1.TestIamPermissionsResponse | TestIamPermissionsResponse}.
     *   The promise has a method named "cancel" which cancels the ongoing API call.
     *
     */
    testIamPermissions(request, options, callback) {
        return this.iamClient.testIamPermissions(request, options, callback);
    }
    /**
       * Gets information about a location.
       *
       * @param {Object} request
       *   The request object that will be sent.
       * @param {string} request.name
       *   Resource name for the location.
       * @param {object} [options]
       *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html | CallOptions} for more details.
       * @returns {Promise} - The promise which resolves to an array.
       *   The first element of the array is an object representing {@link google.cloud.location.Location | Location}.
       *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
       *   for more details and examples.
       * @example
       * ```
       * const [response] = await client.getLocation(request);
       * ```
       */
    getLocation(request, options, callback) {
        return this.locationsClient.getLocation(request, options, callback);
    }
    /**
       * Lists information about the supported locations for this service. Returns an iterable object.
       *
       * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
       * @param {Object} request
       *   The request object that will be sent.
       * @param {string} request.name
       *   The resource that owns the locations collection, if applicable.
       * @param {string} request.filter
       *   The standard list filter.
       * @param {number} request.pageSize
       *   The standard list page size.
       * @param {string} request.pageToken
       *   The standard list page token.
       * @param {object} [options]
       *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
       * @returns {Object}
       *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
       *   When you iterate the returned iterable, each element will be an object representing
       *   {@link google.cloud.location.Location | Location}. The API will be called under the hood as needed, once per the page,
       *   so you can stop the iteration when you don't need more results.
       *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
       *   for more details and examples.
       * @example
       * ```
       * const iterable = client.listLocationsAsync(request);
       * for await (const response of iterable) {
       *   // process response
       * }
       * ```
       */
    listLocationsAsync(request, options) {
        return this.locationsClient.listLocationsAsync(request, options);
    }
    /**
       * Gets the latest state of a long-running operation.  Clients can use this
       * method to poll the operation result at intervals as recommended by the API
       * service.
       *
       * @param {Object} request - The request object that will be sent.
       * @param {string} request.name - The name of the operation resource.
       * @param {Object=} options
       *   Optional parameters. You can override the default settings for this call,
       *   e.g, timeout, retries, paginations, etc. See {@link
       *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
       *   for the details.
       * @param {function(?Error, ?Object)=} callback
       *   The function which will be called with the result of the API call.
       *
       *   The second parameter to the callback is an object representing
       *   {@link google.longrunning.Operation | google.longrunning.Operation}.
       * @return {Promise} - The promise which resolves to an array.
       *   The first element of the array is an object representing
       * {@link google.longrunning.Operation | google.longrunning.Operation}.
       * The promise has a method named "cancel" which cancels the ongoing API call.
       *
       * @example
       * ```
       * const client = longrunning.operationsClient();
       * const name = '';
       * const [response] = await client.getOperation({name});
       * // doThingsWith(response)
       * ```
       */
    getOperation(request, optionsOrCallback, callback) {
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        return this.operationsClient.getOperation(request, options, callback);
    }
    /**
     * Lists operations that match the specified filter in the request. If the
     * server doesn't support this method, it returns `UNIMPLEMENTED`. Returns an iterable object.
     *
     * For-await-of syntax is used with the iterable to recursively get response element on-demand.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation collection.
     * @param {string} request.filter - The standard list filter.
     * @param {number=} request.pageSize -
     *   The maximum number of resources contained in the underlying API
     *   response. If page streaming is performed per-resource, this
     *   parameter does not affect the return value. If page streaming is
     *   performed per-page, this determines the maximum number of
     *   resources in a page.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     *   e.g, timeout, retries, paginations, etc. See {@link
     *   https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     *   details.
     * @returns {Object}
     *   An iterable Object that conforms to {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | iteration protocols}.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * for await (const response of client.listOperationsAsync(request));
     * // doThingsWith(response)
     * ```
     */
    listOperationsAsync(request, options) {
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        return this.operationsClient.listOperationsAsync(request, options);
    }
    /**
     * Starts asynchronous cancellation on a long-running operation.  The server
     * makes a best effort to cancel the operation, but success is not
     * guaranteed.  If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.  Clients can use
     * {@link Operations.GetOperation} or
     * other methods to check whether the cancellation succeeded or whether the
     * operation completed despite cancellation. On successful cancellation,
     * the operation is not deleted; instead, it becomes an operation with
     * an {@link Operation.error} value with a {@link google.rpc.Status.code} of
     * 1, corresponding to `Code.CANCELLED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be cancelled.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions} for the
     * details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.cancelOperation({name: ''});
     * ```
     */
    cancelOperation(request, optionsOrCallback, callback) {
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        return this.operationsClient.cancelOperation(request, options, callback);
    }
    /**
     * Deletes a long-running operation. This method indicates that the client is
     * no longer interested in the operation result. It does not cancel the
     * operation. If the server doesn't support this method, it returns
     * `google.rpc.Code.UNIMPLEMENTED`.
     *
     * @param {Object} request - The request object that will be sent.
     * @param {string} request.name - The name of the operation resource to be deleted.
     * @param {Object=} options
     *   Optional parameters. You can override the default settings for this call,
     * e.g, timeout, retries, paginations, etc. See {@link
     * https://googleapis.github.io/gax-nodejs/global.html#CallOptions | gax.CallOptions}
     * for the details.
     * @param {function(?Error)=} callback
     *   The function which will be called with the result of the API call.
     * @return {Promise} - The promise which resolves when API call finishes.
     *   The promise has a method named "cancel" which cancels the ongoing API
     * call.
     *
     * @example
     * ```
     * const client = longrunning.operationsClient();
     * await client.deleteOperation({name: ''});
     * ```
     */
    deleteOperation(request, optionsOrCallback, callback) {
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        return this.operationsClient.deleteOperation(request, options, callback);
    }
    // --------------------
    // -- Path templates --
    // --------------------
    /**
     * Return a fully-qualified adaptiveMtDataset resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @returns {string} Resource name string.
     */
    adaptiveMtDatasetPath(project, location, dataset) {
        return this.pathTemplates.adaptiveMtDatasetPathTemplate.render({
            project: project,
            location: location,
            dataset: dataset,
        });
    }
    /**
     * Parse the project from AdaptiveMtDataset resource.
     *
     * @param {string} adaptiveMtDatasetName
     *   A fully-qualified path representing AdaptiveMtDataset resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAdaptiveMtDatasetName(adaptiveMtDatasetName) {
        return this.pathTemplates.adaptiveMtDatasetPathTemplate.match(adaptiveMtDatasetName).project;
    }
    /**
     * Parse the location from AdaptiveMtDataset resource.
     *
     * @param {string} adaptiveMtDatasetName
     *   A fully-qualified path representing AdaptiveMtDataset resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAdaptiveMtDatasetName(adaptiveMtDatasetName) {
        return this.pathTemplates.adaptiveMtDatasetPathTemplate.match(adaptiveMtDatasetName).location;
    }
    /**
     * Parse the dataset from AdaptiveMtDataset resource.
     *
     * @param {string} adaptiveMtDatasetName
     *   A fully-qualified path representing AdaptiveMtDataset resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAdaptiveMtDatasetName(adaptiveMtDatasetName) {
        return this.pathTemplates.adaptiveMtDatasetPathTemplate.match(adaptiveMtDatasetName).dataset;
    }
    /**
     * Return a fully-qualified adaptiveMtFile resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} file
     * @returns {string} Resource name string.
     */
    adaptiveMtFilePath(project, location, dataset, file) {
        return this.pathTemplates.adaptiveMtFilePathTemplate.render({
            project: project,
            location: location,
            dataset: dataset,
            file: file,
        });
    }
    /**
     * Parse the project from AdaptiveMtFile resource.
     *
     * @param {string} adaptiveMtFileName
     *   A fully-qualified path representing AdaptiveMtFile resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAdaptiveMtFileName(adaptiveMtFileName) {
        return this.pathTemplates.adaptiveMtFilePathTemplate.match(adaptiveMtFileName).project;
    }
    /**
     * Parse the location from AdaptiveMtFile resource.
     *
     * @param {string} adaptiveMtFileName
     *   A fully-qualified path representing AdaptiveMtFile resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAdaptiveMtFileName(adaptiveMtFileName) {
        return this.pathTemplates.adaptiveMtFilePathTemplate.match(adaptiveMtFileName).location;
    }
    /**
     * Parse the dataset from AdaptiveMtFile resource.
     *
     * @param {string} adaptiveMtFileName
     *   A fully-qualified path representing AdaptiveMtFile resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAdaptiveMtFileName(adaptiveMtFileName) {
        return this.pathTemplates.adaptiveMtFilePathTemplate.match(adaptiveMtFileName).dataset;
    }
    /**
     * Parse the file from AdaptiveMtFile resource.
     *
     * @param {string} adaptiveMtFileName
     *   A fully-qualified path representing AdaptiveMtFile resource.
     * @returns {string} A string representing the file.
     */
    matchFileFromAdaptiveMtFileName(adaptiveMtFileName) {
        return this.pathTemplates.adaptiveMtFilePathTemplate.match(adaptiveMtFileName).file;
    }
    /**
     * Return a fully-qualified adaptiveMtSentence resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} file
     * @param {string} sentence
     * @returns {string} Resource name string.
     */
    adaptiveMtSentencePath(project, location, dataset, file, sentence) {
        return this.pathTemplates.adaptiveMtSentencePathTemplate.render({
            project: project,
            location: location,
            dataset: dataset,
            file: file,
            sentence: sentence,
        });
    }
    /**
     * Parse the project from AdaptiveMtSentence resource.
     *
     * @param {string} adaptiveMtSentenceName
     *   A fully-qualified path representing AdaptiveMtSentence resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromAdaptiveMtSentenceName(adaptiveMtSentenceName) {
        return this.pathTemplates.adaptiveMtSentencePathTemplate.match(adaptiveMtSentenceName).project;
    }
    /**
     * Parse the location from AdaptiveMtSentence resource.
     *
     * @param {string} adaptiveMtSentenceName
     *   A fully-qualified path representing AdaptiveMtSentence resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromAdaptiveMtSentenceName(adaptiveMtSentenceName) {
        return this.pathTemplates.adaptiveMtSentencePathTemplate.match(adaptiveMtSentenceName).location;
    }
    /**
     * Parse the dataset from AdaptiveMtSentence resource.
     *
     * @param {string} adaptiveMtSentenceName
     *   A fully-qualified path representing AdaptiveMtSentence resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromAdaptiveMtSentenceName(adaptiveMtSentenceName) {
        return this.pathTemplates.adaptiveMtSentencePathTemplate.match(adaptiveMtSentenceName).dataset;
    }
    /**
     * Parse the file from AdaptiveMtSentence resource.
     *
     * @param {string} adaptiveMtSentenceName
     *   A fully-qualified path representing AdaptiveMtSentence resource.
     * @returns {string} A string representing the file.
     */
    matchFileFromAdaptiveMtSentenceName(adaptiveMtSentenceName) {
        return this.pathTemplates.adaptiveMtSentencePathTemplate.match(adaptiveMtSentenceName).file;
    }
    /**
     * Parse the sentence from AdaptiveMtSentence resource.
     *
     * @param {string} adaptiveMtSentenceName
     *   A fully-qualified path representing AdaptiveMtSentence resource.
     * @returns {string} A string representing the sentence.
     */
    matchSentenceFromAdaptiveMtSentenceName(adaptiveMtSentenceName) {
        return this.pathTemplates.adaptiveMtSentencePathTemplate.match(adaptiveMtSentenceName).sentence;
    }
    /**
     * Return a fully-qualified dataset resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @returns {string} Resource name string.
     */
    datasetPath(project, location, dataset) {
        return this.pathTemplates.datasetPathTemplate.render({
            project: project,
            location: location,
            dataset: dataset,
        });
    }
    /**
     * Parse the project from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromDatasetName(datasetName) {
        return this.pathTemplates.datasetPathTemplate.match(datasetName).project;
    }
    /**
     * Parse the location from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromDatasetName(datasetName) {
        return this.pathTemplates.datasetPathTemplate.match(datasetName).location;
    }
    /**
     * Parse the dataset from Dataset resource.
     *
     * @param {string} datasetName
     *   A fully-qualified path representing Dataset resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromDatasetName(datasetName) {
        return this.pathTemplates.datasetPathTemplate.match(datasetName).dataset;
    }
    /**
     * Return a fully-qualified example resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} dataset
     * @param {string} example
     * @returns {string} Resource name string.
     */
    examplePath(project, location, dataset, example) {
        return this.pathTemplates.examplePathTemplate.render({
            project: project,
            location: location,
            dataset: dataset,
            example: example,
        });
    }
    /**
     * Parse the project from Example resource.
     *
     * @param {string} exampleName
     *   A fully-qualified path representing Example resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromExampleName(exampleName) {
        return this.pathTemplates.examplePathTemplate.match(exampleName).project;
    }
    /**
     * Parse the location from Example resource.
     *
     * @param {string} exampleName
     *   A fully-qualified path representing Example resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromExampleName(exampleName) {
        return this.pathTemplates.examplePathTemplate.match(exampleName).location;
    }
    /**
     * Parse the dataset from Example resource.
     *
     * @param {string} exampleName
     *   A fully-qualified path representing Example resource.
     * @returns {string} A string representing the dataset.
     */
    matchDatasetFromExampleName(exampleName) {
        return this.pathTemplates.examplePathTemplate.match(exampleName).dataset;
    }
    /**
     * Parse the example from Example resource.
     *
     * @param {string} exampleName
     *   A fully-qualified path representing Example resource.
     * @returns {string} A string representing the example.
     */
    matchExampleFromExampleName(exampleName) {
        return this.pathTemplates.examplePathTemplate.match(exampleName).example;
    }
    /**
     * Return a fully-qualified glossary resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} glossary
     * @returns {string} Resource name string.
     */
    glossaryPath(project, location, glossary) {
        return this.pathTemplates.glossaryPathTemplate.render({
            project: project,
            location: location,
            glossary: glossary,
        });
    }
    /**
     * Parse the project from Glossary resource.
     *
     * @param {string} glossaryName
     *   A fully-qualified path representing Glossary resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromGlossaryName(glossaryName) {
        return this.pathTemplates.glossaryPathTemplate.match(glossaryName).project;
    }
    /**
     * Parse the location from Glossary resource.
     *
     * @param {string} glossaryName
     *   A fully-qualified path representing Glossary resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromGlossaryName(glossaryName) {
        return this.pathTemplates.glossaryPathTemplate.match(glossaryName).location;
    }
    /**
     * Parse the glossary from Glossary resource.
     *
     * @param {string} glossaryName
     *   A fully-qualified path representing Glossary resource.
     * @returns {string} A string representing the glossary.
     */
    matchGlossaryFromGlossaryName(glossaryName) {
        return this.pathTemplates.glossaryPathTemplate.match(glossaryName).glossary;
    }
    /**
     * Return a fully-qualified glossaryEntry resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} glossary
     * @param {string} glossary_entry
     * @returns {string} Resource name string.
     */
    glossaryEntryPath(project, location, glossary, glossaryEntry) {
        return this.pathTemplates.glossaryEntryPathTemplate.render({
            project: project,
            location: location,
            glossary: glossary,
            glossary_entry: glossaryEntry,
        });
    }
    /**
     * Parse the project from GlossaryEntry resource.
     *
     * @param {string} glossaryEntryName
     *   A fully-qualified path representing GlossaryEntry resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromGlossaryEntryName(glossaryEntryName) {
        return this.pathTemplates.glossaryEntryPathTemplate.match(glossaryEntryName).project;
    }
    /**
     * Parse the location from GlossaryEntry resource.
     *
     * @param {string} glossaryEntryName
     *   A fully-qualified path representing GlossaryEntry resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromGlossaryEntryName(glossaryEntryName) {
        return this.pathTemplates.glossaryEntryPathTemplate.match(glossaryEntryName).location;
    }
    /**
     * Parse the glossary from GlossaryEntry resource.
     *
     * @param {string} glossaryEntryName
     *   A fully-qualified path representing GlossaryEntry resource.
     * @returns {string} A string representing the glossary.
     */
    matchGlossaryFromGlossaryEntryName(glossaryEntryName) {
        return this.pathTemplates.glossaryEntryPathTemplate.match(glossaryEntryName).glossary;
    }
    /**
     * Parse the glossary_entry from GlossaryEntry resource.
     *
     * @param {string} glossaryEntryName
     *   A fully-qualified path representing GlossaryEntry resource.
     * @returns {string} A string representing the glossary_entry.
     */
    matchGlossaryEntryFromGlossaryEntryName(glossaryEntryName) {
        return this.pathTemplates.glossaryEntryPathTemplate.match(glossaryEntryName).glossary_entry;
    }
    /**
     * Return a fully-qualified location resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    locationPath(project, location) {
        return this.pathTemplates.locationPathTemplate.render({
            project: project,
            location: location,
        });
    }
    /**
     * Parse the project from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromLocationName(locationName) {
        return this.pathTemplates.locationPathTemplate.match(locationName).project;
    }
    /**
     * Parse the location from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromLocationName(locationName) {
        return this.pathTemplates.locationPathTemplate.match(locationName).location;
    }
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(project, location, model) {
        return this.pathTemplates.modelPathTemplate.render({
            project: project,
            location: location,
            model: model,
        });
    }
    /**
     * Parse the project from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelName(modelName) {
        return this.pathTemplates.modelPathTemplate.match(modelName).project;
    }
    /**
     * Parse the location from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelName(modelName) {
        return this.pathTemplates.modelPathTemplate.match(modelName).location;
    }
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName) {
        return this.pathTemplates.modelPathTemplate.match(modelName).model;
    }
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close() {
        if (this.translationServiceStub && !this._terminated) {
            return this.translationServiceStub.then(stub => {
                this._log.info('ending gRPC channel');
                this._terminated = true;
                stub.close();
                this.iamClient.close().catch(err => { throw err; });
                this.locationsClient.close().catch(err => { throw err; });
                void this.operationsClient.close();
            });
        }
        return Promise.resolve();
    }
}
exports.TranslationServiceClient = TranslationServiceClient;
//# sourceMappingURL=translation_service_client.js.map