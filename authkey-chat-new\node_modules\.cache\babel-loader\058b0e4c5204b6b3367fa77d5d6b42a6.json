{"ast": null, "code": "var _jsxFileName = \"D:\\\\DataGen\\\\authkey-chat-new\\\\src\\\\components\\\\InputBar\\\\Inputbar.jsx\",\n    _s = $RefreshSig$();\n\nimport React from \"react\";\nimport styles from \"./inputBar.module.css\"; // Import the CSS module\n\nimport { useContext, useState, useEffect, useRef, useCallback } from \"react\";\nimport { ChatContext } from \"../../context/ChatContext\";\nimport Input from \"../Input\";\nimport useSentences from \"../../customHooks/useSentences\";\nimport { ChatState } from \"../../context/AllProviders\";\nimport { FaBold } from \"react-icons/fa\";\nimport { FaItalic } from \"react-icons/fa\";\nimport { FaStrikethrough } from \"react-icons/fa\";\nimport { FaLanguage } from \"react-icons/fa\";\nimport ReplyPreview from \"../ReplyPreview/ReplyPreview\";\nimport BlockCard from \"../BlockCard\";\nimport { AuthContext } from \"../../context/AuthContext\";\nimport axios from \"axios\";\nimport { BASE_URL2 } from \"../../api/api\";\nimport { toast } from \"react-toastify\";\nimport { TRANSLATION_CONFIG, getTranslationUrl } from \"../../config/translation\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n\nconst Inputbar = _ref => {\n  _s();\n\n  var _languages$find, _languages$find2;\n\n  let {\n    setFaqOpen,\n    notes,\n    setshowNotesCard,\n    setShowContactDetail,\n    activeTab,\n    setActiveTab,\n    setshowQuickReply\n  } = _ref;\n  const {\n    data\n  } = useContext(ChatContext);\n  const {\n    text,\n    setText,\n    reply,\n    setReply,\n    selectedMobileNumber,\n    selectedUserDetails\n  } = ChatState();\n  const textareaRef = useRef(null);\n  const [showPreview, setShowPreview] = useState(false);\n  const {\n    currentUser\n  } = useContext(AuthContext);\n  const lastTypingTimeRef = useRef(0); // Translation states\n\n  const [selectedLanguage, setSelectedLanguage] = useState('');\n  const [isTranslationEnabled, setIsTranslationEnabled] = useState(false);\n  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);\n  const [languages, setLanguages] = useState([]);\n  const [loadingLanguages, setLoadingLanguages] = useState(false);\n  const [translationError, setTranslationError] = useState(''); // Translation refs\n\n  const debounceTimeoutRef = useRef(null);\n  const lastTranslatedWordRef = useRef('');\n  const isTranslatingRef = useRef(false);\n  const DEBOUNCE_DELAY = TRANSLATION_CONFIG.SETTINGS.DEBOUNCE_DELAY;\n  const {\n    filteredSentences,\n    saveSentences,\n    splitParagraphIntoSentences\n  } = useSentences();\n\n  const handleSuggestionClick = suggestedText => {\n    var _textareaRef$current;\n\n    setText(prevState => {\n      // Split the current input into words\n      const words = prevState.split(/\\s+/); // Check if the last word matches the suggestion\n\n      const lastWord = words[words.length - 1];\n      const suggestionFirstWord = suggestedText.split(/\\s+/)[0];\n\n      if (suggestionFirstWord.toLowerCase().includes(lastWord.toLowerCase())) {\n        // If the last word matches the suggestion, replace it\n        words[words.length - 1] = suggestedText;\n      } else {\n        // Otherwise, append the suggestion\n        words.push(suggestedText);\n      } // Join the words back into a single string and return\n\n\n      return words.join(\" \");\n    });\n    (_textareaRef$current = textareaRef.current) === null || _textareaRef$current === void 0 ? void 0 : _textareaRef$current.focus();\n  };\n\n  const handleTabClick = tabName => {\n    setActiveTab(tabName);\n\n    if (tabName === \"Note\") {\n      setshowNotesCard(prevState => {\n        const newState = !prevState; // Calculate the new state\n\n        if (!newState) {\n          // If the new state is `false`, reset the active tab to \"\"\n          setActiveTab(\"\");\n        }\n\n        return newState;\n      });\n      setShowContactDetail(false);\n      setFaqOpen(false);\n      setshowQuickReply(false);\n    } else if (tabName === \"quickReplies\") {\n      setshowQuickReply(prevState => {\n        const newState = !prevState; // Calculate the new state\n\n        if (!newState) {\n          // If the new state is `false`, reset the active tab to \"\"\n          setActiveTab(\"\");\n        }\n\n        return newState;\n      });\n      setShowContactDetail(false);\n      setFaqOpen(false);\n      setshowNotesCard(false);\n    }\n  };\n\n  const showTypingStatus = async () => {\n    const now = Date.now();\n\n    if (now - lastTypingTimeRef.current < 25000) {\n      return;\n    }\n\n    lastTypingTimeRef.current = now;\n    const payload = {\n      user_id: currentUser.user_id,\n      token: currentUser.token,\n      method: \"typing\",\n      user_type: currentUser.user_type,\n      mobile: selectedMobileNumber,\n      brand_number: currentUser.brand_number\n    };\n\n    try {\n      const {\n        data\n      } = await axios.post(`${BASE_URL2}/conversation`, payload);\n    } catch (error) {\n      if (error.response && error.response.status === 429) {\n        toast.error(\"Too many requests. Please try again later.\");\n      } else {\n        console.error(error);\n        toast.error(\"Something went wrong, please try again later\");\n      }\n    }\n  };\n\n  useEffect(() => {\n    if (!text || !currentUser.user_id || !currentUser.user_type || !currentUser.brand_number || !currentUser.token) return;\n    showTypingStatus();\n  }, [text, currentUser]);\n\n  const applyFormat = wrapSymbol => {\n    const input = textareaRef.current;\n    if (!input) return;\n    const startPos = input.selectionStart;\n    const endPos = input.selectionEnd;\n    const beforeSelection = text.substring(0, startPos);\n    const selectedText = text.substring(startPos, endPos);\n    const afterSelection = text.substring(endPos);\n\n    if (selectedText === \"\") {\n      const newText = beforeSelection + wrapSymbol + wrapSymbol + afterSelection;\n      setText(newText);\n      setShowPreview(true); // Move cursor between added symbols\n\n      setTimeout(() => {\n        input.focus();\n        input.setSelectionRange(startPos + wrapSymbol.length, startPos + wrapSymbol.length);\n      }, 10);\n    } else {\n      const newText = beforeSelection + wrapSymbol + selectedText + wrapSymbol + afterSelection;\n      setText(newText);\n      setShowPreview(true);\n      setTimeout(() => input.focus(), 10);\n    }\n  }; // Translation functions\n\n\n  const fetchLanguages = useCallback(async () => {\n    if (languages.length > 0) return; // Already loaded\n\n    if (!(currentUser !== null && currentUser !== void 0 && currentUser.parent_id) || !(currentUser !== null && currentUser !== void 0 && currentUser.parent_token)) return; // No auth data\n\n    setLoadingLanguages(true);\n\n    try {\n      const payload = {\n        user_id: currentUser.parent_id,\n        token: currentUser.parent_token,\n        method: \"language_list\"\n      };\n      const response = await axios.post(getTranslationUrl(TRANSLATION_CONFIG.ENDPOINTS.GOOGLE), payload);\n      console.log('Language API response:', response.data); // Debug log\n\n      if (response.data.success) {\n        const languagesData = response.data.data;\n        console.log('Raw language data:', languagesData); // Debug log\n\n        let languageArray = []; // Handle different possible data structures\n\n        if (Array.isArray(languagesData)) {\n          // If it's already an array\n          languageArray = languagesData.map(item => ({\n            code: item.code || item.id || item.key,\n            name: String(item.name || item.label || item.value || item.code || item.id)\n          }));\n        } else if (typeof languagesData === 'object' && languagesData !== null) {\n          // If it's an object, convert to array\n          languageArray = Object.entries(languagesData).map(_ref2 => {\n            let [code, name] = _ref2;\n            return {\n              code,\n              name: String(name || code)\n            };\n          });\n        } // Sort languages alphabetically by name (with safety check)\n\n\n        if (languageArray.length > 0) {\n          languageArray.sort((a, b) => {\n            const nameA = String(a.name || '');\n            const nameB = String(b.name || '');\n            return nameA.localeCompare(nameB);\n          });\n        }\n\n        console.log('Processed language array:', languageArray); // Debug log\n\n        if (languageArray.length === 0) {\n          console.warn('No languages found in API response'); // Set a fallback with common languages\n\n          setLanguages([{\n            code: 'hi',\n            name: 'Hindi'\n          }, {\n            code: 'es',\n            name: 'Spanish'\n          }, {\n            code: 'fr',\n            name: 'French'\n          }, {\n            code: 'de',\n            name: 'German'\n          }]);\n        } else {\n          setLanguages(languageArray);\n        }\n      } else {\n        throw new Error(response.data.message || 'Failed to fetch languages');\n      }\n    } catch (error) {\n      console.error('Failed to fetch languages:', error);\n      setTranslationError('Failed to load languages');\n      setTimeout(() => setTranslationError(''), 3000);\n    } finally {\n      setLoadingLanguages(false);\n    }\n  }, [languages.length, currentUser]);\n  const translateWordSilently = useCallback(async (word, wordStart, wordEnd, currentCursorPos) => {\n    if (!word.trim() || !selectedLanguage || isTranslatingRef.current || !isTranslationEnabled) {\n      return;\n    }\n\n    if (!(currentUser !== null && currentUser !== void 0 && currentUser.parent_id) || !(currentUser !== null && currentUser !== void 0 && currentUser.parent_token)) {\n      return;\n    }\n\n    if (word === lastTranslatedWordRef.current) {\n      return;\n    }\n\n    const textarea = textareaRef.current;\n    if (!textarea) return; // Prevent multiple simultaneous translations\n\n    isTranslatingRef.current = true;\n\n    try {\n      // Store current selection to preserve it\n      const selectionStart = textarea.selectionStart;\n      const selectionEnd = textarea.selectionEnd; // Get current text\n\n      const currentText = textarea.value; // Translate the word\n\n      const payload = {\n        user_id: currentUser.parent_id,\n        token: currentUser.parent_token,\n        method: \"translate\",\n        target_lang: selectedLanguage,\n        text: word\n      };\n      const response = await axios.post(getTranslationUrl(TRANSLATION_CONFIG.ENDPOINTS.GOOGLE), payload);\n\n      if (!response.data.success) {\n        throw new Error(response.data.message || 'Translation failed');\n      }\n\n      const translatedWord = response.data.translated_text; // Create new text with translated word\n\n      const newText = currentText.substring(0, wordStart) + translatedWord + currentText.substring(wordEnd); // Update textarea value and React state\n\n      setText(newText); // Calculate new cursor position\n\n      const lengthDifference = translatedWord.length - word.length;\n      let newCursorPos;\n\n      if (currentCursorPos <= wordEnd) {\n        // Cursor was before or at the end of the translated word\n        if (currentCursorPos <= wordStart) {\n          newCursorPos = currentCursorPos; // Before the word, no change\n        } else {\n          newCursorPos = wordEnd + lengthDifference; // At the end of translated word\n        }\n      } else {\n        // Cursor was after the word\n        newCursorPos = currentCursorPos + lengthDifference;\n      } // Restore cursor position after React update\n\n\n      setTimeout(() => {\n        if (textarea) {\n          textarea.setSelectionRange(newCursorPos, newCursorPos);\n          textarea.focus();\n        }\n      }, 10);\n      lastTranslatedWordRef.current = translatedWord;\n    } catch (err) {\n      console.error('Translation error:', err);\n      setTranslationError('Translation failed. Please try again.');\n      setTimeout(() => setTranslationError(''), 3000);\n    } finally {\n      isTranslatingRef.current = false;\n    }\n  }, [selectedLanguage, isTranslationEnabled, currentUser]); // Find completed word when space is pressed\n\n  const findCompletedWord = (text, cursorPos) => {\n    // Look backwards from cursor position to find the completed word\n    if (cursorPos > 0 && text[cursorPos - 1] === ' ') {\n      let wordEnd = cursorPos - 1; // Position of space\n\n      let wordStart = wordEnd; // Find start of word (go backwards until space or beginning)\n\n      while (wordStart > 0 && text[wordStart - 1] !== ' ') {\n        wordStart--;\n      }\n\n      const word = text.substring(wordStart, wordEnd);\n      return {\n        word: word.trim(),\n        start: wordStart,\n        end: wordEnd\n      };\n    }\n\n    return null;\n  }; // Find current word being typed (for auto-translation)\n\n\n  const findCurrentWord = (text, cursorPos) => {\n    let start = cursorPos;\n    let end = cursorPos; // Find word boundaries\n\n    while (start > 0 && text[start - 1] !== ' ') {\n      start--;\n    }\n\n    while (end < text.length && text[end] !== ' ') {\n      end++;\n    }\n\n    const word = text.substring(start, end);\n    return {\n      word: word.trim(),\n      start: start,\n      end: end\n    };\n  };\n\n  const handleLanguageSelect = languageCode => {\n    setSelectedLanguage(languageCode);\n    setIsTranslationEnabled(!!languageCode);\n    setShowLanguageDropdown(false); // Reset translation references\n\n    lastTranslatedWordRef.current = '';\n\n    if (debounceTimeoutRef.current) {\n      clearTimeout(debounceTimeoutRef.current);\n    }\n  };\n\n  const toggleLanguageDropdown = () => {\n    if (!showLanguageDropdown) {\n      fetchLanguages();\n    }\n\n    setShowLanguageDropdown(!showLanguageDropdown);\n  }; // Handle space key press for instant translation\n\n\n  const handleTranslationKeyDown = useCallback(e => {\n    if (e.key === ' ' && !isTranslatingRef.current && isTranslationEnabled) {\n      const textarea = e.target; // Clear any pending auto-translation\n\n      if (debounceTimeoutRef.current) {\n        clearTimeout(debounceTimeoutRef.current);\n      } // Use requestAnimationFrame to ensure space is processed first\n\n\n      requestAnimationFrame(() => {\n        const text = textarea.value;\n        const cursorPos = textarea.selectionStart;\n        const completedWord = findCompletedWord(text, cursorPos);\n\n        if (completedWord && completedWord.word) {\n          translateWordSilently(completedWord.word, completedWord.start, completedWord.end, cursorPos);\n        }\n      });\n    }\n  }, [translateWordSilently, isTranslationEnabled, findCompletedWord]); // Handle input for auto-translation (debounced)\n\n  const handleTranslationInputChange = useCallback(currentText => {\n    if (!isTranslationEnabled || !textareaRef.current) return; // Clear existing timeout\n\n    if (debounceTimeoutRef.current) {\n      clearTimeout(debounceTimeoutRef.current);\n    } // Set timeout for auto-translation\n\n\n    if (!isTranslatingRef.current) {\n      debounceTimeoutRef.current = setTimeout(() => {\n        const textarea = textareaRef.current;\n        if (!textarea) return;\n        const text = textarea.value;\n        const cursorPos = textarea.selectionStart;\n\n        if (text.trim()) {\n          const currentWord = findCurrentWord(text, cursorPos);\n\n          if (currentWord.word && currentWord.word !== lastTranslatedWordRef.current) {\n            translateWordSilently(currentWord.word, currentWord.start, currentWord.end, cursorPos);\n          }\n        }\n      }, DEBOUNCE_DELAY);\n    }\n  }, [isTranslationEnabled, translateWordSilently, findCurrentWord]); // Effect to handle language change\n\n  useEffect(() => {\n    if (!isTranslationEnabled || !textareaRef.current || !(currentUser !== null && currentUser !== void 0 && currentUser.parent_id) || !(currentUser !== null && currentUser !== void 0 && currentUser.parent_token)) return;\n    const textarea = textareaRef.current;\n\n    if (textarea && textarea.value.trim() && !isTranslatingRef.current) {\n      const text = textarea.value;\n      const cursorPos = textarea.selectionStart;\n      const currentWord = findCurrentWord(text, cursorPos);\n\n      if (currentWord.word) {\n        translateWordSilently(currentWord.word, currentWord.start, currentWord.end, cursorPos);\n      }\n    } // Reset references\n\n\n    lastTranslatedWordRef.current = '';\n  }, [selectedLanguage, isTranslationEnabled, translateWordSilently, findCurrentWord, currentUser]); // Cleanup effect\n\n  useEffect(() => {\n    return () => {\n      if (debounceTimeoutRef.current) {\n        clearTimeout(debounceTimeoutRef.current);\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: styles.inputContainer,\n    children: selectedUserDetails.isBlock === 1 ? /*#__PURE__*/_jsxDEV(BlockCard, {\n      selectedMobileNumber: selectedMobileNumber,\n      selectedUserDetails: selectedUserDetails\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 44\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [reply && /*#__PURE__*/_jsxDEV(ReplyPreview, {\n        reply: reply,\n        onClose: () => setReply(null)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"d-flex justify-content-start align-items-center w-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleTabClick(\"Note\"),\n          className: `btn ${styles.button} ms-2 ${activeTab === \"Note\" ? styles.activeButton : \"\"}`,\n          children: [\"Notes \", notes.length > 0 ? `(${notes.length})` : \"\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleTabClick(\"quickReplies\"),\n          className: `btn ${styles.button} ms-2 ${activeTab === \"quickReplies\" ? styles.activeButton : \"\"}`,\n          children: \"Quick Replies\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-1 px-1\",\n          role: \"button\",\n          onClick: () => applyFormat(\"*\"),\n          children: /*#__PURE__*/_jsxDEV(FaBold, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-1 px-1\",\n          role: \"button\",\n          onClick: () => applyFormat(\"_\"),\n          children: /*#__PURE__*/_jsxDEV(FaItalic, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-1 px-1\",\n          role: \"button\",\n          onClick: () => applyFormat(\"~\"),\n          children: /*#__PURE__*/_jsxDEV(FaStrikethrough, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown mx-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `btn ${isTranslationEnabled ? 'btn-primary' : 'btn-outline-secondary'} dropdown-toggle d-flex align-items-center`,\n            type: \"button\",\n            onClick: toggleLanguageDropdown,\n            style: {\n              fontSize: '0.875rem',\n              padding: '0.25rem 0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaLanguage, {\n              className: \"me-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 17\n            }, this), selectedLanguage ? ((_languages$find = languages.find(lang => lang.code === selectedLanguage)) === null || _languages$find === void 0 ? void 0 : _languages$find.name) || 'Language' : 'Language']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 15\n          }, this), showLanguageDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dropdown-menu show\",\n            style: {\n              maxHeight: '300px',\n              overflowY: 'auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-header\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: \"Select language for translation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `dropdown-item ${!selectedLanguage ? 'active' : ''}`,\n              onClick: () => handleLanguageSelect(''),\n              children: \"Disable Translation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-divider\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 19\n            }, this), loadingLanguages ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"dropdown-item-text text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                children: \"Loading languages...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 21\n            }, this) : languages.map(language => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `dropdown-item ${selectedLanguage === language.code ? 'active' : ''}`,\n              onClick: () => handleLanguageSelect(language.code),\n              children: language.name\n            }, language.code, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 23\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 17\n          }, this), showLanguageDropdown && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"position-fixed top-0 start-0 w-100 h-100\",\n            style: {\n              zIndex: 1040\n            },\n            onClick: () => setShowLanguageDropdown(false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 11\n      }, this), filteredSentences.length ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.suggestionsContainer,\n        children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n          className: styles.suggestionsList,\n          children: filteredSentences.map((sentence, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: styles.suggestionPill,\n            onClick: () => handleSuggestionClick(sentence),\n            children: sentence\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-none d-md-flex justify-content-center align-items-end w-100\",\n          style: {\n            fontSize: \".7rem\",\n            color: \"grey\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Press Tab to add text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 599,\n        columnNumber: 13\n      }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.textArea,\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          showPreview: showPreview,\n          setShowPreview: setShowPreview,\n          textareaRef: textareaRef,\n          handleSuggestionClick: handleSuggestionClick,\n          saveSentences: saveSentences,\n          splitParagraphIntoSentences: splitParagraphIntoSentences,\n          filteredSentences: filteredSentences,\n          selectedMobile: data.selectedMobile,\n          convData: data,\n          handleTranslationKeyDown: handleTranslationKeyDown,\n          handleTranslationInputChange: handleTranslationInputChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 11\n      }, this), translationError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"alert alert-warning alert-dismissible fade show mt-2\",\n        role: \"alert\",\n        children: [/*#__PURE__*/_jsxDEV(\"small\", {\n          children: translationError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"btn-close\",\n          onClick: () => setTranslationError(''),\n          \"aria-label\": \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 637,\n        columnNumber: 13\n      }, this), isTranslationEnabled && selectedLanguage && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between align-items-center mt-2 px-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-muted\",\n          children: [\"Translation enabled: \", (_languages$find2 = languages.find(lang => lang.code === selectedLanguage)) === null || _languages$find2 === void 0 ? void 0 : _languages$find2.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-muted\",\n          children: \"Press space after each word for instant translation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 654,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 650,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 497,\n    columnNumber: 5\n  }, this);\n};\n\n_s(Inputbar, \"GsyRH28iiNpkMEHiY0t6v15scj0=\", false, function () {\n  return [useSentences];\n});\n\n_c = Inputbar;\nexport default Inputbar;\n\nvar _c;\n\n$RefreshReg$(_c, \"Inputbar\");", "map": {"version": 3, "sources": ["D:/DataGen/authkey-chat-new/src/components/InputBar/Inputbar.jsx"], "names": ["React", "styles", "useContext", "useState", "useEffect", "useRef", "useCallback", "ChatContext", "Input", "useSentences", "ChatState", "FaBold", "FaItalic", "FaStrikethrough", "FaLanguage", "ReplyPreview", "BlockCard", "AuthContext", "axios", "BASE_URL2", "toast", "TRANSLATION_CONFIG", "getTranslationUrl", "Inputbar", "setFaqOpen", "notes", "setshowNotesCard", "setShowContactDetail", "activeTab", "setActiveTab", "setshowQuickReply", "data", "text", "setText", "reply", "setReply", "selectedMobileNumber", "selectedUserDetails", "textareaRef", "showPreview", "setShowPreview", "currentUser", "lastTypingTimeRef", "selectedLanguage", "setSelectedLanguage", "isTranslationEnabled", "setIsTranslationEnabled", "showLanguageDropdown", "setShowLanguageDropdown", "languages", "setLanguages", "loadingLanguages", "setLoadingLanguages", "translationError", "setTranslationError", "debounceTimeoutRef", "lastTranslatedWordRef", "isTranslatingRef", "DEBOUNCE_DELAY", "SETTINGS", "filteredSentences", "saveSentences", "splitParagraphIntoSentences", "handleSuggestionClick", "suggestedText", "prevState", "words", "split", "lastWord", "length", "suggestionFirstWord", "toLowerCase", "includes", "push", "join", "current", "focus", "handleTabClick", "tabName", "newState", "showTypingStatus", "now", "Date", "payload", "user_id", "token", "method", "user_type", "mobile", "brand_number", "post", "error", "response", "status", "console", "applyFormat", "wrapSymbol", "input", "startPos", "selectionStart", "endPos", "selectionEnd", "beforeSelection", "substring", "selectedText", "afterSelection", "newText", "setTimeout", "setSelectionRange", "fetchLanguages", "parent_id", "parent_token", "ENDPOINTS", "GOOGLE", "log", "success", "languagesData", "languageArray", "Array", "isArray", "map", "item", "code", "id", "key", "name", "String", "label", "value", "Object", "entries", "sort", "a", "b", "nameA", "nameB", "localeCompare", "warn", "Error", "message", "translateWordSilently", "word", "wordStart", "wordEnd", "currentCursorPos", "trim", "textarea", "currentText", "target_lang", "translated<PERSON><PERSON>", "translated_text", "lengthDifference", "newCursorPos", "err", "findCompletedWord", "cursorPos", "start", "end", "findCurrentWord", "handleLanguageSelect", "languageCode", "clearTimeout", "toggleLanguageDropdown", "handleTranslationKeyDown", "e", "target", "requestAnimationFrame", "completedWord", "handleTranslationInputChange", "currentWord", "inputContainer", "isBlock", "button", "activeButton", "fontSize", "padding", "find", "lang", "maxHeight", "overflowY", "language", "zIndex", "<PERSON><PERSON><PERSON><PERSON>", "suggestionsList", "sentence", "index", "suggestionPill", "color", "textArea", "selected<PERSON><PERSON><PERSON>"], "mappings": ";;;AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,OAAOC,MAAP,MAAmB,uBAAnB,C,CAA4C;;AAC5C,SAASC,UAAT,EAAqBC,QAArB,EAA+BC,SAA/B,EAA0CC,MAA1C,EAAkDC,WAAlD,QAAqE,OAArE;AACA,SAASC,WAAT,QAA4B,2BAA5B;AACA,OAAOC,KAAP,MAAkB,UAAlB;AACA,OAAOC,YAAP,MAAyB,gCAAzB;AACA,SAASC,SAAT,QAA0B,4BAA1B;AACA,SAASC,MAAT,QAAuB,gBAAvB;AACA,SAASC,QAAT,QAAyB,gBAAzB;AACA,SAASC,eAAT,QAAgC,gBAAhC;AACA,SAASC,UAAT,QAA2B,gBAA3B;AACA,OAAOC,YAAP,MAAyB,8BAAzB;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,SAASC,WAAT,QAA4B,2BAA5B;AACA,OAAOC,KAAP,MAAkB,OAAlB;AACA,SAASC,SAAT,QAA0B,eAA1B;AACA,SAASC,KAAT,QAAsB,gBAAtB;AACA,SAASC,kBAAT,EAA6BC,iBAA7B,QAAsD,0BAAtD;;;;AACA,MAAMC,QAAQ,GAAG,QASX;AAAA;;AAAA;;AAAA,MATY;AAChBC,IAAAA,UADgB;AAEhBC,IAAAA,KAFgB;AAGhBC,IAAAA,gBAHgB;AAIhBC,IAAAA,oBAJgB;AAKhBC,IAAAA,SALgB;AAMhBC,IAAAA,YANgB;AAOhBC,IAAAA;AAPgB,GASZ;AACJ,QAAM;AAAEC,IAAAA;AAAF,MAAW7B,UAAU,CAACK,WAAD,CAA3B;AACA,QAAM;AAAEyB,IAAAA,IAAF;AAAQC,IAAAA,OAAR;AAAiBC,IAAAA,KAAjB;AAAwBC,IAAAA,QAAxB;AAAkCC,IAAAA,oBAAlC;AAAwDC,IAAAA;AAAxD,MAAgF3B,SAAS,EAA/F;AACA,QAAM4B,WAAW,GAAGjC,MAAM,CAAC,IAAD,CAA1B;AACA,QAAM,CAACkC,WAAD,EAAcC,cAAd,IAAgCrC,QAAQ,CAAC,KAAD,CAA9C;AACA,QAAM;AAAEsC,IAAAA;AAAF,MAAkBvC,UAAU,CAACe,WAAD,CAAlC;AACA,QAAMyB,iBAAiB,GAAGrC,MAAM,CAAC,CAAD,CAAhC,CANI,CAQJ;;AACA,QAAM,CAACsC,gBAAD,EAAmBC,mBAAnB,IAA0CzC,QAAQ,CAAC,EAAD,CAAxD;AACA,QAAM,CAAC0C,oBAAD,EAAuBC,uBAAvB,IAAkD3C,QAAQ,CAAC,KAAD,CAAhE;AACA,QAAM,CAAC4C,oBAAD,EAAuBC,uBAAvB,IAAkD7C,QAAQ,CAAC,KAAD,CAAhE;AACA,QAAM,CAAC8C,SAAD,EAAYC,YAAZ,IAA4B/C,QAAQ,CAAC,EAAD,CAA1C;AACA,QAAM,CAACgD,gBAAD,EAAmBC,mBAAnB,IAA0CjD,QAAQ,CAAC,KAAD,CAAxD;AACA,QAAM,CAACkD,gBAAD,EAAmBC,mBAAnB,IAA0CnD,QAAQ,CAAC,EAAD,CAAxD,CAdI,CAgBJ;;AACA,QAAMoD,kBAAkB,GAAGlD,MAAM,CAAC,IAAD,CAAjC;AACA,QAAMmD,qBAAqB,GAAGnD,MAAM,CAAC,EAAD,CAApC;AACA,QAAMoD,gBAAgB,GAAGpD,MAAM,CAAC,KAAD,CAA/B;AACA,QAAMqD,cAAc,GAAGrC,kBAAkB,CAACsC,QAAnB,CAA4BD,cAAnD;AAGA,QAAM;AAAEE,IAAAA,iBAAF;AAAqBC,IAAAA,aAArB;AAAoCC,IAAAA;AAApC,MACJrD,YAAY,EADd;;AAGA,QAAMsD,qBAAqB,GAAIC,aAAD,IAAmB;AAAA;;AAC/C/B,IAAAA,OAAO,CAAEgC,SAAD,IAAe;AACrB;AACA,YAAMC,KAAK,GAAGD,SAAS,CAACE,KAAV,CAAgB,KAAhB,CAAd,CAFqB,CAIrB;;AACA,YAAMC,QAAQ,GAAGF,KAAK,CAACA,KAAK,CAACG,MAAN,GAAe,CAAhB,CAAtB;AACA,YAAMC,mBAAmB,GAAGN,aAAa,CAACG,KAAd,CAAoB,KAApB,EAA2B,CAA3B,CAA5B;;AAEA,UAAIG,mBAAmB,CAACC,WAApB,GAAkCC,QAAlC,CAA2CJ,QAAQ,CAACG,WAAT,EAA3C,CAAJ,EAAwE;AACtE;AACAL,QAAAA,KAAK,CAACA,KAAK,CAACG,MAAN,GAAe,CAAhB,CAAL,GAA0BL,aAA1B;AACD,OAHD,MAGO;AACL;AACAE,QAAAA,KAAK,CAACO,IAAN,CAAWT,aAAX;AACD,OAdoB,CAerB;;;AACA,aAAOE,KAAK,CAACQ,IAAN,CAAW,GAAX,CAAP;AACD,KAjBM,CAAP;AAkBA,4BAAApC,WAAW,CAACqC,OAAZ,8EAAqBC,KAArB;AACD,GApBD;;AAqBA,QAAMC,cAAc,GAAIC,OAAD,IAAa;AAClCjD,IAAAA,YAAY,CAACiD,OAAD,CAAZ;;AAEA,QAAIA,OAAO,KAAK,MAAhB,EAAwB;AACtBpD,MAAAA,gBAAgB,CAAEuC,SAAD,IAAe;AAC9B,cAAMc,QAAQ,GAAG,CAACd,SAAlB,CAD8B,CACD;;AAC7B,YAAI,CAACc,QAAL,EAAe;AACb;AACAlD,UAAAA,YAAY,CAAC,EAAD,CAAZ;AACD;;AACD,eAAOkD,QAAP;AACD,OAPe,CAAhB;AAQApD,MAAAA,oBAAoB,CAAC,KAAD,CAApB;AACAH,MAAAA,UAAU,CAAC,KAAD,CAAV;AACAM,MAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACD,KAZD,MAYO,IAAIgD,OAAO,KAAK,cAAhB,EAAgC;AACrChD,MAAAA,iBAAiB,CAAEmC,SAAD,IAAe;AAC/B,cAAMc,QAAQ,GAAG,CAACd,SAAlB,CAD+B,CACF;;AAC7B,YAAI,CAACc,QAAL,EAAe;AACb;AACAlD,UAAAA,YAAY,CAAC,EAAD,CAAZ;AACD;;AACD,eAAOkD,QAAP;AACD,OAPgB,CAAjB;AAQApD,MAAAA,oBAAoB,CAAC,KAAD,CAApB;AACAH,MAAAA,UAAU,CAAC,KAAD,CAAV;AACAE,MAAAA,gBAAgB,CAAC,KAAD,CAAhB;AACD;AACF,GA5BD;;AA+BA,QAAMsD,gBAAgB,GAAG,YAAY;AACnC,UAAMC,GAAG,GAAGC,IAAI,CAACD,GAAL,EAAZ;;AACA,QAAIA,GAAG,GAAGvC,iBAAiB,CAACiC,OAAxB,GAAkC,KAAtC,EAA6C;AAC3C;AACD;;AAEDjC,IAAAA,iBAAiB,CAACiC,OAAlB,GAA4BM,GAA5B;AACA,UAAME,OAAO,GAAG;AACdC,MAAAA,OAAO,EAAE3C,WAAW,CAAC2C,OADP;AAEdC,MAAAA,KAAK,EAAE5C,WAAW,CAAC4C,KAFL;AAGdC,MAAAA,MAAM,EAAE,QAHM;AAIdC,MAAAA,SAAS,EAAE9C,WAAW,CAAC8C,SAJT;AAKdC,MAAAA,MAAM,EAAEpD,oBALM;AAMdqD,MAAAA,YAAY,EAAEhD,WAAW,CAACgD;AANZ,KAAhB;;AASA,QAAI;AAEF,YAAM;AAAE1D,QAAAA;AAAF,UAAW,MAAMb,KAAK,CAACwE,IAAN,CAAY,GAAEvE,SAAU,eAAxB,EAAwCgE,OAAxC,CAAvB;AAED,KAJD,CAIE,OAAOQ,KAAP,EAAc;AACd,UAAIA,KAAK,CAACC,QAAN,IAAkBD,KAAK,CAACC,QAAN,CAAeC,MAAf,KAA0B,GAAhD,EAAqD;AACnDzE,QAAAA,KAAK,CAACuE,KAAN,CAAY,4CAAZ;AAED,OAHD,MAGO;AACLG,QAAAA,OAAO,CAACH,KAAR,CAAcA,KAAd;AACAvE,QAAAA,KAAK,CAACuE,KAAN,CAAY,8CAAZ;AACD;AACF;AACF,GA7BD;;AA+BAvF,EAAAA,SAAS,CAAC,MAAM;AACd,QAAI,CAAC4B,IAAD,IAAS,CAACS,WAAW,CAAC2C,OAAtB,IACC,CAAC3C,WAAW,CAAC8C,SADd,IAC2B,CAAC9C,WAAW,CAACgD,YADxC,IAEC,CAAChD,WAAW,CAAC4C,KAFlB,EAEyB;AAEzBL,IAAAA,gBAAgB;AACjB,GANQ,EAMN,CAAChD,IAAD,EAAOS,WAAP,CANM,CAAT;;AASA,QAAMsD,WAAW,GAAIC,UAAD,IAAgB;AAClC,UAAMC,KAAK,GAAG3D,WAAW,CAACqC,OAA1B;AACA,QAAI,CAACsB,KAAL,EAAY;AAEZ,UAAMC,QAAQ,GAAGD,KAAK,CAACE,cAAvB;AACA,UAAMC,MAAM,GAAGH,KAAK,CAACI,YAArB;AAEA,UAAMC,eAAe,GAAGtE,IAAI,CAACuE,SAAL,CAAe,CAAf,EAAkBL,QAAlB,CAAxB;AACA,UAAMM,YAAY,GAAGxE,IAAI,CAACuE,SAAL,CAAeL,QAAf,EAAyBE,MAAzB,CAArB;AACA,UAAMK,cAAc,GAAGzE,IAAI,CAACuE,SAAL,CAAeH,MAAf,CAAvB;;AAEA,QAAII,YAAY,KAAK,EAArB,EAAyB;AACvB,YAAME,OAAO,GAAGJ,eAAe,GAAGN,UAAlB,GAA+BA,UAA/B,GAA4CS,cAA5D;AACAxE,MAAAA,OAAO,CAACyE,OAAD,CAAP;AACAlE,MAAAA,cAAc,CAAC,IAAD,CAAd,CAHuB,CAKvB;;AACAmE,MAAAA,UAAU,CAAC,MAAM;AACfV,QAAAA,KAAK,CAACrB,KAAN;AACAqB,QAAAA,KAAK,CAACW,iBAAN,CAAwBV,QAAQ,GAAGF,UAAU,CAAC3B,MAA9C,EAAsD6B,QAAQ,GAAGF,UAAU,CAAC3B,MAA5E;AACD,OAHS,EAGP,EAHO,CAAV;AAID,KAVD,MAUO;AACL,YAAMqC,OAAO,GAAGJ,eAAe,GAAGN,UAAlB,GAA+BQ,YAA/B,GAA8CR,UAA9C,GAA2DS,cAA3E;AACAxE,MAAAA,OAAO,CAACyE,OAAD,CAAP;AACAlE,MAAAA,cAAc,CAAC,IAAD,CAAd;AAEAmE,MAAAA,UAAU,CAAC,MAAMV,KAAK,CAACrB,KAAN,EAAP,EAAsB,EAAtB,CAAV;AACD;AACF,GA5BD,CAtHI,CAoJJ;;;AACA,QAAMiC,cAAc,GAAGvG,WAAW,CAAC,YAAY;AAC7C,QAAI2C,SAAS,CAACoB,MAAV,GAAmB,CAAvB,EAA0B,OADmB,CACX;;AAClC,QAAI,EAAC5B,WAAD,aAACA,WAAD,eAACA,WAAW,CAAEqE,SAAd,KAA2B,EAACrE,WAAD,aAACA,WAAD,eAACA,WAAW,CAAEsE,YAAd,CAA/B,EAA2D,OAFd,CAEsB;;AAEnE3D,IAAAA,mBAAmB,CAAC,IAAD,CAAnB;;AACA,QAAI;AACF,YAAM+B,OAAO,GAAG;AACdC,QAAAA,OAAO,EAAE3C,WAAW,CAACqE,SADP;AAEdzB,QAAAA,KAAK,EAAE5C,WAAW,CAACsE,YAFL;AAGdzB,QAAAA,MAAM,EAAE;AAHM,OAAhB;AAMA,YAAMM,QAAQ,GAAG,MAAM1E,KAAK,CAACwE,IAAN,CAAWpE,iBAAiB,CAACD,kBAAkB,CAAC2F,SAAnB,CAA6BC,MAA9B,CAA5B,EAAmE9B,OAAnE,CAAvB;AACAW,MAAAA,OAAO,CAACoB,GAAR,CAAY,wBAAZ,EAAsCtB,QAAQ,CAAC7D,IAA/C,EARE,CAQoD;;AAEtD,UAAI6D,QAAQ,CAAC7D,IAAT,CAAcoF,OAAlB,EAA2B;AACzB,cAAMC,aAAa,GAAGxB,QAAQ,CAAC7D,IAAT,CAAcA,IAApC;AACA+D,QAAAA,OAAO,CAACoB,GAAR,CAAY,oBAAZ,EAAkCE,aAAlC,EAFyB,CAEyB;;AAElD,YAAIC,aAAa,GAAG,EAApB,CAJyB,CAMzB;;AACA,YAAIC,KAAK,CAACC,OAAN,CAAcH,aAAd,CAAJ,EAAkC;AAChC;AACAC,UAAAA,aAAa,GAAGD,aAAa,CAACI,GAAd,CAAkBC,IAAI,KAAK;AACzCC,YAAAA,IAAI,EAAED,IAAI,CAACC,IAAL,IAAaD,IAAI,CAACE,EAAlB,IAAwBF,IAAI,CAACG,GADM;AAEzCC,YAAAA,IAAI,EAAEC,MAAM,CAACL,IAAI,CAACI,IAAL,IAAaJ,IAAI,CAACM,KAAlB,IAA2BN,IAAI,CAACO,KAAhC,IAAyCP,IAAI,CAACC,IAA9C,IAAsDD,IAAI,CAACE,EAA5D;AAF6B,WAAL,CAAtB,CAAhB;AAID,SAND,MAMO,IAAI,OAAOP,aAAP,KAAyB,QAAzB,IAAqCA,aAAa,KAAK,IAA3D,EAAiE;AACtE;AACAC,UAAAA,aAAa,GAAGY,MAAM,CAACC,OAAP,CAAed,aAAf,EAA8BI,GAA9B,CAAkC;AAAA,gBAAC,CAACE,IAAD,EAAOG,IAAP,CAAD;AAAA,mBAAmB;AACnEH,cAAAA,IADmE;AAEnEG,cAAAA,IAAI,EAAEC,MAAM,CAACD,IAAI,IAAIH,IAAT;AAFuD,aAAnB;AAAA,WAAlC,CAAhB;AAID,SAnBwB,CAqBzB;;;AACA,YAAIL,aAAa,CAAChD,MAAd,GAAuB,CAA3B,EAA8B;AAC5BgD,UAAAA,aAAa,CAACc,IAAd,CAAmB,CAACC,CAAD,EAAIC,CAAJ,KAAU;AAC3B,kBAAMC,KAAK,GAAGR,MAAM,CAACM,CAAC,CAACP,IAAF,IAAU,EAAX,CAApB;AACA,kBAAMU,KAAK,GAAGT,MAAM,CAACO,CAAC,CAACR,IAAF,IAAU,EAAX,CAApB;AACA,mBAAOS,KAAK,CAACE,aAAN,CAAoBD,KAApB,CAAP;AACD,WAJD;AAKD;;AAEDzC,QAAAA,OAAO,CAACoB,GAAR,CAAY,2BAAZ,EAAyCG,aAAzC,EA9ByB,CA8BgC;;AAEzD,YAAIA,aAAa,CAAChD,MAAd,KAAyB,CAA7B,EAAgC;AAC9ByB,UAAAA,OAAO,CAAC2C,IAAR,CAAa,oCAAb,EAD8B,CAE9B;;AACAvF,UAAAA,YAAY,CAAC,CACX;AAAEwE,YAAAA,IAAI,EAAE,IAAR;AAAcG,YAAAA,IAAI,EAAE;AAApB,WADW,EAEX;AAAEH,YAAAA,IAAI,EAAE,IAAR;AAAcG,YAAAA,IAAI,EAAE;AAApB,WAFW,EAGX;AAAEH,YAAAA,IAAI,EAAE,IAAR;AAAcG,YAAAA,IAAI,EAAE;AAApB,WAHW,EAIX;AAAEH,YAAAA,IAAI,EAAE,IAAR;AAAcG,YAAAA,IAAI,EAAE;AAApB,WAJW,CAAD,CAAZ;AAMD,SATD,MASO;AACL3E,UAAAA,YAAY,CAACmE,aAAD,CAAZ;AACD;AACF,OA5CD,MA4CO;AACL,cAAM,IAAIqB,KAAJ,CAAU9C,QAAQ,CAAC7D,IAAT,CAAc4G,OAAd,IAAyB,2BAAnC,CAAN;AACD;AACF,KAzDD,CAyDE,OAAOhD,KAAP,EAAc;AACdG,MAAAA,OAAO,CAACH,KAAR,CAAc,4BAAd,EAA4CA,KAA5C;AACArC,MAAAA,mBAAmB,CAAC,0BAAD,CAAnB;AACAqD,MAAAA,UAAU,CAAC,MAAMrD,mBAAmB,CAAC,EAAD,CAA1B,EAAgC,IAAhC,CAAV;AACD,KA7DD,SA6DU;AACRF,MAAAA,mBAAmB,CAAC,KAAD,CAAnB;AACD;AACF,GArEiC,EAqE/B,CAACH,SAAS,CAACoB,MAAX,EAAmB5B,WAAnB,CArE+B,CAAlC;AAuEA,QAAMmG,qBAAqB,GAAGtI,WAAW,CAAC,OAAOuI,IAAP,EAAaC,SAAb,EAAwBC,OAAxB,EAAiCC,gBAAjC,KAAsD;AAC9F,QAAI,CAACH,IAAI,CAACI,IAAL,EAAD,IAAgB,CAACtG,gBAAjB,IAAqCc,gBAAgB,CAACkB,OAAtD,IAAiE,CAAC9B,oBAAtE,EAA4F;AAC1F;AACD;;AAED,QAAI,EAACJ,WAAD,aAACA,WAAD,eAACA,WAAW,CAAEqE,SAAd,KAA2B,EAACrE,WAAD,aAACA,WAAD,eAACA,WAAW,CAAEsE,YAAd,CAA/B,EAA2D;AACzD;AACD;;AAED,QAAI8B,IAAI,KAAKrF,qBAAqB,CAACmB,OAAnC,EAA4C;AAC1C;AACD;;AAED,UAAMuE,QAAQ,GAAG5G,WAAW,CAACqC,OAA7B;AACA,QAAI,CAACuE,QAAL,EAAe,OAd+E,CAgB9F;;AACAzF,IAAAA,gBAAgB,CAACkB,OAAjB,GAA2B,IAA3B;;AAEA,QAAI;AACF;AACA,YAAMwB,cAAc,GAAG+C,QAAQ,CAAC/C,cAAhC;AACA,YAAME,YAAY,GAAG6C,QAAQ,CAAC7C,YAA9B,CAHE,CAKF;;AACA,YAAM8C,WAAW,GAAGD,QAAQ,CAAClB,KAA7B,CANE,CAQF;;AACA,YAAM7C,OAAO,GAAG;AACdC,QAAAA,OAAO,EAAE3C,WAAW,CAACqE,SADP;AAEdzB,QAAAA,KAAK,EAAE5C,WAAW,CAACsE,YAFL;AAGdzB,QAAAA,MAAM,EAAE,WAHM;AAId8D,QAAAA,WAAW,EAAEzG,gBAJC;AAKdX,QAAAA,IAAI,EAAE6G;AALQ,OAAhB;AAQA,YAAMjD,QAAQ,GAAG,MAAM1E,KAAK,CAACwE,IAAN,CACrBpE,iBAAiB,CAACD,kBAAkB,CAAC2F,SAAnB,CAA6BC,MAA9B,CADI,EAErB9B,OAFqB,CAAvB;;AAKA,UAAI,CAACS,QAAQ,CAAC7D,IAAT,CAAcoF,OAAnB,EAA4B;AAC1B,cAAM,IAAIuB,KAAJ,CAAU9C,QAAQ,CAAC7D,IAAT,CAAc4G,OAAd,IAAyB,oBAAnC,CAAN;AACD;;AAED,YAAMU,cAAc,GAAGzD,QAAQ,CAAC7D,IAAT,CAAcuH,eAArC,CA1BE,CA4BF;;AACA,YAAM5C,OAAO,GACXyC,WAAW,CAAC5C,SAAZ,CAAsB,CAAtB,EAAyBuC,SAAzB,IACAO,cADA,GAEAF,WAAW,CAAC5C,SAAZ,CAAsBwC,OAAtB,CAHF,CA7BE,CAkCF;;AACA9G,MAAAA,OAAO,CAACyE,OAAD,CAAP,CAnCE,CAqCF;;AACA,YAAM6C,gBAAgB,GAAGF,cAAc,CAAChF,MAAf,GAAwBwE,IAAI,CAACxE,MAAtD;AACA,UAAImF,YAAJ;;AAEA,UAAIR,gBAAgB,IAAID,OAAxB,EAAiC;AAC/B;AACA,YAAIC,gBAAgB,IAAIF,SAAxB,EAAmC;AACjCU,UAAAA,YAAY,GAAGR,gBAAf,CADiC,CACA;AAClC,SAFD,MAEO;AACLQ,UAAAA,YAAY,GAAGT,OAAO,GAAGQ,gBAAzB,CADK,CACsC;AAC5C;AACF,OAPD,MAOO;AACL;AACAC,QAAAA,YAAY,GAAGR,gBAAgB,GAAGO,gBAAlC;AACD,OAnDC,CAqDF;;;AACA5C,MAAAA,UAAU,CAAC,MAAM;AACf,YAAIuC,QAAJ,EAAc;AACZA,UAAAA,QAAQ,CAACtC,iBAAT,CAA2B4C,YAA3B,EAAyCA,YAAzC;AACAN,UAAAA,QAAQ,CAACtE,KAAT;AACD;AACF,OALS,EAKP,EALO,CAAV;AAOApB,MAAAA,qBAAqB,CAACmB,OAAtB,GAAgC0E,cAAhC;AAED,KA/DD,CA+DE,OAAOI,GAAP,EAAY;AACZ3D,MAAAA,OAAO,CAACH,KAAR,CAAc,oBAAd,EAAoC8D,GAApC;AACAnG,MAAAA,mBAAmB,CAAC,uCAAD,CAAnB;AACAqD,MAAAA,UAAU,CAAC,MAAMrD,mBAAmB,CAAC,EAAD,CAA1B,EAAgC,IAAhC,CAAV;AACD,KAnED,SAmEU;AACRG,MAAAA,gBAAgB,CAACkB,OAAjB,GAA2B,KAA3B;AACD;AACF,GAzFwC,EAyFtC,CAAChC,gBAAD,EAAmBE,oBAAnB,EAAyCJ,WAAzC,CAzFsC,CAAzC,CA5NI,CAuTJ;;AACA,QAAMiH,iBAAiB,GAAG,CAAC1H,IAAD,EAAO2H,SAAP,KAAqB;AAC7C;AACA,QAAIA,SAAS,GAAG,CAAZ,IAAiB3H,IAAI,CAAC2H,SAAS,GAAG,CAAb,CAAJ,KAAwB,GAA7C,EAAkD;AAChD,UAAIZ,OAAO,GAAGY,SAAS,GAAG,CAA1B,CADgD,CACnB;;AAC7B,UAAIb,SAAS,GAAGC,OAAhB,CAFgD,CAIhD;;AACA,aAAOD,SAAS,GAAG,CAAZ,IAAiB9G,IAAI,CAAC8G,SAAS,GAAG,CAAb,CAAJ,KAAwB,GAAhD,EAAqD;AACnDA,QAAAA,SAAS;AACV;;AAED,YAAMD,IAAI,GAAG7G,IAAI,CAACuE,SAAL,CAAeuC,SAAf,EAA0BC,OAA1B,CAAb;AACA,aAAO;AACLF,QAAAA,IAAI,EAAEA,IAAI,CAACI,IAAL,EADD;AAELW,QAAAA,KAAK,EAAEd,SAFF;AAGLe,QAAAA,GAAG,EAAEd;AAHA,OAAP;AAKD;;AACD,WAAO,IAAP;AACD,GAnBD,CAxTI,CA6UJ;;;AACA,QAAMe,eAAe,GAAG,CAAC9H,IAAD,EAAO2H,SAAP,KAAqB;AAC3C,QAAIC,KAAK,GAAGD,SAAZ;AACA,QAAIE,GAAG,GAAGF,SAAV,CAF2C,CAI3C;;AACA,WAAOC,KAAK,GAAG,CAAR,IAAa5H,IAAI,CAAC4H,KAAK,GAAG,CAAT,CAAJ,KAAoB,GAAxC,EAA6C;AAC3CA,MAAAA,KAAK;AACN;;AACD,WAAOC,GAAG,GAAG7H,IAAI,CAACqC,MAAX,IAAqBrC,IAAI,CAAC6H,GAAD,CAAJ,KAAc,GAA1C,EAA+C;AAC7CA,MAAAA,GAAG;AACJ;;AAED,UAAMhB,IAAI,GAAG7G,IAAI,CAACuE,SAAL,CAAeqD,KAAf,EAAsBC,GAAtB,CAAb;AACA,WAAO;AACLhB,MAAAA,IAAI,EAAEA,IAAI,CAACI,IAAL,EADD;AAELW,MAAAA,KAAK,EAAEA,KAFF;AAGLC,MAAAA,GAAG,EAAEA;AAHA,KAAP;AAKD,GAlBD;;AAoBA,QAAME,oBAAoB,GAAIC,YAAD,IAAkB;AAC7CpH,IAAAA,mBAAmB,CAACoH,YAAD,CAAnB;AACAlH,IAAAA,uBAAuB,CAAC,CAAC,CAACkH,YAAH,CAAvB;AACAhH,IAAAA,uBAAuB,CAAC,KAAD,CAAvB,CAH6C,CAK7C;;AACAQ,IAAAA,qBAAqB,CAACmB,OAAtB,GAAgC,EAAhC;;AAEA,QAAIpB,kBAAkB,CAACoB,OAAvB,EAAgC;AAC9BsF,MAAAA,YAAY,CAAC1G,kBAAkB,CAACoB,OAApB,CAAZ;AACD;AACF,GAXD;;AAaA,QAAMuF,sBAAsB,GAAG,MAAM;AACnC,QAAI,CAACnH,oBAAL,EAA2B;AACzB8D,MAAAA,cAAc;AACf;;AACD7D,IAAAA,uBAAuB,CAAC,CAACD,oBAAF,CAAvB;AACD,GALD,CA/WI,CAsXJ;;;AACA,QAAMoH,wBAAwB,GAAG7J,WAAW,CAAE8J,CAAD,IAAO;AAClD,QAAIA,CAAC,CAACxC,GAAF,KAAU,GAAV,IAAiB,CAACnE,gBAAgB,CAACkB,OAAnC,IAA8C9B,oBAAlD,EAAwE;AACtE,YAAMqG,QAAQ,GAAGkB,CAAC,CAACC,MAAnB,CADsE,CAGtE;;AACA,UAAI9G,kBAAkB,CAACoB,OAAvB,EAAgC;AAC9BsF,QAAAA,YAAY,CAAC1G,kBAAkB,CAACoB,OAApB,CAAZ;AACD,OANqE,CAQtE;;;AACA2F,MAAAA,qBAAqB,CAAC,MAAM;AAC1B,cAAMtI,IAAI,GAAGkH,QAAQ,CAAClB,KAAtB;AACA,cAAM2B,SAAS,GAAGT,QAAQ,CAAC/C,cAA3B;AAEA,cAAMoE,aAAa,GAAGb,iBAAiB,CAAC1H,IAAD,EAAO2H,SAAP,CAAvC;;AACA,YAAIY,aAAa,IAAIA,aAAa,CAAC1B,IAAnC,EAAyC;AACvCD,UAAAA,qBAAqB,CACnB2B,aAAa,CAAC1B,IADK,EAEnB0B,aAAa,CAACX,KAFK,EAGnBW,aAAa,CAACV,GAHK,EAInBF,SAJmB,CAArB;AAMD;AACF,OAboB,CAArB;AAcD;AACF,GAzB2C,EAyBzC,CAACf,qBAAD,EAAwB/F,oBAAxB,EAA8C6G,iBAA9C,CAzByC,CAA5C,CAvXI,CAkZJ;;AACA,QAAMc,4BAA4B,GAAGlK,WAAW,CAAE6I,WAAD,IAAiB;AAChE,QAAI,CAACtG,oBAAD,IAAyB,CAACP,WAAW,CAACqC,OAA1C,EAAmD,OADa,CAGhE;;AACA,QAAIpB,kBAAkB,CAACoB,OAAvB,EAAgC;AAC9BsF,MAAAA,YAAY,CAAC1G,kBAAkB,CAACoB,OAApB,CAAZ;AACD,KAN+D,CAQhE;;;AACA,QAAI,CAAClB,gBAAgB,CAACkB,OAAtB,EAA+B;AAC7BpB,MAAAA,kBAAkB,CAACoB,OAAnB,GAA6BgC,UAAU,CAAC,MAAM;AAC5C,cAAMuC,QAAQ,GAAG5G,WAAW,CAACqC,OAA7B;AACA,YAAI,CAACuE,QAAL,EAAe;AAEf,cAAMlH,IAAI,GAAGkH,QAAQ,CAAClB,KAAtB;AACA,cAAM2B,SAAS,GAAGT,QAAQ,CAAC/C,cAA3B;;AAEA,YAAInE,IAAI,CAACiH,IAAL,EAAJ,EAAiB;AACf,gBAAMwB,WAAW,GAAGX,eAAe,CAAC9H,IAAD,EAAO2H,SAAP,CAAnC;;AACA,cAAIc,WAAW,CAAC5B,IAAZ,IAAoB4B,WAAW,CAAC5B,IAAZ,KAAqBrF,qBAAqB,CAACmB,OAAnE,EAA4E;AAC1EiE,YAAAA,qBAAqB,CACnB6B,WAAW,CAAC5B,IADO,EAEnB4B,WAAW,CAACb,KAFO,EAGnBa,WAAW,CAACZ,GAHO,EAInBF,SAJmB,CAArB;AAMD;AACF;AACF,OAlBsC,EAkBpCjG,cAlBoC,CAAvC;AAmBD;AACF,GA9B+C,EA8B7C,CAACb,oBAAD,EAAuB+F,qBAAvB,EAA8CkB,eAA9C,CA9B6C,CAAhD,CAnZI,CAmbJ;;AACA1J,EAAAA,SAAS,CAAC,MAAM;AACd,QAAI,CAACyC,oBAAD,IAAyB,CAACP,WAAW,CAACqC,OAAtC,IAAiD,EAAClC,WAAD,aAACA,WAAD,eAACA,WAAW,CAAEqE,SAAd,CAAjD,IAA4E,EAACrE,WAAD,aAACA,WAAD,eAACA,WAAW,CAAEsE,YAAd,CAAhF,EAA4G;AAE5G,UAAMmC,QAAQ,GAAG5G,WAAW,CAACqC,OAA7B;;AACA,QAAIuE,QAAQ,IAAIA,QAAQ,CAAClB,KAAT,CAAeiB,IAAf,EAAZ,IAAqC,CAACxF,gBAAgB,CAACkB,OAA3D,EAAoE;AAClE,YAAM3C,IAAI,GAAGkH,QAAQ,CAAClB,KAAtB;AACA,YAAM2B,SAAS,GAAGT,QAAQ,CAAC/C,cAA3B;AACA,YAAMsE,WAAW,GAAGX,eAAe,CAAC9H,IAAD,EAAO2H,SAAP,CAAnC;;AAEA,UAAIc,WAAW,CAAC5B,IAAhB,EAAsB;AACpBD,QAAAA,qBAAqB,CACnB6B,WAAW,CAAC5B,IADO,EAEnB4B,WAAW,CAACb,KAFO,EAGnBa,WAAW,CAACZ,GAHO,EAInBF,SAJmB,CAArB;AAMD;AACF,KAjBa,CAmBd;;;AACAnG,IAAAA,qBAAqB,CAACmB,OAAtB,GAAgC,EAAhC;AACD,GArBQ,EAqBN,CAAChC,gBAAD,EAAmBE,oBAAnB,EAAyC+F,qBAAzC,EAAgEkB,eAAhE,EAAiFrH,WAAjF,CArBM,CAAT,CApbI,CA2cJ;;AACArC,EAAAA,SAAS,CAAC,MAAM;AACd,WAAO,MAAM;AACX,UAAImD,kBAAkB,CAACoB,OAAvB,EAAgC;AAC9BsF,QAAAA,YAAY,CAAC1G,kBAAkB,CAACoB,OAApB,CAAZ;AACD;AACF,KAJD;AAKD,GANQ,EAMN,EANM,CAAT;AAQA,sBACE;AAAK,IAAA,SAAS,EAAE1E,MAAM,CAACyK,cAAvB;AAAA,cACGrI,mBAAmB,CAACsI,OAApB,KAAgC,CAAhC,gBAAoC,QAAC,SAAD;AAAW,MAAA,oBAAoB,EAAEvI,oBAAjC;AAAuD,MAAA,mBAAmB,EAAEC;AAA5E;AAAA;AAAA;AAAA;AAAA,YAApC,gBACC;AAAA,iBACGH,KAAK,iBAAI,QAAC,YAAD;AAAc,QAAA,KAAK,EAAEA,KAArB;AAA4B,QAAA,OAAO,EAAE,MAAMC,QAAQ,CAAC,IAAD;AAAnD;AAAA;AAAA;AAAA;AAAA,cADZ,eAEE;AAAQ,QAAA,SAAS,EAAC,uDAAlB;AAAA,gCACE;AACE,UAAA,OAAO,EAAE,MAAM0C,cAAc,CAAC,MAAD,CAD/B;AAEE,UAAA,SAAS,EAAG,OAAM5E,MAAM,CAAC2K,MAAO,SAAQhJ,SAAS,KAAK,MAAd,GAAuB3B,MAAM,CAAC4K,YAA9B,GAA6C,EAClF,EAHL;AAAA,+BAKSpJ,KAAK,CAAC4C,MAAN,GAAe,CAAf,GAAoB,IAAG5C,KAAK,CAAC4C,MAAO,GAApC,GAAyC,EALlD;AAAA;AAAA;AAAA;AAAA;AAAA,gBADF,eAQE;AACE,UAAA,OAAO,EAAE,MAAMQ,cAAc,CAAC,cAAD,CAD/B;AAEE,UAAA,SAAS,EAAG,OAAM5E,MAAM,CAAC2K,MAAO,SAAQhJ,SAAS,KAAK,cAAd,GAA+B3B,MAAM,CAAC4K,YAAtC,GAAqD,EAC1F,EAHL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBARF,eAeE;AACE,UAAA,SAAS,EAAC,WADZ;AAEE,UAAA,IAAI,EAAC,QAFP;AAGE,UAAA,OAAO,EAAE,MAAM9E,WAAW,CAAC,GAAD,CAH5B;AAAA,iCAKE,QAAC,MAAD;AAAA;AAAA;AAAA;AAAA;AALF;AAAA;AAAA;AAAA;AAAA,gBAfF,eAsBE;AACE,UAAA,SAAS,EAAC,WADZ;AAEE,UAAA,IAAI,EAAC,QAFP;AAGE,UAAA,OAAO,EAAE,MAAMA,WAAW,CAAC,GAAD,CAH5B;AAAA,iCAKE,QAAC,QAAD;AAAA;AAAA;AAAA;AAAA;AALF;AAAA;AAAA;AAAA;AAAA,gBAtBF,eA6BE;AACE,UAAA,SAAS,EAAC,WADZ;AAEE,UAAA,IAAI,EAAC,QAFP;AAGE,UAAA,OAAO,EAAE,MAAMA,WAAW,CAAC,GAAD,CAH5B;AAAA,iCAKE,QAAC,eAAD;AAAA;AAAA;AAAA;AAAA;AALF;AAAA;AAAA;AAAA;AAAA,gBA7BF,eAsCE;AAAK,UAAA,SAAS,EAAC,eAAf;AAAA,kCACE;AACE,YAAA,SAAS,EAAG,OAAMlD,oBAAoB,GAAG,aAAH,GAAmB,uBAAwB,4CADnF;AAEE,YAAA,IAAI,EAAC,QAFP;AAGE,YAAA,OAAO,EAAEqH,sBAHX;AAIE,YAAA,KAAK,EAAE;AAAEY,cAAAA,QAAQ,EAAE,UAAZ;AAAwBC,cAAAA,OAAO,EAAE;AAAjC,aAJT;AAAA,oCAME,QAAC,UAAD;AAAY,cAAA,SAAS,EAAC;AAAtB;AAAA;AAAA;AAAA;AAAA,oBANF,EAOGpI,gBAAgB,GACf,oBAAAM,SAAS,CAAC+H,IAAV,CAAeC,IAAI,IAAIA,IAAI,CAACvD,IAAL,KAAc/E,gBAArC,qEAAwDkF,IAAxD,KAAgE,UADjD,GAEb,UATN;AAAA;AAAA;AAAA;AAAA;AAAA,kBADF,EAcG9E,oBAAoB,iBACnB;AAAK,YAAA,SAAS,EAAC,oBAAf;AAAoC,YAAA,KAAK,EAAE;AAAEmI,cAAAA,SAAS,EAAE,OAAb;AAAsBC,cAAAA,SAAS,EAAE;AAAjC,aAA3C;AAAA,oCACE;AAAK,cAAA,SAAS,EAAC,iBAAf;AAAA,qCACE;AAAO,gBAAA,SAAS,EAAC,YAAjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,oBADF,eAIE;AACE,cAAA,SAAS,EAAG,iBAAgB,CAACxI,gBAAD,GAAoB,QAApB,GAA+B,EAAG,EADhE;AAEE,cAAA,OAAO,EAAE,MAAMoH,oBAAoB,CAAC,EAAD,CAFrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAJF,eAUE;AAAK,cAAA,SAAS,EAAC;AAAf;AAAA;AAAA;AAAA;AAAA,oBAVF,EAYG5G,gBAAgB,gBACf;AAAK,cAAA,SAAS,EAAC,gCAAf;AAAA,qCACE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,oBADe,GAKfF,SAAS,CAACuE,GAAV,CAAe4D,QAAD,iBACZ;AAEE,cAAA,SAAS,EAAG,iBAAgBzI,gBAAgB,KAAKyI,QAAQ,CAAC1D,IAA9B,GAAqC,QAArC,GAAgD,EAAG,EAFjF;AAGE,cAAA,OAAO,EAAE,MAAMqC,oBAAoB,CAACqB,QAAQ,CAAC1D,IAAV,CAHrC;AAAA,wBAKG0D,QAAQ,CAACvD;AALZ,eACOuD,QAAQ,CAAC1D,IADhB;AAAA;AAAA;AAAA;AAAA,oBADF,CAjBJ;AAAA;AAAA;AAAA;AAAA;AAAA,kBAfJ,EA8CG3E,oBAAoB,iBACnB;AACE,YAAA,SAAS,EAAC,0CADZ;AAEE,YAAA,KAAK,EAAE;AAAEsI,cAAAA,MAAM,EAAE;AAAV,aAFT;AAGE,YAAA,OAAO,EAAE,MAAMrI,uBAAuB,CAAC,KAAD;AAHxC;AAAA;AAAA;AAAA;AAAA,kBA/CJ;AAAA;AAAA;AAAA;AAAA;AAAA,gBAtCF;AAAA;AAAA;AAAA;AAAA;AAAA,cAFF,EAmGGY,iBAAiB,CAACS,MAAlB,gBACC;AAAK,QAAA,SAAS,EAAEpE,MAAM,CAACqL,oBAAvB;AAAA,gCACE;AAAI,UAAA,SAAS,EAAErL,MAAM,CAACsL,eAAtB;AAAA,oBACG3H,iBAAiB,CAAC4D,GAAlB,CAAsB,CAACgE,QAAD,EAAWC,KAAX,kBACrB;AAEE,YAAA,SAAS,EAAExL,MAAM,CAACyL,cAFpB;AAGE,YAAA,OAAO,EAAE,MAAM3H,qBAAqB,CAACyH,QAAD,CAHtC;AAAA,sBAKGA;AALH,aACOC,KADP;AAAA;AAAA;AAAA;AAAA,kBADD;AADH;AAAA;AAAA;AAAA;AAAA,gBADF,eAYE;AACE,UAAA,SAAS,EAAC,+DADZ;AAEE,UAAA,KAAK,EAAE;AAAEX,YAAAA,QAAQ,EAAE,OAAZ;AAAqBa,YAAAA,KAAK,EAAE;AAA5B,WAFT;AAAA,iCAIE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJF;AAAA;AAAA;AAAA;AAAA,gBAZF;AAAA;AAAA;AAAA;AAAA;AAAA,cADD,GAoBG,IAvHN,eAyHE;AAAK,QAAA,SAAS,EAAE1L,MAAM,CAAC2L,QAAvB;AAAA,+BACE,QAAC,KAAD;AACE,UAAA,WAAW,EAAErJ,WADf;AAC4B,UAAA,cAAc,EAAEC,cAD5C;AAEE,UAAA,WAAW,EAAEF,WAFf;AAGE,UAAA,qBAAqB,EAAEyB,qBAHzB;AAIE,UAAA,aAAa,EAAEF,aAJjB;AAKE,UAAA,2BAA2B,EAAEC,2BAL/B;AAME,UAAA,iBAAiB,EAAEF,iBANrB;AAOE,UAAA,cAAc,EAAE7B,IAAI,CAAC8J,cAPvB;AAQE,UAAA,QAAQ,EAAE9J,IARZ;AASE,UAAA,wBAAwB,EAAEoI,wBAT5B;AAUE,UAAA,4BAA4B,EAAEK;AAVhC;AAAA;AAAA;AAAA;AAAA;AADF;AAAA;AAAA;AAAA;AAAA,cAzHF,EAyIGnH,gBAAgB,iBACf;AAAK,QAAA,SAAS,EAAC,sDAAf;AAAsE,QAAA,IAAI,EAAC,OAA3E;AAAA,gCACE;AAAA,oBAAQA;AAAR;AAAA;AAAA;AAAA;AAAA,gBADF,eAEE;AACE,UAAA,IAAI,EAAC,QADP;AAEE,UAAA,SAAS,EAAC,WAFZ;AAGE,UAAA,OAAO,EAAE,MAAMC,mBAAmB,CAAC,EAAD,CAHpC;AAIE,wBAAW;AAJb;AAAA;AAAA;AAAA;AAAA,gBAFF;AAAA;AAAA;AAAA;AAAA;AAAA,cA1IJ,EAsJGT,oBAAoB,IAAIF,gBAAxB,iBACC;AAAK,QAAA,SAAS,EAAC,6DAAf;AAAA,gCACE;AAAO,UAAA,SAAS,EAAC,YAAjB;AAAA,kEACwBM,SAAS,CAAC+H,IAAV,CAAeC,IAAI,IAAIA,IAAI,CAACvD,IAAL,KAAc/E,gBAArC,CADxB,qDACwB,iBAAwDkF,IADhF;AAAA;AAAA;AAAA;AAAA;AAAA,gBADF,eAIE;AAAO,UAAA,SAAS,EAAC,YAAjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAJF;AAAA;AAAA;AAAA;AAAA;AAAA,cAvJJ;AAAA;AAFJ;AAAA;AAAA;AAAA;AAAA,UADF;AAuKD,CApoBD;;GAAMtG,Q;UAiCFd,Y;;;KAjCEc,Q;AAsoBN,eAAeA,QAAf", "sourcesContent": ["import React from \"react\";\r\nimport styles from \"./inputBar.module.css\"; // Import the CSS module\r\nimport { useContext, useState, useEffect, useRef, useCallback } from \"react\";\r\nimport { ChatContext } from \"../../context/ChatContext\";\r\nimport Input from \"../Input\";\r\nimport useSentences from \"../../customHooks/useSentences\";\r\nimport { ChatState } from \"../../context/AllProviders\";\r\nimport { FaBold } from \"react-icons/fa\";\r\nimport { FaItalic } from \"react-icons/fa\";\r\nimport { FaStrikethrough } from \"react-icons/fa\";\r\nimport { FaLanguage } from \"react-icons/fa\";\r\nimport ReplyPreview from \"../ReplyPreview/ReplyPreview\";\r\nimport BlockCard from \"../BlockCard\";\r\nimport { AuthContext } from \"../../context/AuthContext\";\r\nimport axios from \"axios\";\r\nimport { BASE_URL2 } from \"../../api/api\";\r\nimport { toast } from \"react-toastify\";\r\nimport { TRANSLATION_CONFIG, getTranslationUrl } from \"../../config/translation\";\r\nconst Inputbar = ({\r\n  setFaqOpen,\r\n  notes,\r\n  setshowNotesCard,\r\n  setShowContactDetail,\r\n  activeTab,\r\n  setActiveTab,\r\n  setshowQuickReply,\r\n\r\n}) => {\r\n  const { data } = useContext(ChatContext);\r\n  const { text, setText, reply, setReply, selectedMobileNumber, selectedUserDetails } = ChatState();\r\n  const textareaRef = useRef(null);\r\n  const [showPreview, setShowPreview] = useState(false);\r\n  const { currentUser } = useContext(AuthContext);\r\n  const lastTypingTimeRef = useRef(0);\r\n\r\n  // Translation states\r\n  const [selectedLanguage, setSelectedLanguage] = useState('');\r\n  const [isTranslationEnabled, setIsTranslationEnabled] = useState(false);\r\n  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);\r\n  const [languages, setLanguages] = useState([]);\r\n  const [loadingLanguages, setLoadingLanguages] = useState(false);\r\n  const [translationError, setTranslationError] = useState('');\r\n\r\n  // Translation refs\r\n  const debounceTimeoutRef = useRef(null);\r\n  const lastTranslatedWordRef = useRef('');\r\n  const isTranslatingRef = useRef(false);\r\n  const DEBOUNCE_DELAY = TRANSLATION_CONFIG.SETTINGS.DEBOUNCE_DELAY;\r\n\r\n\r\n  const { filteredSentences, saveSentences, splitParagraphIntoSentences } =\r\n    useSentences();\r\n\r\n  const handleSuggestionClick = (suggestedText) => {\r\n    setText((prevState) => {\r\n      // Split the current input into words\r\n      const words = prevState.split(/\\s+/);\r\n\r\n      // Check if the last word matches the suggestion\r\n      const lastWord = words[words.length - 1];\r\n      const suggestionFirstWord = suggestedText.split(/\\s+/)[0];\r\n\r\n      if (suggestionFirstWord.toLowerCase().includes(lastWord.toLowerCase())) {\r\n        // If the last word matches the suggestion, replace it\r\n        words[words.length - 1] = suggestedText;\r\n      } else {\r\n        // Otherwise, append the suggestion\r\n        words.push(suggestedText);\r\n      }\r\n      // Join the words back into a single string and return\r\n      return words.join(\" \");\r\n    });\r\n    textareaRef.current?.focus();\r\n  };\r\n  const handleTabClick = (tabName) => {\r\n    setActiveTab(tabName);\r\n\r\n    if (tabName === \"Note\") {\r\n      setshowNotesCard((prevState) => {\r\n        const newState = !prevState; // Calculate the new state\r\n        if (!newState) {\r\n          // If the new state is `false`, reset the active tab to \"\"\r\n          setActiveTab(\"\");\r\n        }\r\n        return newState;\r\n      });\r\n      setShowContactDetail(false);\r\n      setFaqOpen(false);\r\n      setshowQuickReply(false);\r\n    } else if (tabName === \"quickReplies\") {\r\n      setshowQuickReply((prevState) => {\r\n        const newState = !prevState; // Calculate the new state\r\n        if (!newState) {\r\n          // If the new state is `false`, reset the active tab to \"\"\r\n          setActiveTab(\"\");\r\n        }\r\n        return newState;\r\n      });\r\n      setShowContactDetail(false);\r\n      setFaqOpen(false);\r\n      setshowNotesCard(false);\r\n    }\r\n  };\r\n\r\n\r\n  const showTypingStatus = async () => {\r\n    const now = Date.now();\r\n    if (now - lastTypingTimeRef.current < 25000) {\r\n      return;\r\n    }\r\n\r\n    lastTypingTimeRef.current = now;\r\n    const payload = {\r\n      user_id: currentUser.user_id,\r\n      token: currentUser.token,\r\n      method: \"typing\",\r\n      user_type: currentUser.user_type,\r\n      mobile: selectedMobileNumber,\r\n      brand_number: currentUser.brand_number,\r\n\r\n    }\r\n    try {\r\n\r\n      const { data } = await axios.post(`${BASE_URL2}/conversation`, payload);\r\n\r\n    } catch (error) {\r\n      if (error.response && error.response.status === 429) {\r\n        toast.error(\"Too many requests. Please try again later.\")\r\n\r\n      } else {\r\n        console.error(error);\r\n        toast.error(\"Something went wrong, please try again later\")\r\n      }\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    if (!text || !currentUser.user_id\r\n      || !currentUser.user_type || !currentUser.brand_number\r\n      || !currentUser.token) return;\r\n\r\n    showTypingStatus();\r\n  }, [text, currentUser]);\r\n\r\n\r\n  const applyFormat = (wrapSymbol) => {\r\n    const input = textareaRef.current;\r\n    if (!input) return;\r\n\r\n    const startPos = input.selectionStart;\r\n    const endPos = input.selectionEnd;\r\n\r\n    const beforeSelection = text.substring(0, startPos);\r\n    const selectedText = text.substring(startPos, endPos);\r\n    const afterSelection = text.substring(endPos);\r\n\r\n    if (selectedText === \"\") {\r\n      const newText = beforeSelection + wrapSymbol + wrapSymbol + afterSelection;\r\n      setText(newText);\r\n      setShowPreview(true);\r\n\r\n      // Move cursor between added symbols\r\n      setTimeout(() => {\r\n        input.focus();\r\n        input.setSelectionRange(startPos + wrapSymbol.length, startPos + wrapSymbol.length);\r\n      }, 10);\r\n    } else {\r\n      const newText = beforeSelection + wrapSymbol + selectedText + wrapSymbol + afterSelection;\r\n      setText(newText);\r\n      setShowPreview(true);\r\n\r\n      setTimeout(() => input.focus(), 10);\r\n    }\r\n  };\r\n\r\n  // Translation functions\r\n  const fetchLanguages = useCallback(async () => {\r\n    if (languages.length > 0) return; // Already loaded\r\n    if (!currentUser?.parent_id || !currentUser?.parent_token) return; // No auth data\r\n\r\n    setLoadingLanguages(true);\r\n    try {\r\n      const payload = {\r\n        user_id: currentUser.parent_id,\r\n        token: currentUser.parent_token,\r\n        method: \"language_list\"\r\n      };\r\n\r\n      const response = await axios.post(getTranslationUrl(TRANSLATION_CONFIG.ENDPOINTS.GOOGLE), payload);\r\n      console.log('Language API response:', response.data); // Debug log\r\n\r\n      if (response.data.success) {\r\n        const languagesData = response.data.data;\r\n        console.log('Raw language data:', languagesData); // Debug log\r\n\r\n        let languageArray = [];\r\n\r\n        // Handle different possible data structures\r\n        if (Array.isArray(languagesData)) {\r\n          // If it's already an array\r\n          languageArray = languagesData.map(item => ({\r\n            code: item.code || item.id || item.key,\r\n            name: String(item.name || item.label || item.value || item.code || item.id)\r\n          }));\r\n        } else if (typeof languagesData === 'object' && languagesData !== null) {\r\n          // If it's an object, convert to array\r\n          languageArray = Object.entries(languagesData).map(([code, name]) => ({\r\n            code,\r\n            name: String(name || code)\r\n          }));\r\n        }\r\n\r\n        // Sort languages alphabetically by name (with safety check)\r\n        if (languageArray.length > 0) {\r\n          languageArray.sort((a, b) => {\r\n            const nameA = String(a.name || '');\r\n            const nameB = String(b.name || '');\r\n            return nameA.localeCompare(nameB);\r\n          });\r\n        }\r\n\r\n        console.log('Processed language array:', languageArray); // Debug log\r\n\r\n        if (languageArray.length === 0) {\r\n          console.warn('No languages found in API response');\r\n          // Set a fallback with common languages\r\n          setLanguages([\r\n            { code: 'hi', name: 'Hindi' },\r\n            { code: 'es', name: 'Spanish' },\r\n            { code: 'fr', name: 'French' },\r\n            { code: 'de', name: 'German' }\r\n          ]);\r\n        } else {\r\n          setLanguages(languageArray);\r\n        }\r\n      } else {\r\n        throw new Error(response.data.message || 'Failed to fetch languages');\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to fetch languages:', error);\r\n      setTranslationError('Failed to load languages');\r\n      setTimeout(() => setTranslationError(''), 3000);\r\n    } finally {\r\n      setLoadingLanguages(false);\r\n    }\r\n  }, [languages.length, currentUser]);\r\n\r\n  const translateWordSilently = useCallback(async (word, wordStart, wordEnd, currentCursorPos) => {\r\n    if (!word.trim() || !selectedLanguage || isTranslatingRef.current || !isTranslationEnabled) {\r\n      return;\r\n    }\r\n\r\n    if (!currentUser?.parent_id || !currentUser?.parent_token) {\r\n      return;\r\n    }\r\n\r\n    if (word === lastTranslatedWordRef.current) {\r\n      return;\r\n    }\r\n\r\n    const textarea = textareaRef.current;\r\n    if (!textarea) return;\r\n\r\n    // Prevent multiple simultaneous translations\r\n    isTranslatingRef.current = true;\r\n\r\n    try {\r\n      // Store current selection to preserve it\r\n      const selectionStart = textarea.selectionStart;\r\n      const selectionEnd = textarea.selectionEnd;\r\n\r\n      // Get current text\r\n      const currentText = textarea.value;\r\n\r\n      // Translate the word\r\n      const payload = {\r\n        user_id: currentUser.parent_id,\r\n        token: currentUser.parent_token,\r\n        method: \"translate\",\r\n        target_lang: selectedLanguage,\r\n        text: word\r\n      };\r\n\r\n      const response = await axios.post(\r\n        getTranslationUrl(TRANSLATION_CONFIG.ENDPOINTS.GOOGLE),\r\n        payload\r\n      );\r\n\r\n      if (!response.data.success) {\r\n        throw new Error(response.data.message || 'Translation failed');\r\n      }\r\n\r\n      const translatedWord = response.data.translated_text;\r\n\r\n      // Create new text with translated word\r\n      const newText =\r\n        currentText.substring(0, wordStart) +\r\n        translatedWord +\r\n        currentText.substring(wordEnd);\r\n\r\n      // Update textarea value and React state\r\n      setText(newText);\r\n\r\n      // Calculate new cursor position\r\n      const lengthDifference = translatedWord.length - word.length;\r\n      let newCursorPos;\r\n\r\n      if (currentCursorPos <= wordEnd) {\r\n        // Cursor was before or at the end of the translated word\r\n        if (currentCursorPos <= wordStart) {\r\n          newCursorPos = currentCursorPos; // Before the word, no change\r\n        } else {\r\n          newCursorPos = wordEnd + lengthDifference; // At the end of translated word\r\n        }\r\n      } else {\r\n        // Cursor was after the word\r\n        newCursorPos = currentCursorPos + lengthDifference;\r\n      }\r\n\r\n      // Restore cursor position after React update\r\n      setTimeout(() => {\r\n        if (textarea) {\r\n          textarea.setSelectionRange(newCursorPos, newCursorPos);\r\n          textarea.focus();\r\n        }\r\n      }, 10);\r\n\r\n      lastTranslatedWordRef.current = translatedWord;\r\n\r\n    } catch (err) {\r\n      console.error('Translation error:', err);\r\n      setTranslationError('Translation failed. Please try again.');\r\n      setTimeout(() => setTranslationError(''), 3000);\r\n    } finally {\r\n      isTranslatingRef.current = false;\r\n    }\r\n  }, [selectedLanguage, isTranslationEnabled, currentUser]);\r\n\r\n  // Find completed word when space is pressed\r\n  const findCompletedWord = (text, cursorPos) => {\r\n    // Look backwards from cursor position to find the completed word\r\n    if (cursorPos > 0 && text[cursorPos - 1] === ' ') {\r\n      let wordEnd = cursorPos - 1; // Position of space\r\n      let wordStart = wordEnd;\r\n\r\n      // Find start of word (go backwards until space or beginning)\r\n      while (wordStart > 0 && text[wordStart - 1] !== ' ') {\r\n        wordStart--;\r\n      }\r\n\r\n      const word = text.substring(wordStart, wordEnd);\r\n      return {\r\n        word: word.trim(),\r\n        start: wordStart,\r\n        end: wordEnd\r\n      };\r\n    }\r\n    return null;\r\n  };\r\n\r\n  // Find current word being typed (for auto-translation)\r\n  const findCurrentWord = (text, cursorPos) => {\r\n    let start = cursorPos;\r\n    let end = cursorPos;\r\n\r\n    // Find word boundaries\r\n    while (start > 0 && text[start - 1] !== ' ') {\r\n      start--;\r\n    }\r\n    while (end < text.length && text[end] !== ' ') {\r\n      end++;\r\n    }\r\n\r\n    const word = text.substring(start, end);\r\n    return {\r\n      word: word.trim(),\r\n      start: start,\r\n      end: end\r\n    };\r\n  };\r\n\r\n  const handleLanguageSelect = (languageCode) => {\r\n    setSelectedLanguage(languageCode);\r\n    setIsTranslationEnabled(!!languageCode);\r\n    setShowLanguageDropdown(false);\r\n\r\n    // Reset translation references\r\n    lastTranslatedWordRef.current = '';\r\n\r\n    if (debounceTimeoutRef.current) {\r\n      clearTimeout(debounceTimeoutRef.current);\r\n    }\r\n  };\r\n\r\n  const toggleLanguageDropdown = () => {\r\n    if (!showLanguageDropdown) {\r\n      fetchLanguages();\r\n    }\r\n    setShowLanguageDropdown(!showLanguageDropdown);\r\n  };\r\n\r\n  // Handle space key press for instant translation\r\n  const handleTranslationKeyDown = useCallback((e) => {\r\n    if (e.key === ' ' && !isTranslatingRef.current && isTranslationEnabled) {\r\n      const textarea = e.target;\r\n\r\n      // Clear any pending auto-translation\r\n      if (debounceTimeoutRef.current) {\r\n        clearTimeout(debounceTimeoutRef.current);\r\n      }\r\n\r\n      // Use requestAnimationFrame to ensure space is processed first\r\n      requestAnimationFrame(() => {\r\n        const text = textarea.value;\r\n        const cursorPos = textarea.selectionStart;\r\n\r\n        const completedWord = findCompletedWord(text, cursorPos);\r\n        if (completedWord && completedWord.word) {\r\n          translateWordSilently(\r\n            completedWord.word,\r\n            completedWord.start,\r\n            completedWord.end,\r\n            cursorPos\r\n          );\r\n        }\r\n      });\r\n    }\r\n  }, [translateWordSilently, isTranslationEnabled, findCompletedWord]);\r\n\r\n  // Handle input for auto-translation (debounced)\r\n  const handleTranslationInputChange = useCallback((currentText) => {\r\n    if (!isTranslationEnabled || !textareaRef.current) return;\r\n\r\n    // Clear existing timeout\r\n    if (debounceTimeoutRef.current) {\r\n      clearTimeout(debounceTimeoutRef.current);\r\n    }\r\n\r\n    // Set timeout for auto-translation\r\n    if (!isTranslatingRef.current) {\r\n      debounceTimeoutRef.current = setTimeout(() => {\r\n        const textarea = textareaRef.current;\r\n        if (!textarea) return;\r\n\r\n        const text = textarea.value;\r\n        const cursorPos = textarea.selectionStart;\r\n\r\n        if (text.trim()) {\r\n          const currentWord = findCurrentWord(text, cursorPos);\r\n          if (currentWord.word && currentWord.word !== lastTranslatedWordRef.current) {\r\n            translateWordSilently(\r\n              currentWord.word,\r\n              currentWord.start,\r\n              currentWord.end,\r\n              cursorPos\r\n            );\r\n          }\r\n        }\r\n      }, DEBOUNCE_DELAY);\r\n    }\r\n  }, [isTranslationEnabled, translateWordSilently, findCurrentWord]);\r\n\r\n  // Effect to handle language change\r\n  useEffect(() => {\r\n    if (!isTranslationEnabled || !textareaRef.current || !currentUser?.parent_id || !currentUser?.parent_token) return;\r\n\r\n    const textarea = textareaRef.current;\r\n    if (textarea && textarea.value.trim() && !isTranslatingRef.current) {\r\n      const text = textarea.value;\r\n      const cursorPos = textarea.selectionStart;\r\n      const currentWord = findCurrentWord(text, cursorPos);\r\n\r\n      if (currentWord.word) {\r\n        translateWordSilently(\r\n          currentWord.word,\r\n          currentWord.start,\r\n          currentWord.end,\r\n          cursorPos\r\n        );\r\n      }\r\n    }\r\n\r\n    // Reset references\r\n    lastTranslatedWordRef.current = '';\r\n  }, [selectedLanguage, isTranslationEnabled, translateWordSilently, findCurrentWord, currentUser]);\r\n\r\n  // Cleanup effect\r\n  useEffect(() => {\r\n    return () => {\r\n      if (debounceTimeoutRef.current) {\r\n        clearTimeout(debounceTimeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div className={styles.inputContainer}>\r\n      {selectedUserDetails.isBlock === 1 ? <BlockCard selectedMobileNumber={selectedMobileNumber} selectedUserDetails={selectedUserDetails} /> :\r\n        <>\r\n          {reply && <ReplyPreview reply={reply} onClose={() => setReply(null)} />}\r\n          <header className=\"d-flex justify-content-start align-items-center w-100\">\r\n            <button\r\n              onClick={() => handleTabClick(\"Note\")}\r\n              className={`btn ${styles.button} ms-2 ${activeTab === \"Note\" ? styles.activeButton : \"\"\r\n                }`}\r\n            >\r\n              Notes {notes.length > 0 ? `(${notes.length})` : \"\"}\r\n            </button>\r\n            <button\r\n              onClick={() => handleTabClick(\"quickReplies\")}\r\n              className={`btn ${styles.button} ms-2 ${activeTab === \"quickReplies\" ? styles.activeButton : \"\"\r\n                }`}\r\n            >\r\n              Quick Replies\r\n            </button>\r\n            <div\r\n              className=\"mx-1 px-1\"\r\n              role=\"button\"\r\n              onClick={() => applyFormat(\"*\")}\r\n            >\r\n              <FaBold />\r\n            </div>\r\n            <div\r\n              className=\"mx-1 px-1\"\r\n              role=\"button\"\r\n              onClick={() => applyFormat(\"_\")}\r\n            >\r\n              <FaItalic />\r\n            </div>\r\n            <div\r\n              className=\"mx-1 px-1\"\r\n              role=\"button\"\r\n              onClick={() => applyFormat(\"~\")}\r\n            >\r\n              <FaStrikethrough />\r\n            </div>\r\n\r\n            {/* Language Translation Dropdown */}\r\n            <div className=\"dropdown mx-1\">\r\n              <button\r\n                className={`btn ${isTranslationEnabled ? 'btn-primary' : 'btn-outline-secondary'} dropdown-toggle d-flex align-items-center`}\r\n                type=\"button\"\r\n                onClick={toggleLanguageDropdown}\r\n                style={{ fontSize: '0.875rem', padding: '0.25rem 0.5rem' }}\r\n              >\r\n                <FaLanguage className=\"me-1\" />\r\n                {selectedLanguage ?\r\n                  languages.find(lang => lang.code === selectedLanguage)?.name || 'Language'\r\n                  : 'Language'\r\n                }\r\n              </button>\r\n\r\n              {showLanguageDropdown && (\r\n                <div className=\"dropdown-menu show\" style={{ maxHeight: '300px', overflowY: 'auto' }}>\r\n                  <div className=\"dropdown-header\">\r\n                    <small className=\"text-muted\">Select language for translation</small>\r\n                  </div>\r\n                  <button\r\n                    className={`dropdown-item ${!selectedLanguage ? 'active' : ''}`}\r\n                    onClick={() => handleLanguageSelect('')}\r\n                  >\r\n                    Disable Translation\r\n                  </button>\r\n                  <div className=\"dropdown-divider\"></div>\r\n\r\n                  {loadingLanguages ? (\r\n                    <div className=\"dropdown-item-text text-center\">\r\n                      <small>Loading languages...</small>\r\n                    </div>\r\n                  ) : (\r\n                    languages.map((language) => (\r\n                      <button\r\n                        key={language.code}\r\n                        className={`dropdown-item ${selectedLanguage === language.code ? 'active' : ''}`}\r\n                        onClick={() => handleLanguageSelect(language.code)}\r\n                      >\r\n                        {language.name}\r\n                      </button>\r\n                    ))\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Overlay to close dropdown */}\r\n              {showLanguageDropdown && (\r\n                <div\r\n                  className=\"position-fixed top-0 start-0 w-100 h-100\"\r\n                  style={{ zIndex: 1040 }}\r\n                  onClick={() => setShowLanguageDropdown(false)}\r\n                />\r\n              )}\r\n            </div>\r\n          </header>\r\n\r\n\r\n\r\n          {/* Suggestion Section */}\r\n          {filteredSentences.length ? (\r\n            <div className={styles.suggestionsContainer}>\r\n              <ul className={styles.suggestionsList}>\r\n                {filteredSentences.map((sentence, index) => (\r\n                  <li\r\n                    key={index}\r\n                    className={styles.suggestionPill}\r\n                    onClick={() => handleSuggestionClick(sentence)}\r\n                  >\r\n                    {sentence}\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n              <div\r\n                className=\"d-none d-md-flex justify-content-center align-items-end w-100\"\r\n                style={{ fontSize: \".7rem\", color: \"grey\" }}\r\n              >\r\n                <div>Press Tab to add text</div>\r\n              </div>\r\n            </div>\r\n          ) : null}\r\n\r\n          <div className={styles.textArea}>\r\n            <Input\r\n              showPreview={showPreview} setShowPreview={setShowPreview}\r\n              textareaRef={textareaRef}\r\n              handleSuggestionClick={handleSuggestionClick}\r\n              saveSentences={saveSentences}\r\n              splitParagraphIntoSentences={splitParagraphIntoSentences}\r\n              filteredSentences={filteredSentences}\r\n              selectedMobile={data.selectedMobile}\r\n              convData={data}\r\n              handleTranslationKeyDown={handleTranslationKeyDown}\r\n              handleTranslationInputChange={handleTranslationInputChange}\r\n            />\r\n          </div>\r\n\r\n          {/* Translation Error Display */}\r\n          {translationError && (\r\n            <div className=\"alert alert-warning alert-dismissible fade show mt-2\" role=\"alert\">\r\n              <small>{translationError}</small>\r\n              <button\r\n                type=\"button\"\r\n                className=\"btn-close\"\r\n                onClick={() => setTranslationError('')}\r\n                aria-label=\"Close\"\r\n              ></button>\r\n            </div>\r\n          )}\r\n\r\n          {/* Translation Status */}\r\n          {isTranslationEnabled && selectedLanguage && (\r\n            <div className=\"d-flex justify-content-between align-items-center mt-2 px-2\">\r\n              <small className=\"text-muted\">\r\n                Translation enabled: {languages.find(lang => lang.code === selectedLanguage)?.name}\r\n              </small>\r\n              <small className=\"text-muted\">\r\n                Press space after each word for instant translation\r\n              </small>\r\n            </div>\r\n          )}\r\n        </>\r\n      }\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Inputbar;\r\n"]}, "metadata": {}, "sourceType": "module"}