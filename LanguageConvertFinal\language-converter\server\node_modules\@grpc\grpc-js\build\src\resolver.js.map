{"version": 3, "file": "resolver.js", "sourceRoot": "", "sources": ["../../src/resolver.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAuGH,4CAKC;AAOD,sDAEC;AAQD,wCAYC;AAOD,kDAMC;AAED,kDAaC;AAhKD,6CAAoD;AAwFpD,MAAM,mBAAmB,GAA8C,EAAE,CAAC;AAC1E,IAAI,aAAa,GAAkB,IAAI,CAAC;AAExC;;;;;;GAMG;AACH,SAAgB,gBAAgB,CAC9B,MAAc,EACd,aAAkC;IAElC,mBAAmB,CAAC,MAAM,CAAC,GAAG,aAAa,CAAC;AAC9C,CAAC;AAED;;;;GAIG;AACH,SAAgB,qBAAqB,CAAC,MAAc;IAClD,aAAa,GAAG,MAAM,CAAC;AACzB,CAAC;AAED;;;;;GAKG;AACH,SAAgB,cAAc,CAC5B,MAAe,EACf,QAA0B,EAC1B,OAAuB;IAEvB,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,MAAM,IAAI,mBAAmB,EAAE,CAAC;QACxE,OAAO,IAAI,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC3E,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CACb,2CAA2C,IAAA,wBAAW,EAAC,MAAM,CAAC,EAAE,CACjE,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,mBAAmB,CAAC,MAAe;IACjD,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,MAAM,IAAI,mBAAmB,EAAE,CAAC;QACxE,OAAO,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IACxE,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,kBAAkB,IAAA,wBAAW,EAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC;AAED,SAAgB,mBAAmB,CAAC,MAAe;IACjD,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,mBAAmB,CAAC,EAAE,CAAC;QAC3E,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;YAC3B,OAAO;gBACL,MAAM,EAAE,aAAa;gBACrB,SAAS,EAAE,SAAS;gBACpB,IAAI,EAAE,IAAA,wBAAW,EAAC,MAAM,CAAC;aAC1B,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC"}