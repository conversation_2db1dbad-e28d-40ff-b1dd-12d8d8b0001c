.labelColumn {
  min-width: 280px;
  max-width: 300px;
  display: flex;
  flex-direction: column;
}

.cardContainer {
  overflow-y: auto;
  flex-grow: 1;  /* Allows container to grow based on available space */
  padding: 10px; /* Prevent cards from touching edges */
  height: 100%;
  scroll-behavior: smooth; /* Smooth scrolling */
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
  
}

.arrowHeader {
  position: relative;
  color: white;
  font-weight: 600;
  text-align: center;
  padding: 8px 12px;
  margin-bottom: 1rem;
  clip-path: polygon(0 0, 90% 0, 100% 50%, 90% 100%, 0 100%);
  font-size: 14px;
}

.arrowHeader:first-child {
  border-radius: 4px 0 0 4px;
}

.arrowHeader:last-child {
  border-radius: 0 4px 4px 0;
}
.noContacts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
  font-size: 1.1rem;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px dashed #ced4da;
}
