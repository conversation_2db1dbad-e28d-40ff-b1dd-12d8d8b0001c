import axios from "axios";
import { useState, useEffect, useContext, useRef, useCallback } from "react";
import { BASE_URL, BASE_URL2 } from "../../api/api";
import { AuthContext } from "../../context/AuthContext";
import LeftMenu from "../../components/LeftMenu";
import { MdDownload } from "react-icons/md";
import { FiFilter } from "react-icons/fi";
import styles from './customers.module.css';
import DatePicker from "react-datepicker";
import { CSVLink } from 'react-csv';
import { useDebounce } from "../../customHooks/useDebounce";
import Broadcast from "../../components/Broadcast/Broadcast";
import { Link } from "react-router-dom";
import { useMaskNo } from "../../customHooks/useMaskNo";
import { FaAngleUp } from "react-icons/fa";
import LabelCard from "../../components/Labels/LabelCard";
import { CircularProgress } from "@mui/material";
import { formatLocalDate } from "../../utils/Utils";
import Select from "react-select";
const Customers = () => {
    const maskNo = useMaskNo();
    const [loading, setLoading] = useState(false);
    const { currentUser } = useContext(AuthContext);
    const [contacts, setContacts] = useState([]);
    const [filteredContacts, setFilteredContacts] = useState([]);
    const [filters, setFilters] = useState({});
    const [selectedContacts, setSelectedContacts] = useState([])
    const [searchQuery, setSearchQuery] = useState("");
    const [headers, setHeaders] = useState([]);
    const [dateRange, setDateRange] = useState([new Date().toISOString(), new Date().toISOString()]);
    const [fromDate, toDate] = dateRange;
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [templatePopUp, setTemplatePopUp] = useState(false);
    const [page, setPage] = useState(1);
    const [showGoToTop, setShowGoToTop] = useState(false);
    const [labels, setLabels] = useState([]);
    const [showFilters, setShowFilters] = useState(false);
    const filterRef = useRef(null);
    const [isFetching, setIsFetching] = useState(false);



    const scrollContainerRef = useRef(null);

    const debouncedSearchInput = useDebounce(searchQuery, 300);

    // Developer-configurable mapping for keys to display user-friendly labels
    const keyLabelMapping = {
        name: "Customer Name",
        email: "Email Address",
        mobile: "Mobile",
        update_time: "Date",
        team_name: "Team",
        agent_name: "Agent",
        first_msg: "First message",
        content: "Last Message",
        status: "Status",
        "label[0].name": "Label",
    };

    // Specify which keys should be displayed and filtered
    const headerKeys = ['name', 'email', 'mobile', 'team_name', 'agent_name', 'first_msg', 'content',
        'status', 'update_time', 'label[0].name']; // The keys for displaying in table

    // Keys for filtering (can be different from header keys)
    const filterKeys = ['team_name', 'first_msg'];

    useEffect(() => {

        fetchNewContactList();
    }, [currentUser, fromDate, toDate]);


    useEffect(() => {
        const applyFiltersAndSearch = () => {
            let tempContacts = [...contacts];

            // Apply filters based on selected keys
            Object.keys(filters).forEach((key) => {
                if (filters[key]) {

                    if (key === "label.name") {
                        tempContacts = tempContacts.filter((contact) =>
                            contact.label?.some((lbl) =>
                                (filters[key] || []).some((selected) =>
                                    lbl?.name?.toLowerCase().includes(selected.toLowerCase())
                                )
                            )
                        );
                    }
                    else if (key === "team_name" || key === "agent_name") {

                        tempContacts = tempContacts.filter((contact) =>
                            contact?.team_name?.toLowerCase().includes(filters[key].toLowerCase()) ||
                            contact?.agent_name?.toLowerCase().includes(filters[key].toLowerCase())
                        );
                    } else {
                        tempContacts = tempContacts.filter((contact) =>
                            key === "status"
                                ? contact?.status === filters[key] // Exact match for status
                                : String(contact[key])
                                    .toLowerCase()
                                    .includes(filters[key].toLowerCase())
                        );

                    }
                }
            });

            // Apply search query
            if (searchQuery) {
                const searchableKeys = ['name', 'email', 'mobile', 'content']; // Specify searchable fields
                tempContacts = tempContacts.filter((contact) =>
                    searchableKeys.some((key) =>
                        String(contact[key]).toLowerCase().includes(searchQuery.toLowerCase())
                    )
                );
            }

            setFilteredContacts(tempContacts);
        };

        applyFiltersAndSearch();
    }, [filters, searchQuery, contacts]);

    useEffect(() => {
        if (debouncedSearchInput !== undefined) {
            fetchSearch(debouncedSearchInput);

        }

    }, [debouncedSearchInput]);

    useEffect(() => {
        const fetchLabels = async () => {
            if (!currentUser || !currentUser.parent_token || !currentUser.parent_id) return;
            const body = {
                user_id: currentUser.parent_id,
                token: currentUser.parent_token,
                method: "wp_list_retrieve_all",
            };

            try {
                const response = await axios.post(`${BASE_URL2}/contact_list`, body);
                if (response.data.success) {
                    setLabels(response.data.data); // Store labels if fetch is successful

                }
            } catch (error) {
                console.error("Error fetching labels:", error);
            }
        };

        fetchLabels();
    }, [currentUser]);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (filterRef.current && !filterRef.current.contains(event.target)) {
                setShowFilters(false);
            }
        };

        if (showFilters) {
            document.addEventListener("mousedown", handleClickOutside);
        }
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, [showFilters]);

    const fetchSearch = async (searchInput) => {

        if (searchInput.trim() === "") {
            // If search is cleared, reset contacts
            // fetchContactList();
            return;
        }

        setContacts([]);

        let dataforsearchdata = {
            token: currentUser.parent_token,
            user_id: currentUser.parent_id,
            method: "left_menunew",
            search_keyword: searchInput,
            brand_number: currentUser.brand_number,
            user_type: currentUser.user_type,
            search_id: currentUser.user_id ? currentUser.user_id : "",
        };
        try {
            const { data } = await axios.post(
                `${BASE_URL}/netcore_conversation.php`,
                dataforsearchdata
            );

            if (data.success === true) {

                setContacts(data.data);
            } else {
                setContacts([]);
            }
        } catch (error) {
            console.error(error);
        }

    };

    const fetchNewContactList = async () => {
        if (!currentUser || !currentUser.token || !currentUser.user_id ||  !currentUser.user_type || !fromDate || !toDate) return;
        setIsFetching(true);
        setLoading(true);
        const payload = {
            token: currentUser.token,
            user_id: currentUser.user_id,
            method: "left_menu",
            brand_number: currentUser.brand_number,
            agent_id: 0,
            type: "whatsapp",
            from_date: formatLocalDate(fromDate),
            to_date: formatLocalDate(toDate),
            page: 1,
            user_type:currentUser.user_type
        };
        try {
            const { data } = await axios.post(
                `${BASE_URL2}/whatsapp_conv`,
                payload,

            );

            if (data.success === true) {
                // Helper function to determine status based on time
                const getStatus = (dateString) => {
                    const givenDate = new Date(dateString); // This is in UTC
                    const currentDate = new Date();

                    // Convert both to UTC timestamps for accurate comparison
                    const givenTimeUTC = givenDate.getTime();
                    const currentTimeUTC = currentDate.getTime();
                    const timeThresholdUTC = currentTimeUTC - 24 * 60 * 60 * 1000; // 24 hours ago in UTC

                    return givenTimeUTC < timeThresholdUTC ? "Inactive" : "Active";
                };

                const updatedContacts = data.data.map((contact) => ({
                    ...contact,
                    status: getStatus(contact.update_time),
                }));

                setContacts(updatedContacts);
                setFilteredContacts(updatedContacts)


                const dynamicHeaders = headerKeys.map((key) => ({
                    label: keyLabelMapping[key] || key.charAt(0).toUpperCase() + key.slice(1),
                    key,
                }));
                setHeaders(dynamicHeaders);

                // Initialize filters only for selected keys
                if (page === 1) {
                    setFilters(
                        filterKeys.reduce((acc, key) => {
                            acc[key] = "";
                            return acc;
                        }, {})
                    );
                }
            }
        } catch (error) {
            console.error("Error fetching contact list:", error);
        } finally {
            setIsFetching(false);
            setLoading(false);
        }

    }

    const filterHeaders = () => {
        return headers.filter(header =>
            header.key !== "label[0].name" &&
            header.key !== "update_time" &&
            selectedContacts.every(contact => contact[header.key] !== null)
        );
    };


    const handleFilterChange = (e) => {
        const { name, value } = e.target;

        setFilters((prev) => {
            // If cleared (empty array or string), remove the key
            if (Array.isArray(value) && value.length === 0) {
                const { [name]: _, ...rest } = prev;
                return rest;
            }
            if (value === "") {
                const { [name]: _, ...rest } = prev;
                return rest;
            }

            return {
                ...prev,
                [name]: value,
            };
        });
    };


    const handleDateChange = (dates) => {
        if (!dates) {
            setDateRange([null, null]);
            return;
        }

        const [start, end] = dates;

        // Ensure proper start and end limits
        const adjustedStart = start ? new Date(start.setHours(0, 0, 0, 0)) : null; // Start at 00:00:00
        const adjustedEnd = end ? new Date(end.setHours(23, 59, 59, 999)) : null; // End at 23:59:59

        setDateRange([adjustedStart, adjustedEnd]);

        if (adjustedStart && adjustedEnd) {
            setShowDatePicker(false);
            fetchNewContactList();
        }
    };


    const handleDateClick = () => {
        setShowDatePicker(prevState => !prevState); // Show the DatePicker when the input is clicked
    };


    const isFilterApplied = () => {
        return (
            Object.values(filters).some((value) => value)
        );
    };

    // Function to clear all filters
    const clearFilters = () => {
        setFilters({});
        setSearchQuery("");

    };

    const clearFilter = (filterKey) => {
        setFilters((prev) => {
            const updated = { ...prev };
            delete updated[filterKey];
            return updated;
        });
    };


    const labelOptions = labels.map((label) => ({
        value: label.name,
        label: label.name,
    }));

    // Get selected value from filters
    const selectedOption = labelOptions.find(
        (opt) => opt.value === filters["label.name"]
    );

    const downloadCSV = () => {
        return filteredContacts.map(contact => {
            const row = {};
            headerKeys.forEach((key) => {
                if (key === "label[0].name") {
                    row[key] = contact.label?.length
                        ? contact.label.map(lbl => lbl.name).join(", ")
                        : "No Label";
                } else if (key === "update_time") {
                    row[key] = contact[key] ? `${contact[key].split("T")[0]} ${contact[key].split("T")[1]}` : "N/A";
                } else {
                    row[key] = getNestedValue(contact, key) || "N/A";
                }
            });
            return row;
        });
    };

    const toggleSelectContact = (contact) => {
        setSelectedContacts((prevSelectedContacts) => {
            const isSelected = prevSelectedContacts.some((c) => c.mobile === contact.mobile);
            if (isSelected) {
                // Remove the contact from the array
                return prevSelectedContacts.filter((c) => c.mobile !== contact.mobile);
            } else {
                // Add the contact to the array
                return [...prevSelectedContacts, {
                    name: contact.name,
                    email: contact.email,
                    mobile: contact.mobile,
                    team_name: contact.team_name,
                    content: contact.content,
                    status: contact.status,
                    oldest_message_content: contact.first_msg,
                    agent_name: contact.agent_name

                }];
            }
        });
    };
    const toggleSelectAll = () => {
        if (selectedContacts.length === filteredContacts.length) {
            setSelectedContacts([]); // Deselect all
        } else {
            setSelectedContacts(
                filteredContacts.map((contact) => ({
                    name: contact.name,
                    email: contact.email,
                    mobile: contact.mobile,
                    team_name: contact.team_name,
                    content: contact.content,
                    status: contact.status,
                    oldest_message_content: contact.first_msg,
                    agent_name: contact.agent_name
                }))
            ); // Select all
        }
    };

    const handleScroll = () => {
        const { scrollTop, clientHeight, scrollHeight } =
            scrollContainerRef.current;

        const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;

        if (isAtBottom && !isFetching) {
            setPage(page + 1);
            const nextPage = page + 1

        }
        setShowGoToTop(scrollTop > clientHeight);
    };

    const scrollToTop = () => {
        if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollTo({ top: 0, behavior: 'smooth' });
        }
    };

    const getNestedValue = (obj, key) => {
        return key.split('.').reduce((acc, part) => {
            if (!acc) return undefined;

            // Handle array indexes like `label[0].name`
            if (part.includes('[') && part.includes(']')) {
                const [arrayKey, index] = part.split(/\[|\]/).filter(Boolean);
                return acc[arrayKey] && acc[arrayKey][parseInt(index, 10)];
            }

            return acc[part];
        }, obj);
    };


    const handleStartBroadcast = () => {
        const filteredHeaders = filterHeaders();
        setHeaders(filteredHeaders);
        setTemplatePopUp(true);

    }



    return (
        <div className={`${styles.broadcastContainer}`}>

            {/* Left Menu */}
            <LeftMenu />

            {/* Main Content */}
            <div className={`d-flex flex-column justify-content-start align-items-center p-3 ${styles.main}`}>
                <div className="d-flex flex-column flex-md-row justify-content-between w-100 align-items-center mb-3">
                    {/* First Section */}
                    <div className="d-flex w-100 w-md-25 justify-content-between align-items-center mb-2 mb-md-0 me-md-4">
                        <h4 className="fw-bold">Customers</h4>
                        <div className="d-flex align-items-center gap-3">
                            <div className={`${styles.iconWrapper} d-flex d-md-none `}
                                onClick={() => setShowFilters(!showFilters)}
                            >
                                {isFilterApplied() && <div className={styles.dot} />}
                                <FiFilter size={24} role="button" className="text-primary" />
                            </div>
                            <div className={`${styles.iconWrapper} d-flex d-md-none`}>
                                <CSVLink
                                    data={downloadCSV()}
                                    filename="contacts.csv"
                                    target="_blank"
                                    className="text-black"
                                >
                                    <MdDownload size={24} />
                                </CSVLink>
                            </div>

                        </div>

                    </div>
                    {/* Second Section */}
                    <div className="d-flex w-100 w-md-50 justify-content-end align-items-center">
                        {/* Search Input */}
                        {/* <div className={`me-md-2 w-100`}>
                            <input
                                type="text"
                                className={`form-control`}
                                placeholder="Search..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />

                        </div> */}
                        {/* CSV Download */}
                        <div className={`${styles.iconWrapper} d-none d-md-flex`}>
                            <CSVLink
                                data={downloadCSV()}
                                filename="contacts.csv"
                                target="_blank"
                                className="text-black"
                            >
                                <MdDownload size={24} />
                            </CSVLink>
                        </div>
                    </div>
                </div>

                <div ref={filterRef} className={`${styles.filterRow} ${showFilters ? styles.showFilter : ""}`}>
                    <div className="d-flex flex-wrap align-items-center justify-content-evenly">
                        {filterKeys.map((key) => (
                            <div key={key} className={`me-2 mb-2 ${styles.filterItem}`}>
                                <label htmlFor={`${key}Filter`}>{keyLabelMapping[key]}</label>
                                <input
                                    disabled={loading}
                                    type="text"
                                    id={`${key}Filter`}
                                    name={key}
                                    value={filters[key] || ""}
                                    onChange={handleFilterChange}
                                    className="form-control"
                                    placeholder={`Filter by ${keyLabelMapping[key]}${key === "team_name" ? '/Agent' : ''}`} />
                            </div>
                        ))}

                        <div className={`position-relative me-2 mb-2 ${styles.filterItem}`}>
                            <label>Date <span className="text-danger">*</span> </label>


                            <DatePicker
                                selectsRange
                                startDate={fromDate}
                                endDate={toDate}
                                onChange={(update) => handleDateChange(update)}
                                maxDate={fromDate instanceof Date ? new Date(fromDate.getFullYear(), fromDate.getMonth() + 1, fromDate.getDate()) : new Date()}
                                placeholderText="Select a date range"
                                className="form-control"
                                disabled={loading}
                            />



                        </div>

                        <div className={`me-2 mb-2 ${styles.filterItem}`}>
                            <label>Status</label>
                            <select name="status" id="statusFilter" className="form-control"
                                value={filters.status ? filters.status : ""}
                                onChange={handleFilterChange}
                                disabled={loading}
                            >
                                <option value="">Select Status</option>
                                <option value="Active">Active</option>
                                <option value="Inactive">Inactive</option>
                            </select>
                        </div>

                        <div className={`me-2 mb-2 ${styles.filterItem}`}>
                            <label>Labels</label>
                            <Select
                                name="label.name"
                                id="labelFilter"
                                options={labelOptions}
                                value={labelOptions.filter((option) =>
                                    (filters["label.name"] || []).includes(option.value)
                                )}
                                onChange={(selectedOptions) => {
                                    const selectedValues = selectedOptions ? selectedOptions.map((opt) => opt.value) : [];
                                    const event = {
                                        target: {
                                            name: "label.name",
                                            value: selectedValues,
                                        },
                                    };
                                    handleFilterChange(event);
                                }}
                                isDisabled={loading}
                                isClearable
                                isMulti
                                placeholder="Select labels"
                                menuPortalTarget={document.body}
                                styles={{
                                    menuPortal: (base) => ({ ...base, zIndex: 9999 }),
                                }}
                            />

                        </div>
                    </div>
                    {isFilterApplied() && (
                        <div>
                            <button
                                className={`${styles.filterRemoveBtn} btn-sm`}
                                onClick={clearFilters}
                            >
                                Remove
                            </button>
                        </div>


                    )}
                </div>

                <div className={`d-flex flex-column flex-md-row w-100 justify-content-between
                 align-items-center mb-2`} >
                    <div style={{
                        color: "maroon", fontSize: "1.2rem",
                        fontWeight: "bold"
                    }}
                        className={`${styles.totalLengthContainer}`}
                    >
                        Total Contacts: {filteredContacts.length}

                    </div>

                    <div className={`${styles.broadcastbtnContainer}`}>
                        <Link to={'/report/broadcast-report'} className="me-2">Broadcast Report</Link>
                        <button className="border rounded-2 btn btn-primary"
                            onClick={handleStartBroadcast}
                            disabled={selectedContacts.length === 0 || loading}
                        >Start broadcast</button>
                    </div>

                </div>


                {/* Table Section */}

                {loading ? <CircularProgress
                    style={{
                        position: 'fixed',
                        border: 'none',
                        width: '40px',
                        height: '40px',
                        bottom: "15vh",
                        cursor: 'pointer',

                    }}
                /> : <div
                    className={styles.tableContainer}

                    ref={scrollContainerRef}
                    onScroll={handleScroll}

                >
                    <table className="table">
                        <thead
                            style={{
                                position: "sticky",
                                top: 0, // Stick the header to the top
                                backgroundColor: "#f8f9fa", // Match Bootstrap table header color
                                zIndex: "50"

                            }}
                        >
                            <tr>
                                <th>
                                    <div className="d-flex justify-content-start align-items-center">
                                        <input
                                            type="checkbox"
                                            checked={
                                                selectedContacts.length === filteredContacts.length &&
                                                filteredContacts.length !== 0
                                            }
                                            onChange={toggleSelectAll}
                                            className={styles.customCheckbox}

                                        />
                                        <label
                                            htmlFor=""
                                            className="ml-2"
                                            style={{ fontWeight: "bold" }}
                                        >
                                            Select All
                                        </label>
                                        {selectedContacts.length !== 0 && <div>({selectedContacts.length})</div>}
                                    </div>
                                </th>
                                {headers.map((header) => (
                                    <th
                                        key={header.key}
                                        style={{ fontWeight: "bold" }}
                                    >
                                        {header.label}
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody>
                            {filteredContacts.length ? (
                                filteredContacts.map((contact, index) => (
                                    <tr
                                        key={index}
                                        style={{
                                            backgroundColor: index % 2 === 0 ? "white" : "whiteSmoke", // Alternating row colors
                                        }}
                                    >
                                        <td>
                                            <input
                                                type="checkbox"
                                                checked={selectedContacts.some((c) => c.mobile === contact.mobile)} // Track selected contacts by ID
                                                onChange={() => toggleSelectContact(contact)}
                                                className={styles.customCheckbox}

                                            />
                                        </td>
                                        {headers.map((header) => (
                                            <td
                                                key={`${index}-${header.key}`}
                                                style={
                                                    header.key === "status"
                                                        ? {
                                                            color:
                                                                contact.status === "Active" ? "green" : "red",

                                                        }
                                                        : {}
                                                }
                                            >
                                                {header.key === "mobile" ? (
                                                    currentUser.user_type === "admin" ? (
                                                        contact[header.key]
                                                    ) : (
                                                        maskNo(contact[header.key])
                                                    )
                                                ) : header.key === "content" ? (
                                                    <>

                                                        {contact[header.key]}
                                                    </>
                                                ) : header.key === "update_time" ? (
                                                    <>
                                                        {contact[header.key].split("T")[0]}
                                                    </>

                                                )
                                                    : header.key === "label[0].name" ? (
                                                        contact.label?.length ? (
                                                            <div className="d-flex flex-wrap gap-2">
                                                                {contact.label.map((lbl, idx) => (
                                                                    <LabelCard key={idx} labelData={lbl} />
                                                                ))}
                                                            </div>
                                                        ) : (
                                                            "No Label"
                                                        )
                                                    ) : (
                                                        getNestedValue(contact, header.key) || "N/A"

                                                    )}

                                            </td>
                                        ))}

                                    </tr>
                                ))
                            ) : (
                                <tr>
                                    <td colSpan={headers.length + 1} className="text-center">
                                        No Data Available
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>}

                {showGoToTop && (
                    <button
                        onClick={scrollToTop}
                        className={styles.goToTop}
                        style={{
                            position: 'fixed',
                            backgroundColor: 'lightGray',
                            color: 'black',
                            border: 'none',
                            borderRadius: '50%',
                            width: '40px',
                            height: '40px',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            cursor: 'pointer',
                            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
                        }}
                    >
                        <FaAngleUp size={20} />
                    </button>)}








            </div>
            {templatePopUp && (
                <div className="popup-agent">
                    <div className="assign-popup-content-agent" style={{ width: "80%", maxWidth: "800px", height: "100vh" }}>
                        <div style={{ float: "right", cursor: "pointer" }}>
                            <i
                                className="bx bx-x float-right"
                                style={{ fontSize: "26px" }}
                                onClick={() => setTemplatePopUp(false)}
                            ></i>
                        </div>

                        <Broadcast setTemplatePopUp={setTemplatePopUp}
                            setSelectedContacts={setSelectedContacts}
                            selectedContacts={selectedContacts} headers={headers} />

                    </div>
                </div>
            )}

        </div>
    );
}

export default Customers;
