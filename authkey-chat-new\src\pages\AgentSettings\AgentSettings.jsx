import React, { useEffect, useState, useContext } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import axios from "axios";
import { Switch, CircularProgress } from "@mui/material";
import { toast } from "react-toastify";
import LeftMenu from "../../components/LeftMenu";
import styles from "./agentSettings.module.css";
import { BASE_URL2 } from "../../api/api";
import { AuthContext } from "../../context/AuthContext";
import { ChatState } from "../../context/AllProviders";
const AgentSettings = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const agentId = searchParams.get("id");
  const agentName = searchParams.get("name");

  
  const{mobileVisible, setMobileVisible}=ChatState();
  const [keywords, setKeywords] = useState("");
  const [addedKeywords, setAddedKeywords] = useState([]);
  const [keywordSuggestions, setKeywordSuggestions] = useState([

  ]); // Placeholder for future debouncing
  const [loading, setLoading] = useState(false);

  const { currentUser } = useContext(AuthContext);

  useEffect(() => {
    if (!agentId || !agentName) {
      navigate("/agent-management/agent", { replace: true }); // Prevents navigating back to this page
    }
  }, [agentId, agentName, navigate]);



  useEffect(() => {
    fetchkeywords();

  }, [currentUser, agentId])


  

  const fetchkeywords = async () => {
    if (!currentUser || !currentUser.parent_id || !currentUser.parent_token || !agentId) return;
    const payload = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: "keyword_retrieve",
      agent_id: agentId,
    }

    try {
      const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent`, payload);
      if (data.success) {
        setAddedKeywords([...data.data.keywords])
      } else {
        setAddedKeywords([]);
      }
    } catch (error) {
      console.log(error);

    }
  }

  const handleShowMobile = async () => {
    if(!agentId || !agentName) return;
    const newStatus = mobileVisible ? 0 : 1;
    
    const payload = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: "number_visibility",
      agent_id: agentId,
      status: newStatus.toString(),
    };
    
    try {
      const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent`, payload);
      if (data.success) {
        setMobileVisible(prevState=>!prevState);
        toast.success("Mobile number visibility updated.");
      } else {
        throw new Error("Failed to update.");
      }
    } catch (error) {
      console.error(error);
      toast.error("Error updating visibility.");
    }
  };

  const handleAssignKeyword = async () => {

    if (!currentUser || !currentUser.parent_id || !currentUser.parent_token || !agentId || !keywords.trim()) return;

    const payload = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: "update_keyword",
      agent_id: agentId,
      keyword: keywords,
    };

    try {
      const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent`, payload);
      if (data.success) {
        toast.success("Keywords assigned successfully.");
        setAddedKeywords([...addedKeywords, keywords]);
        setKeywords("");
      } else {
        throw new Error("Failed to assign keywords.");
      }
    } catch (error) {
      console.error(error);
      toast.error("Error assigning keywords.");
    }

  };

  const handleRemoveKeyword = async (index, kw) => {
    if (!currentUser || !currentUser.parent_id || !currentUser.parent_token || !agentId) return;
    const payload = {
      user_id: currentUser.parent_id,
      token: currentUser.parent_token,
      method: "delete_keyword",
      agent_id: agentId,
      keyword: kw,

    }

    try {
      const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent`, payload);
      if (data.success) {
        const updatedKeywords = addedKeywords.filter((_, i) => i !== index);
        setAddedKeywords(updatedKeywords);
        toast.success("Keyword removed successfully");
      }

    } catch (error) {
      console.log(error);
      toast.error("Something went wrong, please try again later");

    }


  };

  const handleSelectSuggestion = (suggestion) => {
    setKeywords(suggestion);
    setKeywordSuggestions([]); // Hide suggestions after selection
  };



  if (loading) return <div><CircularProgress /></div>;

  return (
    <div className={styles.pageContainer}>
      <LeftMenu />
      <div className={`d-flex flex-column w-100 align-items-center ${styles.main}`}>
        {/* Agent Details Card */}
        <div className="w-100 card p-3">
          <div className="w-100">
            <strong>Agent Name:</strong> {agentName}
          </div>
          <div className="w-100">
            <strong>Agent ID:</strong> {agentId}
          </div>
        </div>

        {/* Settings Card */}
        <div className={`w-100 card p-3 ${styles.settingsCard}`}>
          <div className="w-100">
            <h5 style={{ fontWeight: "bold" }}>Settings</h5>
          </div>
          <div className="w-100 d-flex align-items-center">
            <label className="me-3">Show Mobile Number</label>
            <Switch checked={mobileVisible} onChange={handleShowMobile} color="primary" />
          </div>

          {/* Keyword Management */}
          <div className="d-flex flex-column w-100 gap-2 mt-3">
            <label>Keyword Management</label>
            <div className="d-flex flex-column flex-md-row w-100 justify-content-between align-items-center gap-3">
              <div className={styles.keywordInputContainer}>

                <input
                  type="text"
                  placeholder="Add keyword"
                  className={`form-control ${styles.keywordInput}`}
                  value={keywords}
                  onChange={(e) => setKeywords(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleAssignKeyword()}
                />


                {keywordSuggestions.length > 0 && (
                  <ul className={styles.suggestionList}>
                    {keywordSuggestions.map((suggestion, index) => (
                      <li
                        key={index}
                        className={styles.suggestionItem}
                        onClick={() => handleSelectSuggestion(suggestion)}
                      >
                        {suggestion}
                      </li>
                    ))}
                  </ul>
                )}
              </div>

              <button className={`btn btn-primary ${styles.addBtn}`} onClick={handleAssignKeyword}>
                Add Keyword
              </button>

            </div>


            <div className={styles.keywordContainer}>
              {addedKeywords.length > 0 && addedKeywords[0] !== "" ? (
                addedKeywords.map((kw, index) => (
                  <span key={index} className={styles.keywordBadge}>
                    {kw}
                    <button className={styles.removeBtn} onClick={() => handleRemoveKeyword(index, kw)}>
                      &times;
                    </button>
                  </span>
                ))
              ) : (
                <div className="text-muted w-100 p-2"
                  style={{ minHeight: "200px", backgroundColor: "whitesmoke", borderRadius: ".5rem" }}>
                  No keywords added yet.
                </div>
              )}
            </div>

          </div>
        </div>
      </div>

    </div>
  );
};

export default AgentSettings;
