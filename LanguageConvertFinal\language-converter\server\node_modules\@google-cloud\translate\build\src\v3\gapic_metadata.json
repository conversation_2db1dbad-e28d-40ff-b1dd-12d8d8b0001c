{"schema": "1.0", "comment": "This file maps proto services/RPCs to the corresponding library clients/methods", "language": "typescript", "protoPackage": "google.cloud.translation.v3", "libraryPackage": "@google-cloud/translate", "services": {"TranslationService": {"clients": {"grpc": {"libraryClient": "TranslationServiceClient", "rpcs": {"TranslateText": {"methods": ["translateText"]}, "RomanizeText": {"methods": ["romanizeText"]}, "DetectLanguage": {"methods": ["detectLanguage"]}, "GetSupportedLanguages": {"methods": ["getSupportedLanguages"]}, "TranslateDocument": {"methods": ["translateDocument"]}, "GetGlossary": {"methods": ["getGlossary"]}, "GetGlossaryEntry": {"methods": ["getGlossaryEntry"]}, "CreateGlossaryEntry": {"methods": ["createGlossaryEntry"]}, "UpdateGlossaryEntry": {"methods": ["updateGlossaryEntry"]}, "DeleteGlossaryEntry": {"methods": ["deleteGlossaryEntry"]}, "GetDataset": {"methods": ["getDataset"]}, "CreateAdaptiveMtDataset": {"methods": ["createAdaptiveMtDataset"]}, "DeleteAdaptiveMtDataset": {"methods": ["deleteAdaptiveMtDataset"]}, "GetAdaptiveMtDataset": {"methods": ["getAdaptiveMtDataset"]}, "AdaptiveMtTranslate": {"methods": ["adaptiveMtTranslate"]}, "GetAdaptiveMtFile": {"methods": ["getAdaptiveMtFile"]}, "DeleteAdaptiveMtFile": {"methods": ["deleteAdaptiveMtFile"]}, "ImportAdaptiveMtFile": {"methods": ["importAdaptiveMtFile"]}, "GetModel": {"methods": ["getModel"]}, "BatchTranslateText": {"methods": ["batchTranslateText"]}, "BatchTranslateDocument": {"methods": ["batchTranslateDocument"]}, "CreateGlossary": {"methods": ["createGlossary"]}, "UpdateGlossary": {"methods": ["updateGlossary"]}, "DeleteGlossary": {"methods": ["deleteGlossary"]}, "CreateDataset": {"methods": ["createDataset"]}, "DeleteDataset": {"methods": ["deleteDataset"]}, "ImportData": {"methods": ["importData"]}, "ExportData": {"methods": ["exportData"]}, "CreateModel": {"methods": ["createModel"]}, "DeleteModel": {"methods": ["deleteModel"]}, "ListGlossaries": {"methods": ["listGlossaries", "listGlossariesStream", "listGlossariesAsync"]}, "ListGlossaryEntries": {"methods": ["listGlossaryEntries", "listGlossaryEntriesStream", "listGlossaryEntriesAsync"]}, "ListDatasets": {"methods": ["listDatasets", "listDatasetsStream", "listDatasetsAsync"]}, "ListAdaptiveMtDatasets": {"methods": ["listAdaptiveMtDatasets", "listAdaptiveMtDatasetsStream", "listAdaptiveMtDatasetsAsync"]}, "ListAdaptiveMtFiles": {"methods": ["listAdaptiveMtFiles", "listAdaptiveMtFilesStream", "listAdaptiveMtFilesAsync"]}, "ListAdaptiveMtSentences": {"methods": ["listAdaptiveMtSentences", "listAdaptiveMtSentencesStream", "listAdaptiveMtSentencesAsync"]}, "ListExamples": {"methods": ["listExamples", "listExamplesStream", "listExamplesAsync"]}, "ListModels": {"methods": ["listModels", "listModelsStream", "listModelsAsync"]}}}, "grpc-fallback": {"libraryClient": "TranslationServiceClient", "rpcs": {"TranslateText": {"methods": ["translateText"]}, "RomanizeText": {"methods": ["romanizeText"]}, "DetectLanguage": {"methods": ["detectLanguage"]}, "GetSupportedLanguages": {"methods": ["getSupportedLanguages"]}, "TranslateDocument": {"methods": ["translateDocument"]}, "GetGlossary": {"methods": ["getGlossary"]}, "GetGlossaryEntry": {"methods": ["getGlossaryEntry"]}, "CreateGlossaryEntry": {"methods": ["createGlossaryEntry"]}, "UpdateGlossaryEntry": {"methods": ["updateGlossaryEntry"]}, "DeleteGlossaryEntry": {"methods": ["deleteGlossaryEntry"]}, "GetDataset": {"methods": ["getDataset"]}, "CreateAdaptiveMtDataset": {"methods": ["createAdaptiveMtDataset"]}, "DeleteAdaptiveMtDataset": {"methods": ["deleteAdaptiveMtDataset"]}, "GetAdaptiveMtDataset": {"methods": ["getAdaptiveMtDataset"]}, "AdaptiveMtTranslate": {"methods": ["adaptiveMtTranslate"]}, "GetAdaptiveMtFile": {"methods": ["getAdaptiveMtFile"]}, "DeleteAdaptiveMtFile": {"methods": ["deleteAdaptiveMtFile"]}, "ImportAdaptiveMtFile": {"methods": ["importAdaptiveMtFile"]}, "GetModel": {"methods": ["getModel"]}, "BatchTranslateText": {"methods": ["batchTranslateText"]}, "BatchTranslateDocument": {"methods": ["batchTranslateDocument"]}, "CreateGlossary": {"methods": ["createGlossary"]}, "UpdateGlossary": {"methods": ["updateGlossary"]}, "DeleteGlossary": {"methods": ["deleteGlossary"]}, "CreateDataset": {"methods": ["createDataset"]}, "DeleteDataset": {"methods": ["deleteDataset"]}, "ImportData": {"methods": ["importData"]}, "ExportData": {"methods": ["exportData"]}, "CreateModel": {"methods": ["createModel"]}, "DeleteModel": {"methods": ["deleteModel"]}, "ListGlossaries": {"methods": ["listGlossaries", "listGlossariesStream", "listGlossariesAsync"]}, "ListGlossaryEntries": {"methods": ["listGlossaryEntries", "listGlossaryEntriesStream", "listGlossaryEntriesAsync"]}, "ListDatasets": {"methods": ["listDatasets", "listDatasetsStream", "listDatasetsAsync"]}, "ListAdaptiveMtDatasets": {"methods": ["listAdaptiveMtDatasets", "listAdaptiveMtDatasetsStream", "listAdaptiveMtDatasetsAsync"]}, "ListAdaptiveMtFiles": {"methods": ["listAdaptiveMtFiles", "listAdaptiveMtFilesStream", "listAdaptiveMtFilesAsync"]}, "ListAdaptiveMtSentences": {"methods": ["listAdaptiveMtSentences", "listAdaptiveMtSentencesStream", "listAdaptiveMtSentencesAsync"]}, "ListExamples": {"methods": ["listExamples", "listExamplesStream", "listExamplesAsync"]}, "ListModels": {"methods": ["listModels", "listModelsStream", "listModelsAsync"]}}}}}}}