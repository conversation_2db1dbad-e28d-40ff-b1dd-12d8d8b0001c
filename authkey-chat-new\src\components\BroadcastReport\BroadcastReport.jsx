
import { useContext, useEffect, useState } from "react";
import { AuthContext } from "../../context/AuthContext";
import axios from "axios";
import { BASE_URL3 } from "../../api/api";
import styles from './BroadcastReport.module.css'
import { CircularProgress } from "@mui/material";
import { IoLogoWhatsapp } from "react-icons/io";
import { FaArrowUp } from "react-icons/fa6";
import { RiCloseFill } from "react-icons/ri";
import { AiOutlineDeliveredProcedure } from "react-icons/ai";
import { useOutletContext } from "react-router-dom";
import { formatLocalDate } from "../../utils/Utils";
const BroadcastReport = () => {
    const { fromDate, toDate, setFetchDataFn } = useOutletContext();
    const [reportData, setReportData] = useState([]);
    const [loading, setLoading] = useState(false);

    const { currentUser } = useContext(AuthContext);
    useEffect(() => {
        if (currentUser.user_id && currentUser.parent_token && currentUser.parent_id
            && fromDate && toDate
        ) {

            fetchReportData();
        }


    }, [currentUser])
    useEffect(() => {
        if (setFetchDataFn) setFetchDataFn(() => fetchReportData);
    }, [setFetchDataFn, fromDate, toDate]);


    const fetchReportData = async () => {
        const payload = {
            user_id: currentUser.parent_id,
            method: "retrieve_new_broadcast",
            token: currentUser.parent_token,
            channel: "whatsapp",
            from_date: formatLocalDate(fromDate),
            to_date: formatLocalDate(toDate),
            created_by_id: currentUser.user_id
        }

        setLoading(true);

        try {
            const { data } = await axios.post(`${BASE_URL3}/bulk_campaign_sms.php`, payload);
            if (data.success) {
                setReportData(data.data)

            } else {
                setReportData([]);
            }

        } catch (error) {
            console.log(error);

        }
        finally { setLoading(false) };
    }
    return (

        <div className={`d-flex flex-column justify-content-start 
            align-items-center ${styles.reportContainer}`}
        >
            {loading ?
                <div>
                    <CircularProgress />
                </div> :
                <div className={`
                         w-100 ${styles.scrollContainer} `}
                >
                    {reportData.length === 0 ?
                        <div className="text-center fw-bold">
                            No data present for selected date range
                        </div>
                        : reportData.map((item) =>
                            <div key={item.id} className={`w-100 p-3 d-flex flex-column 
                        justify-content-center align-items-center ${styles.reportCard}`}>
                                <header className="d-flex mb-2 mb-md-0 justify-content-between align-items-center w-100">
                                    <div>
                                        <IoLogoWhatsapp className="text-success me-2" size={20} />
                                        <span className="fw-bold">
                                            Broadcast Title:{" "}
                                        </span>
                                        {item.campaign_name}
                                    </div>
                                    <div className="p-1">
                                        {item.status === 1 ? (
                                            <span className="badge bg-info">
                                                Processing
                                            </span>
                                        ) : item.status === 5 ? (
                                            <span className="badge bg-info">
                                                Schedule
                                            </span>
                                        ) : item.status === 4 ? (
                                            <span className="badge bg-success">
                                                Completed
                                            </span>
                                        ) : item.status === 6 ? (
                                            <span className="badge bg-primary ">
                                                Schedule Deleted
                                            </span>
                                        ) : null}

                                    </div>
                                    <div>
                                        <span className="fw-bold">
                                            Broadcast Date:{" "}
                                        </span> {item.camp_start_datetime.split(" ")[0]}
                                    </div>


                                </header>
                                <main className="w-100 mb-2 mb-md-0">
                                    <div style={{ overflowX: "auto", width: "100%" }}>
                                        <table className="table w-100" style={{ minWidth: "600px" }}>
                                            <thead>
                                                <tr>
                                                    <th>Total</th>
                                                    <th>Sent</th>
                                                    <th>Read</th>
                                                    <th>Delivered</th>
                                                    <th>Failed</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>{item.contact_count}</td>
                                                    <td>
                                                        {item.sent}
                                                        <FaArrowUp className="mb-1 text-primary" />

                                                    </td>
                                                    <td>{item.read}
                                                        <FaArrowUp className="mb-1 text-primary" />
                                                    </td>
                                                    <td>{item.delivered}
                                                        <AiOutlineDeliveredProcedure size={20} className="mb-1 ml-2 text-primary" />

                                                    </td>
                                                    <td>{item.failed}
                                                        <RiCloseFill size={20} className="mb-1 text-danger" />
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                </main>
                                <footer className="d-flex justify-content-between 
                            align-items-center w-100"
                                    style={{ fontSize: ".8rem" }}
                                >
                                    <div>
                                        <span className="fw-bold" >
                                            Broadcast ID: {" "}
                                        </span>  {item.id}/ {" "}
                                        <span className="fw-bold">
                                            Template ID:{" "}
                                        </span>
                                        {item.template_id}
                                    </div>
                                    <div>
                                        <span className="fw-bold">
                                            Cost:
                                        </span>  {item.campaign_cost}
                                    </div>

                                </footer>

                            </div>

                        )}

                </div>}

        </div>

    )
}

export default BroadcastReport;