{"name": "@google-cloud/common", "description": "Common components for Cloud APIs Node.js Client Libraries", "version": "6.0.0", "license": "Apache-2.0", "author": "Google Inc.", "engines": {"node": ">=18"}, "repository": "googleapis/nodejs-common", "main": "./build/src/index.js", "types": "./build/src/index.d.ts", "files": ["build/src", "!build/src/**/*.map"], "scripts": {"docs": "jsdoc -c .jsdoc.js", "test": "c8 mocha build/test", "prepare": "npm run compile", "pretest": "npm run compile", "compile": "tsc -p .", "fix": "gts fix", "lint": "gts check", "presystem-test": "npm run compile", "system-test": "mocha build/system-test", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "prelint": "cd samples; npm link ../; npm install", "clean": "gts clean", "precompile": "gts clean"}, "dependencies": {"@google-cloud/projectify": "^4.0.0", "@google-cloud/promisify": "^4.0.0", "arrify": "^2.0.0", "duplexify": "^4.1.3", "extend": "^3.0.2", "google-auth-library": "^10.0.0-rc.1", "html-entities": "^2.5.2", "retry-request": "^8.0.0", "teeny-request": "^10.0.0"}, "devDependencies": {"@types/ent": "^2.2.8", "@types/extend": "^3.0.4", "@types/mocha": "^10.0.10", "@types/mv": "^2.1.4", "@types/ncp": "^2.0.8", "@types/node": "^22.13.5", "@types/proxyquire": "^1.3.31", "@types/request": "^2.48.12", "@types/sinon": "^17.0.4", "@types/tmp": "^0.2.6", "c8": "^10.1.3", "codecov": "^3.8.3", "gts": "^6.0.2", "jsdoc": "^4.0.4", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^6.1.2", "mocha": "^11.1.0", "mv": "^2.1.1", "ncp": "^2.0.0", "nock": "^14.0.1", "proxyquire": "^2.1.3", "sinon": "^19.0.2", "tmp": "^0.2.3", "typescript": "^5.8.2"}}