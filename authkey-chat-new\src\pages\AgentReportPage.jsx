import React, {
  useContext,
  useEffect,
  useState,
  useCallback,
  useRef
} from "react";
import LeftMenu from "../components/LeftMenu";
import Navbar from "../components/Navbar";
import { BASE_URL2 } from "../api/api";
import { AuthContext } from "../context/AuthContext";
import { toast } from "react-toastify";
import axios from "axios";
import { BASE_URL } from "../api/api";
import "react-datepicker/dist/react-datepicker.css";
import { ChatState } from "../context/AllProviders";
import NewDashboard from "../components/NewDashboard/NewDashboard";

const AgentReportPage = () => {
  const { currentUser } = useContext(AuthContext);
  const isFetched = useRef(false);
  const { socket, page, setPage } = ChatState();
  const [report, setReport] = useState({
    live_chat: [],
    total_chat: [],
    new_chat: [],
    total_repeat: [],
    answer_chat: [],
    missed_chat: [],
  });
  const [reportSummary, setReportSummary] = useState([]);
  const [liveAgentsData, setLiveAgentsData] = useState([]);
  const [chatsLoading, setChatsLoading] = useState(false);
  const [chats, setChats] = useState([]);
  const [loading, setLoading] = useState({
    activeAgent: false,
    chart: false,
    report: false,
  });
  const [selectFilter, setSelectFilter] = useState("thisWeek");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [calendarOpen, setCalendarOpen] = useState(false);
  const [labelData, setLabelData] = useState([]);

  useEffect(() => {
    if (!socket) return;

    const updateLiveAgent = (data, online) => {
      if (data.user_type !== "agent") return;

      setLiveAgentsData((prevAgents) => {
        if (online) {
          const newAgent = {
            id: data.user_id,
            user_id: data.parent_id,
            name: data.name,
            email: data.email,
            online: 1,
            live_chat: 0,
          };
          if (!prevAgents.some((agent) => agent.id === data.user_id)) {
            return [...prevAgents, newAgent];
          }
        } else {
          return prevAgents.filter((agent) => agent.id !== data.user_id);
        }
        return prevAgents;
      });
    };

    const handleOnlineAgent = (data) => updateLiveAgent(data, true);
    const handleOfflineAgent = (data) => updateLiveAgent(data, false);

    const updateChatStatus = (data, isChatOn) => {
      if (
        !["admin", "manager", "team"].includes(currentUser.user_type) ||
        data.user_type !== "agent"
      ) {
        return;
      }

      const isRelevant = {
        admin: () => true,
        manager: () => currentUser.user_id === data.manager,
        team: () => currentUser.user_id === data.team,
      }[currentUser.user_type]();

      if (!isRelevant) return;

      setReport((prevReport) => ({
        ...prevReport,
        live_chat: isChatOn
          ? [...prevReport.live_chat, { ...data, id: data.user_id }]
          : prevReport.live_chat.filter(
            (user) => user.user_id !== data.user_id
          ),
      }));

      setLiveAgentsData((prevAgents) =>
        prevAgents.map((agent) =>
          agent.id === data.user_id
            ? { ...agent, live_chat: isChatOn ? 1 : 0 }
            : agent
        )
      );
    };

    socket.on("online agent", handleOnlineAgent);
    socket.on("offline agent", handleOfflineAgent);
    socket.on("chat on", (data) => updateChatStatus(data, true));
    socket.on("chat off", (data) => updateChatStatus(data, false));

    return () => {
      socket.off("online agent", handleOnlineAgent);
      socket.off("offline agent", handleOfflineAgent);
      socket.off("chat on", (data) => updateChatStatus(data, true));
      socket.off("chat off", (data) => updateChatStatus(data, false));

    };
  }, [currentUser, socket]);

  useEffect(() => {
    const fetchReport = async () => {
      if (!currentUser.parent_id) return;
      setLoading((prevState) => ({ ...prevState, report: true }));

      try {
        const { data } = await axios.post(`${BASE_URL2}/whatsapp_report`, {
          user_id: currentUser.parent_id,
          token: currentUser.parent_token,
          method: "today_report",
          brand_number: currentUser.brand_number,
          user_type: currentUser.user_type,
          agent_id: currentUser.user_id,
        });
        if (data.success) {
          setReport(data.data);
        }
      } catch (error) {
        if (error.response && error.response.status === 429) {
          toast.error("Too many requests. Please try again later.")

        } else {
          console.error(error);
          toast.error("Something went wrong, please try again later")
        }

      } finally {
        setLoading((prevState) => ({ ...prevState, report: false }));
      }
    };

    const fetchLiveAgents = async () => {
      if (!currentUser.parent_id) return;
      setLoading((prevState) => ({ ...prevState, activeAgent: true }));

      try {
        const { data } = await axios.post(`${BASE_URL2}/whatsapp_agent`, {
          user_id: currentUser.parent_id,
          token: currentUser.parent_token,
          method: "live_agents",
          user_type: currentUser.user_type,
          agent_id: currentUser.user_id,
        });
        if (data.success) {
          setLiveAgentsData(data.data);
        }
      } catch (error) {
        if (error.response && error.response.status === 429) {
          toast.error("Too many requests. Please try again later.")

        } else {
          console.error(error);
          toast.error("Something went wrong, please try again later")
        }
      } finally {
        setLoading((prevState) => ({ ...prevState, activeAgent: false }));
      }
    };

    fetchReport();
    if (currentUser.user_type === "admin") {
      graphReport({ type: "thisWeek" });
    }
    fetchLiveAgents();
  }, [currentUser]);

  useEffect(() => {
    fetchNewContactList();
  }, [currentUser]);

  const getWhatsaAppNumberList = async (page) => {
    if (currentUser.parent_id) {
      setChatsLoading(true);
      let datafornolist = {
        token: currentUser.parent_token,
        user_id: currentUser.parent_id,
        method: "left_menunew",
        start: page,
        brand_number: currentUser.brand_number,
        user_type: currentUser.user_type,
        search_id: currentUser.user_id ? currentUser.user_id : "",
      };
      try {
        const { data } = await axios.post(
          `${BASE_URL}/netcore_conversation.php`,
          datafornolist
        );

        if (data.success === true) {

          setChats((prevChats) => [...prevChats, ...data.data]);;

        }
        setChatsLoading(false);
      } catch (error) {
        if (error.response && error.response.status === 429) {
          toast.error("Too many requests. Please try again later.")

        } else {
          console.error(error);
          toast.error("Something went wrong, please try again later")
        }

      }
      finally {
        setChatsLoading(false);
      }
    }
  };

  useEffect(() => {
    if (!isFetched.current) {
      getWhatsaAppNumberList(0);
      isFetched.current = true; // Set flag to prevent future calls
    }
  }, []);


  const fetchNextPage = () => {
    const nextPage = page + 30;
    setPage(nextPage);
    getWhatsaAppNumberList(nextPage);
  }

  const graphReport = useCallback(
    async (filterdata) => {
      if (!currentUser.parent_id) return;
      setLoading((prevState) => ({ ...prevState, chart: true }));

      try {
        const { data } = await axios.post(`${BASE_URL2}/whatsapp_report`, {
          user_id: currentUser.parent_id,
          token: currentUser.parent_token,
          method: "retrieve_report",
          brand_number: currentUser.brand_number,
          user_type: currentUser.user_type,
          filter: filterdata.type,
          from_date: filterdata.from_date,
          to_date: filterdata.to_date,
        });
        if (data.success) {
          setReportSummary(data.data);
        }
      } catch (error) {
        console.error(error.message);
        toast.error(error.message);
      } finally {
        setLoading((prevState) => ({ ...prevState, chart: false }));
      }
    },
    [currentUser]
  );

  const handleFilterChange = useCallback(
    async (e) => {
      const selectedValue = e.target.value;
      setSelectFilter(selectedValue);
      const filter = { type: selectedValue };
      if (selectedValue === "custom_date") {
        setCalendarOpen(true);
        setEndDate(null);
        setStartDate(null);
      }
      if (selectedValue !== "custom_date") {

        await graphReport(filter);
      }

    },
    [graphReport]
  );

  const handleDateChange = useCallback(
    async (dates) => {
      const [start, end] = dates;
      setStartDate(start);
      setEndDate(end);
      if (end && selectFilter === "custom_date") {
        setCalendarOpen(false);
        await graphReport({ type: "custom_date", from_date: start, to_date: end });
      }
    },
    [graphReport, selectFilter]
  );

  const handleOpenCalendar = () => {
    if (startDate && endDate) {
      setCalendarOpen(true);
    }
  };

  const fetchNewContactList = async () => {
    if (!currentUser || !currentUser.token || !currentUser.user_id || !currentUser.user_type) return;

    setLoading(true);
    const payload = {
      token: currentUser.token,
      user_id: currentUser.user_id,
      method: "left_menu",
      brand_number: currentUser.brand_number,
      agent_id: 0,
      type: "whatsapp",
      from_date: new Date().toISOString().split("T")[0],
      to_date: new Date().toISOString().split("T")[0],
      page: 1,
      user_type: currentUser.user_type
    };
    try {
      const { data } = await axios.post(
        `${BASE_URL2}/whatsapp_conv`,
        payload,

      );

      if (data.success === true) {

        setLabelData(data.data)
      }
    } catch (error) {
      console.error("Error fetching contact list:", error);
    } finally {

      setLoading(false);
    }

  }

  return (
    <>
      <div className="layout-wrapper d-lg-flex">
        <LeftMenu />
        <div className="w-100" style={{ height: "100vh", overflowY: "hidden" }}>
          {/* <Navbar /> */}
          <NewDashboard labelData={labelData} loading={loading} currentUser={currentUser} fetchNextPage={fetchNextPage} brand_number={currentUser.brand_number} todayReport={report} agentList={liveAgentsData} handleFilterChange={handleFilterChange} selectFilter={selectFilter} handleDateChange={handleDateChange} startDate={startDate} endDate={endDate}
            calendarOpen={calendarOpen} chats={chats} handleOpenCalendar={handleOpenCalendar} reportSummary={reportSummary} setCalendarOpen={setCalendarOpen} />
        </div>
      </div>
    </>
    // <NewDashboard labelData={labelData} loading={loading} currentUser={currentUser} fetchNextPage={fetchNextPage} brand_number={currentUser.brand_number} todayReport={report} agentList={liveAgentsData} handleFilterChange={handleFilterChange} selectFilter={selectFilter} handleDateChange={handleDateChange} startDate={startDate} endDate={endDate}
    //   calendarOpen={calendarOpen} chats={chats} handleOpenCalendar={handleOpenCalendar} reportSummary={reportSummary} setCalendarOpen={setCalendarOpen} />
  );
};

export default AgentReportPage;
