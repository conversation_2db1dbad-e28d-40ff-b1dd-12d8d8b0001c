{"ast": null, "code": "// Translation service configuration\nexport const TRANSLATION_CONFIG = {\n  // Base URL for the translation service\n  BASE_URL: 'http://localhost:5000',\n  // API endpoints\n  ENDPOINTS: {\n    LANGUAGES: '/api/languages',\n    TRANSLATE: '/api/translate',\n    HEALTH: '/api/health'\n  },\n  // Translation settings\n  SETTINGS: {\n    DEBOUNCE_DELAY: 1500,\n    // milliseconds\n    AUTO_TRANSLATE: true,\n    INSTANT_TRANSLATE_ON_SPACE: true\n  }\n}; // Helper function to get full URL\n\nexport const getTranslationUrl = endpoint => {\n  return `${TRANSLATION_CONFIG.BASE_URL}${endpoint}`;\n};", "map": {"version": 3, "sources": ["D:/DataGen/authkey-chat-new/src/config/translation.js"], "names": ["TRANSLATION_CONFIG", "BASE_URL", "ENDPOINTS", "LANGUAGES", "TRANSLATE", "HEALTH", "SETTINGS", "DEBOUNCE_DELAY", "AUTO_TRANSLATE", "INSTANT_TRANSLATE_ON_SPACE", "getTranslationUrl", "endpoint"], "mappings": "AAAA;AACA,OAAO,MAAMA,kBAAkB,GAAG;AAChC;AACAC,EAAAA,QAAQ,EAAE,uBAFsB;AAIhC;AACAC,EAAAA,SAAS,EAAE;AACTC,IAAAA,SAAS,EAAE,gBADF;AAETC,IAAAA,SAAS,EAAE,gBAFF;AAGTC,IAAAA,MAAM,EAAE;AAHC,GALqB;AAWhC;AACAC,EAAAA,QAAQ,EAAE;AACRC,IAAAA,cAAc,EAAE,IADR;AACc;AACtBC,IAAAA,cAAc,EAAE,IAFR;AAGRC,IAAAA,0BAA0B,EAAE;AAHpB;AAZsB,CAA3B,C,CAmBP;;AACA,OAAO,MAAMC,iBAAiB,GAAIC,QAAD,IAAc;AAC7C,SAAQ,GAAEX,kBAAkB,CAACC,QAAS,GAAEU,QAAS,EAAjD;AACD,CAFM", "sourcesContent": ["// Translation service configuration\nexport const TRANSLATION_CONFIG = {\n  // Base URL for the translation service\n  BASE_URL: 'http://localhost:5000',\n  \n  // API endpoints\n  ENDPOINTS: {\n    LANGUAGES: '/api/languages',\n    TRANSLATE: '/api/translate',\n    HEALTH: '/api/health'\n  },\n  \n  // Translation settings\n  SETTINGS: {\n    DEBOUNCE_DELAY: 1500, // milliseconds\n    AUTO_TRANSLATE: true,\n    INSTANT_TRANSLATE_ON_SPACE: true\n  }\n};\n\n// Helper function to get full URL\nexport const getTranslationUrl = (endpoint) => {\n  return `${TRANSLATION_CONFIG.BASE_URL}${endpoint}`;\n};\n"]}, "metadata": {}, "sourceType": "module"}