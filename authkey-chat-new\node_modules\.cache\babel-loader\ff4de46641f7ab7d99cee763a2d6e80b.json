{"ast": null, "code": "// authkey-chat-new/src/config/translation.js\nexport const TRANSLATION_CONFIG = {\n  // Use their existing API endpoint\n  BASE_URL: 'https://napi.authkey.io/api',\n  // Single endpoint for both operations\n  ENDPOINTS: {\n    GOOGLE: '/google'\n  },\n  // Translation settings\n  SETTINGS: {\n    DEBOUNCE_DELAY: 1500,\n    AUTO_TRANSLATE: true,\n    INSTANT_TRANSLATE_ON_SPACE: true\n  }\n}; // Helper function to get full URL\n\nexport const getTranslationUrl = endpoint => {\n  return `${TRANSLATION_CONFIG.BASE_URL}${endpoint}`;\n};", "map": {"version": 3, "sources": ["D:/DataGen/authkey-chat-new/src/config/translation.js"], "names": ["TRANSLATION_CONFIG", "BASE_URL", "ENDPOINTS", "GOOGLE", "SETTINGS", "DEBOUNCE_DELAY", "AUTO_TRANSLATE", "INSTANT_TRANSLATE_ON_SPACE", "getTranslationUrl", "endpoint"], "mappings": "AAAA;AACA,OAAO,MAAMA,kBAAkB,GAAG;AAChC;AACAC,EAAAA,QAAQ,EAAE,6BAFsB;AAIhC;AACAC,EAAAA,SAAS,EAAE;AACTC,IAAAA,MAAM,EAAE;AADC,GALqB;AAShC;AACAC,EAAAA,QAAQ,EAAE;AACRC,IAAAA,cAAc,EAAE,IADR;AAERC,IAAAA,cAAc,EAAE,IAFR;AAGRC,IAAAA,0BAA0B,EAAE;AAHpB;AAVsB,CAA3B,C,CAiBP;;AACA,OAAO,MAAMC,iBAAiB,GAAIC,QAAD,IAAc;AAC7C,SAAQ,GAAET,kBAAkB,CAACC,QAAS,GAAEQ,QAAS,EAAjD;AACD,CAFM", "sourcesContent": ["// authkey-chat-new/src/config/translation.js\nexport const TRANSLATION_CONFIG = {\n  // Use their existing API endpoint\n  BASE_URL: 'https://napi.authkey.io/api',\n  \n  // Single endpoint for both operations\n  ENDPOINTS: {\n    GOOGLE: '/google'\n  },\n  \n  // Translation settings\n  SETTINGS: {\n    DEBOUNCE_DELAY: 1500,\n    AUTO_TRANSLATE: true,\n    INSTANT_TRANSLATE_ON_SPACE: true\n  }\n};\n\n// Helper function to get full URL\nexport const getTranslationUrl = (endpoint) => {\n  return `${TRANSLATION_CONFIG.BASE_URL}${endpoint}`;\n};"]}, "metadata": {}, "sourceType": "module"}