import { Service, Metadata } from '@google-cloud/common';
import { GoogleAuthOptions } from 'google-auth-library';
import { DecorateRequestOptions, BodyResponseCallback } from '@google-cloud/common/build/src/util';
export interface TranslateRequest {
    format?: string;
    from?: string;
    model?: string;
    to?: string;
}
export interface TranslateCallback<T extends string | string[]> {
    (err: Error | null, translations?: T | null, apiResponse?: Metadata): void;
}
export interface DetectResult {
    language: string;
    confidence: number;
    input: string;
}
export interface DetectCallback<T extends DetectResult | DetectResult[]> {
    (err: Error | null, results?: T | null, apiResponse?: Metadata): void;
}
export interface LanguageResult {
    code: string;
    name: string;
}
export interface GetLanguagesCallback {
    (err: Error | null, results?: LanguageResult[] | null, apiResponse?: Metadata): void;
}
export interface TranslateConfig extends GoogleAuthOptions {
    key?: string;
    autoRetry?: boolean;
    maxRetries?: number;
    /**
     * The API endpoint of the service used to make requests.
     * Defaults to `translation.googleapis.com`.
     */
    apiEndpoint?: string;
}
/**
 * @typedef {object} ClientConfig
 * @memberof v2
 * @property {string} [projectId] The project ID from the Google Developer's
 *     Console, e.g. 'grape-spaceship-123'. We will also check the environment
 *     variable `GCLOUD_PROJECT` for your project ID. If your app is running in
 *     an environment which supports {@link
 * https://cloud.google.com/docs/authentication/production#providing_credentials_to_your_application
 * Application Default Credentials}, your project ID will be detected
 * automatically.
 * @property {string} [key] An API key. You should prefer using a Service
 *     Account key file instead of an API key.
 * @property {string} [keyFilename] Full path to the a .json, .pem, or .p12 key
 *     downloaded from the Google Developers Console. If you provide a path to a
 *     JSON file, the `projectId` option above is not necessary. NOTE: .pem and
 *     .p12 require you to specify the `email` option as well.
 * @property {string} [email] Account email address. Required when using a .pem
 *     or .p12 keyFilename.
 * @property {object} [credentials] Credentials object.
 * @property {string} [credentials.client_email]
 * @property {string} [credentials.private_key]
 * @property {boolean} [autoRetry=true] Automatically retry requests if the
 *     response is related to rate limits or certain intermittent server errors.
 *     We will exponentially backoff subsequent requests by default.
 * @property {number} [maxRetries=3] Maximum number of automatic retries
 *     attempted before returning the error.
 * @property {Constructor} [promise] Custom promise module to use instead of
 *     native Promises.
 */
/**
 * With {@link https://cloud.google.com/translate| Google Translate}, you can
 * dynamically translate text between thousands of language pairs.
 *
 * The Google Cloud Translation API lets websites and programs integrate with
 * Google Translate programmatically.
 *
 * @class
 * @memberof v2
 *
 * @see [Getting Started]{@link https://cloud.google.com/translate/v2/getting_started}
 * @see [Identifying your application to Google]{@link https://cloud.google.com/translate/v2/using_rest#auth}
 *
 * @param {ClientConfig} [options] Configuration options.
 *
 * @example
 * ```
 * //-
 * // <h3>Custom Translation API</h3>
 * //
 * // The environment variable, `GOOGLE_CLOUD_TRANSLATE_ENDPOINT`, is honored as
 * // a custom backend which our library will send requests to.
 * //-
 *
 * ```
 * @example <caption>include:samples/quickstart.js</caption>
 * region_tag:translate_quickstart
 * Full quickstart example:
 */
export declare class Translate extends Service {
    options: TranslateConfig;
    key?: string;
    constructor(options?: TranslateConfig);
    detect(input: string): Promise<[DetectResult, Metadata]>;
    detect(input: string[]): Promise<[DetectResult[], Metadata]>;
    detect(input: string, callback: DetectCallback<DetectResult>): void;
    detect(input: string[], callback: DetectCallback<DetectResult[]>): void;
    getLanguages(target?: string): Promise<[LanguageResult[], Metadata]>;
    getLanguages(target: string, callback: GetLanguagesCallback): void;
    getLanguages(callback: GetLanguagesCallback): void;
    translate(input: string, options: TranslateRequest): Promise<[string, Metadata]>;
    translate(input: string[], options: TranslateRequest): Promise<[string[], Metadata]>;
    translate(input: string, to: string): Promise<[string, Metadata]>;
    translate(input: string[], to: string): Promise<[string[], Metadata]>;
    translate(input: string, options: TranslateRequest, callback: TranslateCallback<string>): void;
    translate(input: string, to: string, callback: TranslateCallback<string>): void;
    translate(input: string[], options: TranslateRequest, callback: TranslateCallback<string[]>): void;
    translate(input: string[], to: string, callback: TranslateCallback<string[]>): void;
    /**
     * A custom request implementation. Requests to this API may optionally use an
     * API key for an application, not a bearer token from a service account. This
     * means it is possible to skip the `makeAuthenticatedRequest` portion of the
     * typical request lifecycle, and manually authenticate the request here.
     *
     * @private
     *
     * @param {object} reqOpts - Request options that are passed to `request`.
     * @param {function} callback - The callback function passed to `request`.
     */
    request(reqOpts: DecorateRequestOptions, callback: BodyResponseCallback): void;
}
