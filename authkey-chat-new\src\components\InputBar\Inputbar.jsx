import React from "react";
import styles from "./inputBar.module.css"; // Import the CSS module
import { useContext, useState, useEffect, useRef, useCallback } from "react";
import { ChatContext } from "../../context/ChatContext";
import Input from "../Input";
import useSentences from "../../customHooks/useSentences";
import { ChatState } from "../../context/AllProviders";
import { FaBold } from "react-icons/fa";
import { FaItalic } from "react-icons/fa";
import { FaStrikethrough } from "react-icons/fa";
import { FaLanguage } from "react-icons/fa";
import ReplyPreview from "../ReplyPreview/ReplyPreview";
import BlockCard from "../BlockCard";
import { AuthContext } from "../../context/AuthContext";
import axios from "axios";
import { BASE_URL2 } from "../../api/api";
import { toast } from "react-toastify";
import { TRANSLATION_CONFIG, getTranslationUrl } from "../../config/translation";
const Inputbar = ({
  setFaqOpen,
  notes,
  setshowNotesCard,
  setShowContactDetail,
  activeTab,
  setActiveTab,
  setshowQuickReply,

}) => {
  const { data } = useContext(ChatContext);
  const { text, setText, reply, setReply, selectedMobileNumber, selectedUserDetails } = ChatState();
  const textareaRef = useRef(null);
  const [showPreview, setShowPreview] = useState(false);
  const { currentUser } = useContext(AuthContext);
  const lastTypingTimeRef = useRef(0);

  // Translation states
  const [selectedLanguage, setSelectedLanguage] = useState('');
  const [isTranslationEnabled, setIsTranslationEnabled] = useState(false);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [languages, setLanguages] = useState([]);
  const [loadingLanguages, setLoadingLanguages] = useState(false);
  const [translationError, setTranslationError] = useState('');

  // Translation refs
  const debounceTimeoutRef = useRef(null);
  const lastTranslatedWordRef = useRef('');
  const isTranslatingRef = useRef(false);
  const DEBOUNCE_DELAY = TRANSLATION_CONFIG.SETTINGS.DEBOUNCE_DELAY;


  const { filteredSentences, saveSentences, splitParagraphIntoSentences } =
    useSentences();

  const handleSuggestionClick = (suggestedText) => {
    setText((prevState) => {
      // Split the current input into words
      const words = prevState.split(/\s+/);

      // Check if the last word matches the suggestion
      const lastWord = words[words.length - 1];
      const suggestionFirstWord = suggestedText.split(/\s+/)[0];

      if (suggestionFirstWord.toLowerCase().includes(lastWord.toLowerCase())) {
        // If the last word matches the suggestion, replace it
        words[words.length - 1] = suggestedText;
      } else {
        // Otherwise, append the suggestion
        words.push(suggestedText);
      }
      // Join the words back into a single string and return
      return words.join(" ");
    });
    textareaRef.current?.focus();
  };
  const handleTabClick = (tabName) => {
    setActiveTab(tabName);

    if (tabName === "Note") {
      setshowNotesCard((prevState) => {
        const newState = !prevState; // Calculate the new state
        if (!newState) {
          // If the new state is `false`, reset the active tab to ""
          setActiveTab("");
        }
        return newState;
      });
      setShowContactDetail(false);
      setFaqOpen(false);
      setshowQuickReply(false);
    } else if (tabName === "quickReplies") {
      setshowQuickReply((prevState) => {
        const newState = !prevState; // Calculate the new state
        if (!newState) {
          // If the new state is `false`, reset the active tab to ""
          setActiveTab("");
        }
        return newState;
      });
      setShowContactDetail(false);
      setFaqOpen(false);
      setshowNotesCard(false);
    }
  };


  const showTypingStatus = async () => {
    const now = Date.now();
    if (now - lastTypingTimeRef.current < 25000) {
      return;
    }

    lastTypingTimeRef.current = now;
    const payload = {
      user_id: currentUser.user_id,
      token: currentUser.token,
      method: "typing",
      user_type: currentUser.user_type,
      mobile: selectedMobileNumber,
      brand_number: currentUser.brand_number,

    }
    try {

      const { data } = await axios.post(`${BASE_URL2}/conversation`, payload);

    } catch (error) {
      if (error.response && error.response.status === 429) {
        toast.error("Too many requests. Please try again later.")

      } else {
        console.error(error);
        toast.error("Something went wrong, please try again later")
      }
    }
  }

  useEffect(() => {
    if (!text || !currentUser.user_id
      || !currentUser.user_type || !currentUser.brand_number
      || !currentUser.token) return;

    showTypingStatus();
  }, [text, currentUser]);


  const applyFormat = (wrapSymbol) => {
    const input = textareaRef.current;
    if (!input) return;

    const startPos = input.selectionStart;
    const endPos = input.selectionEnd;

    const beforeSelection = text.substring(0, startPos);
    const selectedText = text.substring(startPos, endPos);
    const afterSelection = text.substring(endPos);

    if (selectedText === "") {
      const newText = beforeSelection + wrapSymbol + wrapSymbol + afterSelection;
      setText(newText);
      setShowPreview(true);

      // Move cursor between added symbols
      setTimeout(() => {
        input.focus();
        input.setSelectionRange(startPos + wrapSymbol.length, startPos + wrapSymbol.length);
      }, 10);
    } else {
      const newText = beforeSelection + wrapSymbol + selectedText + wrapSymbol + afterSelection;
      setText(newText);
      setShowPreview(true);

      setTimeout(() => input.focus(), 10);
    }
  };

  // Translation functions
  const fetchLanguages = useCallback(async () => {
    if (languages.length > 0) return; // Already loaded
    if (!currentUser?.parent_id || !currentUser?.parent_token) return; // No auth data

    setLoadingLanguages(true);
    try {
      const payload = {
        user_id: currentUser.parent_id,
        token: currentUser.parent_token,
        method: "language_list"
      };

      const response = await axios.post(getTranslationUrl(TRANSLATION_CONFIG.ENDPOINTS.GOOGLE), payload);

      if (response.data.success) {
        const languagesData = response.data.data;

        // Convert object to array and sort
        const languageArray = Object.entries(languagesData).map(([code, name]) => ({
          code,
          name: name,
        }));

        // Sort languages alphabetically by name
        languageArray.sort((a, b) => a.name.localeCompare(b.name));

        setLanguages(languageArray);
      } else {
        throw new Error(response.data.message || 'Failed to fetch languages');
      }
    } catch (error) {
      console.error('Failed to fetch languages:', error);
      setTranslationError('Failed to load languages');
      setTimeout(() => setTranslationError(''), 3000);
    } finally {
      setLoadingLanguages(false);
    }
  }, [languages.length, currentUser]);

  const translateWordSilently = useCallback(async (word, wordStart, wordEnd, currentCursorPos) => {
    if (!word.trim() || !selectedLanguage || isTranslatingRef.current || !isTranslationEnabled) {
      return;
    }

    if (!currentUser?.parent_id || !currentUser?.parent_token) {
      return;
    }

    if (word === lastTranslatedWordRef.current) {
      return;
    }

    const textarea = textareaRef.current;
    if (!textarea) return;

    // Prevent multiple simultaneous translations
    isTranslatingRef.current = true;

    try {
      // Store current selection to preserve it
      const selectionStart = textarea.selectionStart;
      const selectionEnd = textarea.selectionEnd;

      // Get current text
      const currentText = textarea.value;

      // Translate the word
      const payload = {
        user_id: currentUser.parent_id,
        token: currentUser.parent_token,
        method: "translate",
        target_lang: selectedLanguage,
        text: word
      };

      const response = await axios.post(
        getTranslationUrl(TRANSLATION_CONFIG.ENDPOINTS.GOOGLE),
        payload
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Translation failed');
      }

      const translatedWord = response.data.translated_text;

      // Create new text with translated word
      const newText =
        currentText.substring(0, wordStart) +
        translatedWord +
        currentText.substring(wordEnd);

      // Update textarea value and React state
      setText(newText);

      // Calculate new cursor position
      const lengthDifference = translatedWord.length - word.length;
      let newCursorPos;

      if (currentCursorPos <= wordEnd) {
        // Cursor was before or at the end of the translated word
        if (currentCursorPos <= wordStart) {
          newCursorPos = currentCursorPos; // Before the word, no change
        } else {
          newCursorPos = wordEnd + lengthDifference; // At the end of translated word
        }
      } else {
        // Cursor was after the word
        newCursorPos = currentCursorPos + lengthDifference;
      }

      // Restore cursor position after React update
      setTimeout(() => {
        if (textarea) {
          textarea.setSelectionRange(newCursorPos, newCursorPos);
          textarea.focus();
        }
      }, 10);

      lastTranslatedWordRef.current = translatedWord;

    } catch (err) {
      console.error('Translation error:', err);
      setTranslationError('Translation failed. Please try again.');
      setTimeout(() => setTranslationError(''), 3000);
    } finally {
      isTranslatingRef.current = false;
    }
  }, [selectedLanguage, isTranslationEnabled, currentUser]);

  // Find completed word when space is pressed
  const findCompletedWord = (text, cursorPos) => {
    // Look backwards from cursor position to find the completed word
    if (cursorPos > 0 && text[cursorPos - 1] === ' ') {
      let wordEnd = cursorPos - 1; // Position of space
      let wordStart = wordEnd;

      // Find start of word (go backwards until space or beginning)
      while (wordStart > 0 && text[wordStart - 1] !== ' ') {
        wordStart--;
      }

      const word = text.substring(wordStart, wordEnd);
      return {
        word: word.trim(),
        start: wordStart,
        end: wordEnd
      };
    }
    return null;
  };

  // Find current word being typed (for auto-translation)
  const findCurrentWord = (text, cursorPos) => {
    let start = cursorPos;
    let end = cursorPos;

    // Find word boundaries
    while (start > 0 && text[start - 1] !== ' ') {
      start--;
    }
    while (end < text.length && text[end] !== ' ') {
      end++;
    }

    const word = text.substring(start, end);
    return {
      word: word.trim(),
      start: start,
      end: end
    };
  };

  const handleLanguageSelect = (languageCode) => {
    setSelectedLanguage(languageCode);
    setIsTranslationEnabled(!!languageCode);
    setShowLanguageDropdown(false);

    // Reset translation references
    lastTranslatedWordRef.current = '';

    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
  };

  const toggleLanguageDropdown = () => {
    if (!showLanguageDropdown) {
      fetchLanguages();
    }
    setShowLanguageDropdown(!showLanguageDropdown);
  };

  // Handle space key press for instant translation
  const handleTranslationKeyDown = useCallback((e) => {
    if (e.key === ' ' && !isTranslatingRef.current && isTranslationEnabled) {
      const textarea = e.target;

      // Clear any pending auto-translation
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Use requestAnimationFrame to ensure space is processed first
      requestAnimationFrame(() => {
        const text = textarea.value;
        const cursorPos = textarea.selectionStart;

        const completedWord = findCompletedWord(text, cursorPos);
        if (completedWord && completedWord.word) {
          translateWordSilently(
            completedWord.word,
            completedWord.start,
            completedWord.end,
            cursorPos
          );
        }
      });
    }
  }, [translateWordSilently, isTranslationEnabled, findCompletedWord]);

  // Handle input for auto-translation (debounced)
  const handleTranslationInputChange = useCallback((currentText) => {
    if (!isTranslationEnabled || !textareaRef.current) return;

    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set timeout for auto-translation
    if (!isTranslatingRef.current) {
      debounceTimeoutRef.current = setTimeout(() => {
        const textarea = textareaRef.current;
        if (!textarea) return;

        const text = textarea.value;
        const cursorPos = textarea.selectionStart;

        if (text.trim()) {
          const currentWord = findCurrentWord(text, cursorPos);
          if (currentWord.word && currentWord.word !== lastTranslatedWordRef.current) {
            translateWordSilently(
              currentWord.word,
              currentWord.start,
              currentWord.end,
              cursorPos
            );
          }
        }
      }, DEBOUNCE_DELAY);
    }
  }, [isTranslationEnabled, translateWordSilently, findCurrentWord]);

  // Effect to handle language change
  useEffect(() => {
    if (!isTranslationEnabled || !textareaRef.current || !currentUser?.parent_id || !currentUser?.parent_token) return;

    const textarea = textareaRef.current;
    if (textarea && textarea.value.trim() && !isTranslatingRef.current) {
      const text = textarea.value;
      const cursorPos = textarea.selectionStart;
      const currentWord = findCurrentWord(text, cursorPos);

      if (currentWord.word) {
        translateWordSilently(
          currentWord.word,
          currentWord.start,
          currentWord.end,
          cursorPos
        );
      }
    }

    // Reset references
    lastTranslatedWordRef.current = '';
  }, [selectedLanguage, isTranslationEnabled, translateWordSilently, findCurrentWord, currentUser]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className={styles.inputContainer}>
      {selectedUserDetails.isBlock === 1 ? <BlockCard selectedMobileNumber={selectedMobileNumber} selectedUserDetails={selectedUserDetails} /> :
        <>
          {reply && <ReplyPreview reply={reply} onClose={() => setReply(null)} />}
          <header className="d-flex justify-content-start align-items-center w-100">
            <button
              onClick={() => handleTabClick("Note")}
              className={`btn ${styles.button} ms-2 ${activeTab === "Note" ? styles.activeButton : ""
                }`}
            >
              Notes {notes.length > 0 ? `(${notes.length})` : ""}
            </button>
            <button
              onClick={() => handleTabClick("quickReplies")}
              className={`btn ${styles.button} ms-2 ${activeTab === "quickReplies" ? styles.activeButton : ""
                }`}
            >
              Quick Replies
            </button>
            <div
              className="mx-1 px-1"
              role="button"
              onClick={() => applyFormat("*")}
            >
              <FaBold />
            </div>
            <div
              className="mx-1 px-1"
              role="button"
              onClick={() => applyFormat("_")}
            >
              <FaItalic />
            </div>
            <div
              className="mx-1 px-1"
              role="button"
              onClick={() => applyFormat("~")}
            >
              <FaStrikethrough />
            </div>

            {/* Language Translation Dropdown */}
            <div className="dropdown mx-1">
              <button
                className={`btn ${isTranslationEnabled ? 'btn-primary' : 'btn-outline-secondary'} dropdown-toggle d-flex align-items-center`}
                type="button"
                onClick={toggleLanguageDropdown}
                style={{ fontSize: '0.875rem', padding: '0.25rem 0.5rem' }}
              >
                <FaLanguage className="me-1" />
                {selectedLanguage ?
                  languages.find(lang => lang.code === selectedLanguage)?.name || 'Language'
                  : 'Language'
                }
              </button>

              {showLanguageDropdown && (
                <div className="dropdown-menu show" style={{ maxHeight: '300px', overflowY: 'auto' }}>
                  <div className="dropdown-header">
                    <small className="text-muted">Select language for translation</small>
                  </div>
                  <button
                    className={`dropdown-item ${!selectedLanguage ? 'active' : ''}`}
                    onClick={() => handleLanguageSelect('')}
                  >
                    Disable Translation
                  </button>
                  <div className="dropdown-divider"></div>

                  {loadingLanguages ? (
                    <div className="dropdown-item-text text-center">
                      <small>Loading languages...</small>
                    </div>
                  ) : (
                    languages.map((language) => (
                      <button
                        key={language.code}
                        className={`dropdown-item ${selectedLanguage === language.code ? 'active' : ''}`}
                        onClick={() => handleLanguageSelect(language.code)}
                      >
                        {language.name}
                      </button>
                    ))
                  )}
                </div>
              )}

              {/* Overlay to close dropdown */}
              {showLanguageDropdown && (
                <div
                  className="position-fixed top-0 start-0 w-100 h-100"
                  style={{ zIndex: 1040 }}
                  onClick={() => setShowLanguageDropdown(false)}
                />
              )}
            </div>
          </header>



          {/* Suggestion Section */}
          {filteredSentences.length ? (
            <div className={styles.suggestionsContainer}>
              <ul className={styles.suggestionsList}>
                {filteredSentences.map((sentence, index) => (
                  <li
                    key={index}
                    className={styles.suggestionPill}
                    onClick={() => handleSuggestionClick(sentence)}
                  >
                    {sentence}
                  </li>
                ))}
              </ul>
              <div
                className="d-none d-md-flex justify-content-center align-items-end w-100"
                style={{ fontSize: ".7rem", color: "grey" }}
              >
                <div>Press Tab to add text</div>
              </div>
            </div>
          ) : null}

          <div className={styles.textArea}>
            <Input
              showPreview={showPreview} setShowPreview={setShowPreview}
              textareaRef={textareaRef}
              handleSuggestionClick={handleSuggestionClick}
              saveSentences={saveSentences}
              splitParagraphIntoSentences={splitParagraphIntoSentences}
              filteredSentences={filteredSentences}
              selectedMobile={data.selectedMobile}
              convData={data}
              handleTranslationKeyDown={handleTranslationKeyDown}
              handleTranslationInputChange={handleTranslationInputChange}
            />
          </div>

          {/* Translation Error Display */}
          {translationError && (
            <div className="alert alert-warning alert-dismissible fade show mt-2" role="alert">
              <small>{translationError}</small>
              <button
                type="button"
                className="btn-close"
                onClick={() => setTranslationError('')}
                aria-label="Close"
              ></button>
            </div>
          )}

          {/* Translation Status */}
          {isTranslationEnabled && selectedLanguage && (
            <div className="d-flex justify-content-between align-items-center mt-2 px-2">
              <small className="text-muted">
                Translation enabled: {languages.find(lang => lang.code === selectedLanguage)?.name}
              </small>
              <small className="text-muted">
                Press space after each word for instant translation
              </small>
            </div>
          )}
        </>
      }
    </div>
  );
};

export default Inputbar;
