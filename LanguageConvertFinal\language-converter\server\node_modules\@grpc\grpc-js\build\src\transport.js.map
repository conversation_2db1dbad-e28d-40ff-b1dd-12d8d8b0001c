{"version": 3, "file": "transport.js", "sourceRoot": "", "sources": ["../../src/transport.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,+BAA+B;AAQ/B,yCAQoB;AACpB,2CAA2C;AAC3C,6CAAoD;AACpD,qCAAqC;AACrC,yCAAiD;AACjD,6DAI8B;AAC9B,6CAA8D;AAC9D,2BAA2B;AAC3B,uDAI2B;AAE3B,+CAAkD;AAGlD,MAAM,WAAW,GAAG,WAAW,CAAC;AAChC,MAAM,wBAAwB,GAAG,oBAAoB,CAAC;AAEtD,MAAM,aAAa,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC;AAE5D,MAAM,EACJ,sBAAsB,EACtB,yBAAyB,EACzB,mBAAmB,EACnB,iBAAiB,EACjB,eAAe,EACf,uBAAuB,GACxB,GAAG,KAAK,CAAC,SAAS,CAAC;AAEpB,MAAM,oBAAoB,GAAG,KAAK,CAAC;AA4BnC,MAAM,gBAAgB,GAAW,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;AAExE,MAAM,cAAc;IA2ClB,YACU,OAAiC,EACzC,iBAAoC,EAC5B,OAAuB;IAC/B;;;OAGG;IACK,UAAyB;QAPzB,YAAO,GAAP,OAAO,CAA0B;QAEjC,YAAO,GAAP,OAAO,CAAgB;QAKvB,eAAU,GAAV,UAAU,CAAe;QAtCnC;;WAEG;QACK,mBAAc,GAA0B,IAAI,CAAC;QACrD;;;WAGG;QACK,6BAAwB,GAAG,KAAK,CAAC;QAIjC,gBAAW,GAA6B,IAAI,GAAG,EAAE,CAAC;QAIlD,wBAAmB,GAAkC,EAAE,CAAC;QAExD,sBAAiB,GAAG,KAAK,CAAC;QAIjB,oBAAe,GAAY,IAAI,CAAC;QAEzC,mBAAc,GAAG,CAAC,CAAC;QACnB,iBAAY,GAAG,CAAC,CAAC;QACjB,qBAAgB,GAAG,CAAC,CAAC;QACrB,6BAAwB,GAAgB,IAAI,CAAC;QAC7C,iCAA4B,GAAgB,IAAI,CAAC;QAYvD;+DACuD;QACvD,IAAI,CAAC,uBAAuB,GAAG,IAAA,8CAAyB,EAAC,iBAAiB,CAAC,CAAC;QAE5E,IAAI,OAAO,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,kCAAuB,EAAE,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,GAAG,IAAI,8BAAmB,EAAE,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAA,iCAAsB,EACvC,IAAI,CAAC,uBAAuB,EAC5B,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE,EAC5B,IAAI,CAAC,eAAe,CACrB,CAAC;QAEF,2BAA2B;QAC3B,IAAI,CAAC,SAAS,GAAG;YACf,OAAO,CAAC,yBAAyB,CAAC;YAClC,gBAAgB,aAAa,EAAE;YAC/B,OAAO,CAAC,2BAA2B,CAAC;SACrC;aACE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACd,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,6BAA6B;QAE3C,IAAI,wBAAwB,IAAI,OAAO,EAAE,CAAC;YACxC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,wBAAwB,CAAE,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;QAC5B,CAAC;QACD,IAAI,2BAA2B,IAAI,OAAO,EAAE,CAAC;YAC3C,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,2BAA2B,CAAE,CAAC;QAClE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,kBAAkB,GAAG,oBAAoB,CAAC;QACjD,CAAC;QACD,IAAI,qCAAqC,IAAI,OAAO,EAAE,CAAC;YACrD,IAAI,CAAC,qBAAqB;gBACxB,OAAO,CAAC,qCAAqC,CAAC,KAAK,CAAC,CAAC;QACzD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACrC,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;YACzB,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAC7B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,IAAI,CACV,QAAQ,EACR,CAAC,SAAiB,EAAE,YAAoB,EAAE,UAAmB,EAAE,EAAE;YAC/D,IAAI,YAAY,GAAG,KAAK,CAAC;YACzB;0GAC8F;YAC9F,IACE,SAAS,KAAK,KAAK,CAAC,SAAS,CAAC,yBAAyB;gBACvD,UAAU;gBACV,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,EACnC,CAAC;gBACD,YAAY,GAAG,IAAI,CAAC;YACtB,CAAC;YACD,IAAI,CAAC,KAAK,CACR,wCAAwC;gBACtC,SAAS;gBACT,YAAY;iBACZ,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,QAAQ,EAAE,CAAA,CACzB,CAAC;YACF,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC;QAC7C,CAAC,CACF,CAAC;QAEF,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,+BAA+B,GAAI,KAAe,CAAC,OAAO,CAAC,CAAC;YACvE,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAE;YACxC,IAAI,CAAC,KAAK,CAAC,8BAA8B,GAAG,QAAQ,CAAC,CAAC;YACtD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,IAAI,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;YACzC,OAAO,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,QAAwB,EAAE,EAAE;gBACxD,IAAI,CAAC,KAAK,CACR,uBAAuB;oBACrB,CAAC,IAAI,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC1D,IAAI;oBACJ,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAC3B,CAAC;YACJ,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,QAAwB,EAAE,EAAE;gBACvD,IAAI,CAAC,KAAK,CACR,uCAAuC;oBACrC,CAAC,IAAI,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC1D,IAAI;oBACJ,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAC3B,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QAED;uEAC+D;QAC/D,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/B,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACtC,CAAC;IACH,CAAC;IAEO,eAAe;;QACrB,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAC1C,MAAM,aAAa,GAAG,aAAa,CAAC,aAAa;YAC/C,CAAC,CAAC,IAAA,8CAAyB,EACvB,aAAa,CAAC,aAAa,EAC3B,aAAa,CAAC,UAAU,CACzB;YACH,CAAC,CAAC,IAAI,CAAC;QACT,MAAM,YAAY,GAAG,aAAa,CAAC,YAAY;YAC7C,CAAC,CAAC,IAAA,8CAAyB,EACvB,aAAa,CAAC,YAAY,EAC1B,aAAa,CAAC,SAAS,CACxB;YACH,CAAC,CAAC,IAAI,CAAC;QACT,IAAI,OAAuB,CAAC;QAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3B,MAAM,SAAS,GAAc,aAA0B,CAAC;YACxD,MAAM,UAAU,GACd,SAAS,CAAC,SAAS,EAAE,CAAC;YACxB,MAAM,WAAW,GAAG,SAAS,CAAC,cAAc,EAAE,CAAC;YAC/C,MAAM,eAAe,GAAG,SAAS,CAAC,kBAAkB,EAAE,CAAC;YACvD,OAAO,GAAG;gBACR,uBAAuB,EAAE,MAAA,UAAU,CAAC,YAAY,mCAAI,IAAI;gBACxD,oBAAoB,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI;gBACtE,gBAAgB,EACd,WAAW,IAAI,KAAK,IAAI,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI;gBAC9D,iBAAiB,EACf,eAAe,IAAI,KAAK,IAAI,eAAe;oBACzC,CAAC,CAAC,eAAe,CAAC,GAAG;oBACrB,CAAC,CAAC,IAAI;aACX,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,IAAI,CAAC;QACjB,CAAC;QACD,MAAM,UAAU,GAAe;YAC7B,aAAa,EAAE,aAAa;YAC5B,YAAY,EAAE,YAAY;YAC1B,QAAQ,EAAE,OAAO;YACjB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,YAAY;YAC/C,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc;YACnD,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,WAAW;YAC7C,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,+BAA+B,EAC7B,IAAI,CAAC,aAAa,CAAC,wBAAwB;YAC7C,gCAAgC,EAAE,IAAI;YACtC,wBAAwB,EAAE,IAAI,CAAC,wBAAwB;YACvD,4BAA4B,EAAE,IAAI,CAAC,4BAA4B;YAC/D,sBAAsB,EAAE,MAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,mCAAI,IAAI;YAClE,uBAAuB,EAAE,MAAA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,gBAAgB,mCAAI,IAAI;SACrE,CAAC;QACF,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,IAAY;QACxB,OAAO,CAAC,KAAK,CACX,wBAAY,CAAC,KAAK,EAClB,WAAW,EACX,GAAG;YACD,IAAI,CAAC,WAAW,CAAC,EAAE;YACnB,IAAI;YACJ,IAAI,CAAC,uBAAuB;YAC5B,GAAG;YACH,IAAI,CACP,CAAC;IACJ,CAAC;IAEO,cAAc,CAAC,IAAY;QACjC,OAAO,CAAC,KAAK,CACX,wBAAY,CAAC,KAAK,EAClB,WAAW,EACX,GAAG;YACD,IAAI,CAAC,WAAW,CAAC,EAAE;YACnB,IAAI;YACJ,IAAI,CAAC,uBAAuB;YAC5B,GAAG;YACH,IAAI,CACP,CAAC;IACJ,CAAC;IAEO,gBAAgB,CAAC,IAAY;QACnC,OAAO,CAAC,KAAK,CACX,wBAAY,CAAC,KAAK,EAClB,wBAAwB,EACxB,GAAG;YACD,IAAI,CAAC,WAAW,CAAC,EAAE;YACnB,IAAI;YACJ,IAAI,CAAC,uBAAuB;YAC5B,GAAG;YACH,IAAI,CACP,CAAC;IACJ,CAAC;IAEO,cAAc,CAAC,IAAY;QACjC,OAAO,CAAC,KAAK,CACX,wBAAY,CAAC,KAAK,EAClB,qBAAqB,EACrB,GAAG;YACD,IAAI,CAAC,WAAW,CAAC,EAAE;YACnB,IAAI;YACJ,IAAI,CAAC,uBAAuB;YAC5B,GAAG;YACH,IAAI,CACP,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACK,uBAAuB,CAAC,YAAqB;QACnD,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,OAAO;QACT,CAAC;QACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QACpC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;QACD,4DAA4D;QAC5D,YAAY,CAAC,GAAG,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,qBAAqB,CAAC,QAAqC;QACzD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAEO,WAAW;QACjB,OAAO,CACL,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS;YACvB,IAAI,CAAC,eAAe,GAAG,CAAC;YACxB,CAAC,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC,CAC1D,CAAC;IACJ,CAAC;IAEO,aAAa;;QACnB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACxB,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;YACrC,OAAO;QACT,CAAC;QACD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,OAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAC9C,OAAO;QACT,CAAC;QACD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;QAC3B,CAAC;QACD,IAAI,CAAC,cAAc,CACjB,4BAA4B,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAC9D,CAAC;QACF,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,EAAE;YACpC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,cAAc,CAAC,sCAAsC,CAAC,CAAC;YAC5D,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC5B,MAAA,MAAA,IAAI,CAAC,cAAc,EAAC,KAAK,kDAAI,CAAC;QAC9B,IAAI,aAAa,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC;YACH,MAAM,oBAAoB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAC5C,CAAC,GAAiB,EAAE,QAAgB,EAAE,OAAe,EAAE,EAAE;gBACvD,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,IAAI,GAAG,EAAE,CAAC;oBACR,IAAI,CAAC,cAAc,CAAC,yBAAyB,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC;oBAC7D,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,cAAc,CAAC,wBAAwB,CAAC,CAAC;oBAC9C,IAAI,CAAC,4BAA4B,EAAE,CAAC;gBACtC,CAAC;YACH,CAAC,CACF,CAAC;YACF,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC1B,aAAa,GAAG,qBAAqB,CAAC;YACxC,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,sBAAsB;YACtB,aAAa,GAAG,CAAC,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,eAAe,CAAC;QAC3E,CAAC;QACD,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,CAAC,cAAc,CAAC,oBAAoB,GAAG,aAAa,CAAC,CAAC;YAC1D,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,4BAA4B;;QAClC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QACD,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAClC,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;YACtC,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YAChC,IAAI,CAAC,cAAc,CACjB,+BAA+B,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAC9D,CAAC;YACF,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,EAAE;gBACpC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YACzB,MAAA,MAAA,IAAI,CAAC,cAAc,EAAC,KAAK,kDAAI,CAAC;QAChC,CAAC;QACD;wCACgC;IAClC,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,IAAyB;QAChD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,IAAyB;QAC7C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC3B,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAChC,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACtC,CAAC;QACH,CAAC;IACH,CAAC;IAED,UAAU,CACR,QAAkB,EAClB,IAAY,EACZ,MAAc,EACd,QAA4C,EAC5C,0BAAqD;QAErD,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,EAAE,CAAC;QAC1C,OAAO,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAAC;QACvC,OAAO,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;QAClD,OAAO,CAAC,yBAAyB,CAAC,GAAG,kBAAkB,CAAC;QACxD,OAAO,CAAC,mBAAmB,CAAC,GAAG,MAAM,CAAC;QACtC,OAAO,CAAC,iBAAiB,CAAC,GAAG,MAAM,CAAC;QACpC,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,CAAC;QACtC,IAAI,WAAoC,CAAC;QACzC;;;;;;;WAOG;QACH,IAAI,CAAC;YACH,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,MAAM,CAAC,CAAC;QACV,CAAC;QACD,IAAI,CAAC,gBAAgB,CACnB,qBAAqB;YACnB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe;YAClC,uBAAuB;YACvB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,gBAAgB,CACtC,CAAC;QACF,IAAI,CAAC,cAAc,CACjB,iBAAiB;YACf,IAAI,CAAC,OAAO,CAAC,MAAM;YACnB,qBAAqB;YACrB,IAAI,CAAC,OAAO,CAAC,SAAS;YACtB,4BAA4B;YAC5B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAChC,CAAC;QACF,IAAI,YAA8B,CAAC;QACnC,wCAAwC;QACxC,IAAI,IAAyB,CAAC;QAC9B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YACpC,YAAY,GAAG;gBACb,cAAc,EAAE,GAAG,EAAE;;oBACnB,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;oBACvB,IAAI,CAAC,wBAAwB,GAAG,IAAI,IAAI,EAAE,CAAC;oBAC3C,MAAA,0BAA0B,CAAC,cAAc,0EAAI,CAAC;gBAChD,CAAC;gBACD,kBAAkB,EAAE,GAAG,EAAE;;oBACvB,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC;oBAC3B,IAAI,CAAC,4BAA4B,GAAG,IAAI,IAAI,EAAE,CAAC;oBAC/C,MAAA,0BAA0B,CAAC,kBAAkB,0EAAI,CAAC;gBACpD,CAAC;gBACD,SAAS,EAAE,MAAM,CAAC,EAAE;;oBAClB,MAAA,0BAA0B,CAAC,SAAS,2EAAG,MAAM,CAAC,CAAC;oBAC/C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBAC9B,CAAC;gBACD,WAAW,EAAE,OAAO,CAAC,EAAE;;oBACrB,IAAI,OAAO,EAAE,CAAC;wBACZ,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;oBACxC,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;oBACrC,CAAC;oBACD,MAAA,0BAA0B,CAAC,WAAW,2EAAG,OAAO,CAAC,CAAC;gBACpD,CAAC;aACF,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,YAAY,GAAG;gBACb,cAAc,EAAE,GAAG,EAAE;;oBACnB,MAAA,0BAA0B,CAAC,cAAc,0EAAI,CAAC;gBAChD,CAAC;gBACD,kBAAkB,EAAE,GAAG,EAAE;;oBACvB,MAAA,0BAA0B,CAAC,kBAAkB,0EAAI,CAAC;gBACpD,CAAC;gBACD,SAAS,EAAE,MAAM,CAAC,EAAE;;oBAClB,MAAA,0BAA0B,CAAC,SAAS,2EAAG,MAAM,CAAC,CAAC;oBAC/C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBAC9B,CAAC;gBACD,WAAW,EAAE,OAAO,CAAC,EAAE;;oBACrB,MAAA,0BAA0B,CAAC,WAAW,2EAAG,OAAO,CAAC,CAAC;gBACpD,CAAC;aACF,CAAC;QACJ,CAAC;QACD,IAAI,GAAG,IAAI,qCAAmB,CAC5B,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,IAAI,EACJ,IAAA,+BAAiB,GAAE,CACpB,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACtC,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,IAAA,gCAAqB,EAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC1C,CAAC;CACF;AAWD,MAAa,wBAAwB;IAGnC,YAAoB,aAAsB;QAAtB,kBAAa,GAAb,aAAa,CAAS;QAFlC,YAAO,GAAoC,IAAI,CAAC;QAChD,eAAU,GAAG,KAAK,CAAC;IACkB,CAAC;IAEtC,KAAK,CAAC,IAAY;QACxB,OAAO,CAAC,KAAK,CACX,wBAAY,CAAC,KAAK,EAClB,WAAW,EACX,IAAA,wBAAW,EAAC,IAAI,CAAC,aAAa,CAAC,GAAG,GAAG,GAAG,IAAI,CAC7C,CAAC;IACJ,CAAC;IAEO,aAAa,CACnB,mBAAwC,EACxC,OAA0B,EAC1B,OAAuB;QAEvB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,OAAO,OAAO,CAAC,MAAM,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,mBAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACtC,OAAO,OAAO,CAAC,MAAM,CAAC,oDAAoD,CAAC,CAAC;QAC9E,CAAC;QAED,OAAO,IAAI,OAAO,CAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrD,IAAI,UAAU,GAAkB,IAAI,CAAC;YACrC,IAAI,UAAU,GAAY,IAAI,CAAC,aAAa,CAAC;YAC7C,IAAI,0BAA0B,IAAI,OAAO,EAAE,CAAC;gBAC1C,MAAM,YAAY,GAAG,IAAA,qBAAQ,EAAC,OAAO,CAAC,0BAA0B,CAAE,CAAC,CAAC;gBACpE,IAAI,YAAY,EAAE,CAAC;oBACjB,UAAU,GAAG,YAAY,CAAC;oBAC1B,UAAU,GAAG,IAAA,wBAAW,EAAC,YAAY,CAAC,CAAC;gBACzC,CAAC;YACH,CAAC;YACD,MAAM,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YAC7D,MAAM,UAAU,GAAG,IAAA,8BAAmB,EAAC,UAAU,CAAC,CAAC;YACnD,MAAM,YAAY,GAAG,GAAG,EAAE;;gBACxB,MAAA,IAAI,CAAC,OAAO,0CAAE,OAAO,EAAE,CAAC;gBACxB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACpB,wDAAwD;gBACxD,YAAY,CAAC,GAAG,EAAE;oBAChB,IAAI,CAAC,aAAa,EAAE,CAAC;wBACnB,aAAa,GAAG,IAAI,CAAC;wBACrB,MAAM,CAAC,GAAG,YAAY,CAAC,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;oBACjE,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;YACF,MAAM,YAAY,GAAG,CAAC,KAAY,EAAE,EAAE;;gBACpC,MAAA,IAAI,CAAC,OAAO,0CAAE,OAAO,EAAE,CAAC;gBACxB,YAAY,GAAI,KAAe,CAAC,OAAO,CAAC;gBACxC,IAAI,CAAC,KAAK,CAAC,+BAA+B,GAAG,YAAY,CAAC,CAAC;gBAC3D,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,aAAa,GAAG,IAAI,CAAC;oBACrB,MAAM,CAAC,GAAG,YAAY,KAAK,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;gBAC1D,CAAC;YACH,CAAC,CAAC;YACF,MAAM,cAAc,GAA+B;gBACjD,gBAAgB,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE;oBACtC,OAAO,mBAAmB,CAAC,MAAM,CAAC;gBACpC,CAAC;aACF,CAAC;YACF,IAAI,OAAO,CAAC,+BAA+B,CAAC,KAAK,SAAS,EAAE,CAAC;gBAC3D,cAAc,CAAC,QAAQ,GAAG;oBACxB,iBAAiB,EAAE,OAAO,CAAC,+BAA+B,CAAC;iBAC5D,CAAC;YACJ,CAAC;YACD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,MAAM,MAAM,UAAU,EAAE,EAAE,cAAc,CAAC,CAAC;YAC3E,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB,IAAI,YAAY,GAAG,mBAAmB,CAAC;YACvC,IAAI,aAAa,GAAG,KAAK,CAAC;YAC1B,OAAO,CAAC,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE;gBAClC,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBAC7B,mBAAmB,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;gBACjE,mBAAmB,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;gBACjE,OAAO,CAAC,IAAI,cAAc,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;gBACnE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACtB,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACpC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACpC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACvD,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,UAAU,CAAC,OAA0B,EAAE,OAAuB;QACpE,OAAO,IAAA,iCAAoB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YACjE,IAAI,aAAa,EAAE,CAAC;gBAClB,OAAO,aAAa,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC7C,MAAM,aAAa,GAAG,GAAG,EAAE;wBACzB,MAAM,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;oBACrC,CAAC,CAAC;oBACF,MAAM,aAAa,GAAG,CAAC,KAAY,EAAE,EAAE;wBACrC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChB,CAAC,CAAA;oBACD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE;wBACvC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;wBAC9C,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;wBAC9C,OAAO,CAAC,MAAM,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC;oBACH,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;oBACpC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;gBACtC,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CACX,OAA0B,EAC1B,eAAgC,EAChC,OAAuB;QAEvB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,OAAO,OAAO,CAAC,MAAM,EAAE,CAAC;QAC1B,CAAC;QACD,IAAI,aAAa,GAAsB,IAAI,CAAC;QAC5C,IAAI,mBAAmB,GAAgC,IAAI,CAAC;QAC5D,MAAM,aAAa,GAAG,IAAA,8CAAyB,EAAC,OAAO,CAAC,CAAC;QACzD,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,0CAA0C,CAAC,CAAC;YACvE,MAAM,eAAe,CAAC,YAAY,EAAE,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,2BAA2B,CAAC,CAAC;YACxD,aAAa,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACxD,aAAa,CAAC,UAAU,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,6BAA6B,CAAC,CAAC;YAC1D,mBAAmB,GAAG,MAAM,eAAe,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YACnE,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,gCAAgC,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,EAAE,CAAC;YACzB,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,MAAM,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED,QAAQ;;QACN,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,MAAA,IAAI,CAAC,OAAO,0CAAE,KAAK,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;CACF;AAhJD,4DAgJC"}