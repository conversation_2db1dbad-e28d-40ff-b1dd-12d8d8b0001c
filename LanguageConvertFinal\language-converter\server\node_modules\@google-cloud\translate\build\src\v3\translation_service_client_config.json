{"interfaces": {"google.cloud.translation.v3.TranslationService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"TranslateText": {"timeout_millis": 600000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "RomanizeText": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DetectLanguage": {"timeout_millis": 600000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetSupportedLanguages": {"timeout_millis": 600000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "TranslateDocument": {"timeout_millis": 600000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchTranslateText": {"timeout_millis": 600000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "BatchTranslateDocument": {"timeout_millis": 600000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateGlossary": {"timeout_millis": 600000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateGlossary": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListGlossaries": {"timeout_millis": 600000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "GetGlossary": {"timeout_millis": 600000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "DeleteGlossary": {"timeout_millis": 600000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "GetGlossaryEntry": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListGlossaryEntries": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateGlossaryEntry": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateGlossaryEntry": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteGlossaryEntry": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateDataset": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetDataset": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListDatasets": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteDataset": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateAdaptiveMtDataset": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteAdaptiveMtDataset": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetAdaptiveMtDataset": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListAdaptiveMtDatasets": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "AdaptiveMtTranslate": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetAdaptiveMtFile": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteAdaptiveMtFile": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ImportAdaptiveMtFile": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListAdaptiveMtFiles": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListAdaptiveMtSentences": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ImportData": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ExportData": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListExamples": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateModel": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListModels": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetModel": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteModel": {"retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}