import LeftMenu from '../../components/LeftMenu';
import styles from './LeadsDashboard.module.css';
import { useState } from 'react';
import StageCard from './StageCard';
import { useNavigate } from 'react-router-dom';
import { ChatState } from '../../context/AllProviders';

import {
    DndContext,
    closestCenter,
    PointerSensor,
    useSensor,
    useSensors
} from '@dnd-kit/core';
import {
    arrayMove,
    SortableContext,
    horizontalListSortingStrategy,
    useSortable
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { toast } from 'react-toastify';
import { CircularProgress } from '@mui/material';
const defaultStages = [
    { id: 's1', name: 'Contacted', description: 'Initial contact with the lead' },
    { id: 's2', name: 'Qualified', description: 'Lead has been qualified' },
];



function SortableStage({ stage, onDelete, onUpdateStage, handleInsertStage }) {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition
    } = useSortable({ id: stage.id });
    
    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
    };
    
    return (
        <div ref={setNodeRef} className='h-100' style={style} {...attributes} {...listeners}>
            <StageCard
                id={stage.id}
                title={stage.name}
                description={stage.description}
                onDelete={onDelete}
                onUpdateStage={onUpdateStage}
                onInsertStage={handleInsertStage}
                />
        </div>
    );
}

const CreateNewPipeline = () => {
    const [name, setName] = useState('New pipeline');
    const [stages, setStages] = useState(defaultStages);
    const navigate = useNavigate();
    const { setPipelines } = ChatState();
    
    const sensors = useSensors(
        useSensor(PointerSensor, { activationConstraint: { distance: 5 } })
    );

    const handleDragEnd = (event) => {
        const { active, over } = event;
        if (active.id !== over?.id) {
            const oldIndex = stages.findIndex(stage => stage.id === active.id);
            const newIndex = stages.findIndex(stage => stage.id === over?.id);
            setStages(arrayMove(stages, oldIndex, newIndex));
        }

    };


    const handleAddStage = () => {
        if (stages.length >= 10) return toast.error("Maximum 10 stages allowed.");
        const newStage = {
            id: `s-${Date.now()}`,
            name: `New Stage ${stages.length + 1}`,
            description: `Description for new stage ${stages.length + 1}`,
        };
        setStages([...stages, newStage]);
    };

    const handleSave = () => {
        if (!name.trim()) {
            return toast.error("Pipeline name is required.");
          }
        
          for (let i = 0; i < stages.length; i++) {
            const stage = stages[i];
            if (!stage.name.trim()) {
              return toast.error(`Stage ${i + 1} must have a name.`);
            }
            if (!stage.description.trim()) {
              return toast.error(`Stage ${i + 1} must have a description.`);
            }
          }
        const newPipeLine = {
            id: Date.now().toString(),
            name,
            stages,
        };
        setPipelines((prev) => [...prev, newPipeLine]);
        navigate(-1);
    };

    const handleCancel = () => {
        navigate(-1);
    };

    const handleDeleteStage = (id) => {
        setStages(stages.filter(stage => stage.id !== id));
    };

    const handleUpdateStage = (id, updates) => {
        setStages(prev =>
            prev.map(stage =>
                stage.id === id ? { ...stage, ...updates } : stage
            )
        );
    };

    const handleInsertStage = (targetId, position) => {
        if (stages.length >= 10) return toast.error("Maximum 10 stages allowed.");
        const newStage = {
            id: `stage-${Date.now()}`,
            name: "New Stage",
            description: "",
        };

        const index = stages.findIndex((stage) => stage.id === targetId);
        const insertIndex = position === "left" ? index : index + 1;

        const updatedStages = [...stages];
        updatedStages.splice(insertIndex, 0, newStage);

        setStages(updatedStages);
    };


    return (
        <div className={styles.pageContainer}>
            <LeftMenu />
            <div className={`d-flex flex-column justify-content-start align-items-center ${styles.main}`}>
                <div className="d-flex w-100 justify-content-between align-items-center gap-2 px-3 mb-2">
                    <div className='d-flex align-items-center w-25'>
                        <label htmlFor="">Pipeline Name</label>
                        <input
                            type="text"
                            className="form-control ms-2"
                            placeholder="Enter name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            style={{ maxWidth: '300px' }}
                        />
                    </div>
                    <div>
                        <button className="btn btn-secondary me-3" onClick={handleCancel}>Cancel</button>
                        <button className="btn btn-primary" onClick={handleSave}>Save</button>
                    </div>
                </div>
                <hr className={styles.separator} />

                <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                    <SortableContext items={stages.map(s => s.id)} strategy={horizontalListSortingStrategy}>
                        <div className="d-flex gap-2 pb-2 px-3 mt-2 w-100 h-100" style={{ overflowX: 'auto', overflowY: "hidden" }}>
                            {stages.map((stage, index) => (
                                <div className={styles.stageWrapper} key={stage.id} style={{ position: 'relative' }}>
                                     <div
                                        className={`position-absolute d-flex justify-content-center align-items-center p-2 ${styles.addButton}`}
                                        style={{
                                            zIndex: 10,
                                            borderRadius: "50%",
                                            backgroundColor: "lightgray",
                                            width: "25px", height: "25px",
                                            left: "-1rem",
                                            top: "1rem",
                                            fontWeight: "bold"
                                        }}
                                        onClick={() => handleInsertStage(stage.id, "left")}
                                        role="button"
                                    >
                                        +
                                    </div>
                                    <SortableStage
                                        key={stage.id}
                                        stage={stage}
                                        onDelete={handleDeleteStage}
                                        onUpdateStage={handleUpdateStage}

                                    />
                                     <div
                                        className={`position-absolute d-flex justify-content-center align-items-center p-2 ${styles.addButton}`}
                                        style={{
                                            zIndex: 10,
                                            borderRadius: "50%",
                                            backgroundColor: "lightgray",
                                            width: "25px", height: "25px",
                                            right: "-1rem", top: "1rem",
                                            fontWeight: "bold"
                                        }}
                                        onClick={() => handleInsertStage(stage.id, "right")}
                                        role="button"
                                    >
                                        +
                                    </div>
                                    {/* {index === stages.length - 1 && <div
                                        className={`position-absolute d-flex justify-content-center align-items-center p-2 ${styles.addButton}`}
                                        style={{
                                            zIndex: 10,
                                            borderRadius: "50%",
                                            backgroundColor: "lightgray",
                                            width: "25px", height: "25px", right: "-1rem", top: "1rem",
                                            fontWeight: "bold"
                                        }}
                                        onClick={() => handleInsertStage(stage.id, "right")}
                                        role="button"
                                    >
                                        +
                                    </div>} */}
                                </div>
                            ))}
                            <div
                                onClick={handleAddStage}
                                style={{
                                    width: "250px",
                                    minWidth: "250px",
                                    height: "100%",
                                    background: "#fafafa",
                                    border: "2px dashed #d0d0d0",
                                    borderRadius: "10px",
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    cursor: "pointer",
                                    padding: ".5rem",
                                    marginLeft: ".5rem"
                                }}
                            >
                                <h5 style={{ fontWeight: "600" }}>Add new stage</h5>
                                <p style={{ textAlign: "center", color: "#666", marginBottom: "16px" }}>
                                    Pipeline stages represent the steps in your process
                                </p>
                                <button className="btn btn-outline-primary">+ New stage</button>
                            </div>

                        </div>
                    </SortableContext>
                </DndContext>
            </div>
        </div>
    );
};



export default CreateNewPipeline;
