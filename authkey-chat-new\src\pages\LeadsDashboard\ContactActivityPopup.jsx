
import React, { useEffect,useContext,useState } from 'react';
import ReactDOM from 'react-dom';
import { AuthContext } from '../../context/AuthContext';
import axios from 'axios';
import { BASE_URL2 } from '../../api/api';
import { CircularProgress } from '@mui/material';
const ContactActivityPopup = ({ anchorRef,phone, activities,setActivities, onAddActivity, onClose }) => {
  const { currentUser } = useContext(AuthContext);
  const [loading,setLoading]=useState(false);
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (anchorRef.current && !anchorRef.current.contains(e.target)) {
        onClose();
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [anchorRef, onClose]);

  useEffect(()=>{
    if(!currentUser.parent_id && !currentUser.parent_token) return;

    const fetchReminders= async()=>{
      const payload={
        user_id: currentUser.parent_id,
        token: currentUser.parent_token,
        method: "retrieve",
        brand_number: currentUser.brand_number,
        mobile: phone,
      }
      setLoading(true)
      try {
        const{data}= await axios.post(`${BASE_URL2}/whatsapp_reminder`,payload)
        if(data.success){
          setActivities(data.data)
        }else{
          setActivities([])
        }
        
      } catch (error) {
        console.error("Error fetching reminders:", error);
      }
      finally{
        setLoading(false);
      }
    }

    fetchReminders();

  },[currentUser,phone])


  if (!anchorRef.current) return null;

  const rect = anchorRef.current.getBoundingClientRect();

  const style = {
    position: 'fixed',
    top: rect.top + window.scrollY + 20,
    left: rect.right + 5,
    width: '250px',
    background: '#fff',
    boxShadow: '0 0 10px rgba(0,0,0,0.2)',
    borderRadius: '8px',
    padding: '10px',
    zIndex: 9999,
  };


  return ReactDOM.createPortal(
    <div style={style}>
     {loading?
     <div className='w-100 text-center'><CircularProgress/></div>: 
     <div style={{
        maxHeight:"150px",
        overflowY:"auto",
      }}>
        {activities.length > 0 ? (
          activities.map((activity) => (
            <div key={activity._id} style={{ marginBottom: '10px' }}>
              <p style={{ margin: '0', fontWeight: 'bold' }}>{activity.text}</p>
              <p style={{ margin: '0', color: 'red', fontSize: '12px' }}>
                {new Date(activity.date) < new Date() ? 'Overdue' : 'Scheduled'} - {activity.date}
              </p>
            </div>
          ))
        ) : (
          <p>No activities scheduled.</p>
        )}

      </div>}
      {/* <button
        onClick={onAddActivity}
        style={{
          marginTop: '10px',
          width: '100%',
          background: '#f0f0f0',
          border: 'none',
          padding: '8px',
          borderRadius: '5px',
          cursor: 'pointer',
        }}
      >
        + Schedule an activity
      </button> */}
    </div>,
    document.body
  );
};

export default ContactActivityPopup;
